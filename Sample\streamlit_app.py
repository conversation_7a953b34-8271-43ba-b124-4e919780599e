import streamlit as st
import pandas as pd
import time
from collections import Counter
import concurrent.futures
import os
from streamlit_markmap import markmap

# 리팩토링된 모듈들 import
from config import (
    COLUMN_RENAME_MAP,
    CACHE_TTL_SHORT,
    CACHE_TTL_LONG,
    DEFAULT_MAX_EVENTS,
    LARGE_DATA_THRESHOLD
)
from s3_utils import list_log_files, download_and_decompress
from log_parser import (
    parse_log,
    parse_log_extended,
    parse_log_fast_bookmark_combinations,
    parse_log_fast_max_round,
    parse_log_fast_bookmark_joker_usage,
    extract_game_info_summary,
    parse_log_fast_joker_in_out_stats,
    parse_log_fast_item_scroll_usage,
    extract_last_monster_id,
    parse_log_shop_statistics,
    get_shop_statistics_summary
)
from ui_components import (
    setup_page_config,
    display_header,
    create_file_selector,
    create_analysis_tabs,
    create_mindmap_sidebar,
    display_data_summary,
    display_loading_progress,
    display_error_message,
    display_success_message,
    display_processing_time
)
from mindmap_utils import convert_log_to_markdown_extended
from data_utils import organize_files_by_date, is_large_dataset
from korean_names import (
    get_bookmark_name,
    get_joker_name,
    get_playing_card_name,
    get_card_label_name,
    get_scroll_item_name,
    get_consume_item_name,
    get_item_name_by_type,
    get_shop_item_type_korean
)

# 캐시 설정
@st.cache_data(ttl=300)  # 5분으로 단축
def get_log_files(refresh_timestamp=None):
    """로그 파일 목록을 가져오고 캐싱합니다."""
    from config import S3_PREFIX
    from s3_utils import list_log_files_no_cache

    # refresh_timestamp가 있으면 캐시 없이 새로 가져오기
    if refresh_timestamp is not None:
        return list_log_files_no_cache(S3_PREFIX)

    # 일반적인 경우 캐시된 버전 사용
    return list_log_files(S3_PREFIX)

@st.cache_data(ttl=3600)
def get_organized_files(files, log_path_filter="All"):
    """날짜별로 파일을 구조화하고 캐싱합니다."""
    return organize_files_by_date(files, log_path_filter)

@st.cache_data(ttl=3600)
def get_parsed_log(file_path, max_events=10000):
    """로그 파일을 파싱하고 캐싱합니다. 대용량 파일의 경우 이벤트 수를 제한합니다."""
    pb_path = download_and_decompress(file_path)
    df = parse_log(pb_path, max_events=max_events)
    return df.rename(columns={k: v for k, v in COLUMN_RENAME_MAP.items() if k in df.columns})

@st.cache_data(ttl=3600)
def get_game_info_summary(file_path):
    """게임 정보 요약을 가져오고 캐싱합니다."""
    pb_path = download_and_decompress(file_path)
    return extract_game_info_summary(pb_path)

@st.cache_data(ttl=3600)
def get_last_monster_id(file_path):
    """마지막 몬스터 ID를 가져오고 캐싱합니다."""
    pb_path = download_and_decompress(file_path)
    return extract_last_monster_id(pb_path)

@st.cache_data(ttl=3600)
def get_parsed_log_extended(file_path, max_events=1000):
    """확장된 로그 파일을 파싱하고 캐싱합니다. 모든 항목을 포함합니다."""
    pb_path = download_and_decompress(file_path)
    df = parse_log_extended(pb_path, max_events=max_events)
    return df.rename(columns={k: v for k, v in COLUMN_RENAME_MAP.items() if k in df.columns})

def process_log_file(key):
    """병렬 처리를 위한 로그 파일 처리 함수"""
    try:
        pb_path = download_and_decompress(key)
        df = parse_log(pb_path)
        return df
    except Exception:
        return None

def collect_files(date_map, date_keys, include_chapter=True, include_rank=True, status=None, log_type="All"):
    """필터 조건에 맞는 파일 목록을 수집합니다."""
    results = []
    for date_key in date_keys:
        if date_key not in date_map:
            continue

        if include_chapter and "Chapter" in date_map[date_key]:
            if status == "ALL" or log_type == "All":
                for s in date_map[date_key]["Chapter"]:
                    results.extend(date_map[date_key]["Chapter"][s])
            elif status and status in date_map[date_key]["Chapter"]:
                results.extend(date_map[date_key]["Chapter"][status])

        if include_rank and "Rank" in date_map[date_key]:
            if "ALL" in date_map[date_key]["Rank"]:
                results.extend(date_map[date_key]["Rank"]["ALL"])
    return results

def main():
    st.set_page_config(layout="wide")
    st.title("📘 Battle Log Viewer")

    # 사이드바 필터 UI
    st.sidebar.markdown("### 필터")

    # 로그 목록 새로고침 버튼
    if st.sidebar.button("🔄 로그 목록 새로고침"):
        with st.spinner("🔄 캐시를 클리어하고 최신 로그 목록을 불러오는 중..."):
            try:
                # 1. Streamlit 캐시 클리어
                st.cache_data.clear()

                # 2. S3 유틸리티 LRU 캐시 클리어
                from s3_utils import clear_cache as clear_s3_cache
                clear_s3_cache()

                # 3. 한국어 이름 매핑 캐시 클리어
                from korean_names import clear_cache as clear_korean_cache
                clear_korean_cache()

                # 4. 세션 상태에서 파일 관련 캐시 제거 (있다면)
                cache_keys_to_remove = [key for key in st.session_state.keys()
                                      if any(cache_word in key.lower() for cache_word in ['file', 'log', 'cache'])]
                for key in cache_keys_to_remove:
                    del st.session_state[key]

                # 5. 강제 새로고침을 위한 타임스탬프 설정
                import time
                current_timestamp = int(time.time())
                st.session_state['force_refresh_timestamp'] = current_timestamp

                st.success(f"✅ 모든 캐시가 성공적으로 클리어되었습니다! (타임스탬프: {current_timestamp})")

            except Exception as e:
                st.error(f"❌ 캐시 클리어 중 오류 발생: {e}")

        # 페이지 새로고침
        st.rerun()

    # 로그 경로 선택
    log_path_filter = st.sidebar.radio(
        "🗂️ 로그 경로",
        ["All", "Live", "Dev"],
        help="All: BattleLog + BattleLog_dev, Live: BattleLog만, Dev: BattleLog_dev만"
    )

    # 파일 목록 가져오기 및 구조화
    # 강제 새로고침 타임스탬프가 있으면 사용
    refresh_timestamp = st.session_state.get('force_refresh_timestamp', None)
    files = get_log_files(refresh_timestamp)

    # 새로고침 후 타임스탬프 초기화 (한 번만 사용)
    if refresh_timestamp is not None:
        del st.session_state['force_refresh_timestamp']

    date_map = get_organized_files(files, log_path_filter)

    date_options = ["ALL_DATES"] + sorted(date_map.keys(), reverse=True)
    selected_date = st.sidebar.selectbox("📅 날짜 선택", date_options)
    log_type = st.sidebar.radio("🗂️ 로그 유형", ["All", "Chapter", "Rank"])

    # 상태 필터가 필요한 경우만 표시
    status = None
    if log_type == "Chapter":
        status = st.sidebar.radio("✅ 결과 상태", ["ALL", "SUCCESS", "FAIL"], key=f"status_radio_{selected_date}_{log_type}")

    # 파일 필터링
    if selected_date == "ALL_DATES":
        filtered_files = collect_files(date_map, date_map.keys(),
                                      include_chapter=(log_type in ["All", "Chapter"]),
                                      include_rank=(log_type in ["All", "Rank"]),
                                      status=status, log_type=log_type)
    elif selected_date in date_map:
        filtered_files = collect_files(date_map, [selected_date],
                                      include_chapter=(log_type in ["All", "Chapter"]),
                                      include_rank=(log_type in ["All", "Rank"]),
                                      status=status, log_type=log_type)
    else:
        filtered_files = []

    # 보기 모드 선택
    view_mode = st.sidebar.radio("📊 보기 모드", ["로그 뷰", "Mind Map 뷰", "책갈피 조합", "랭킹전 도달 라운드", "챕터 실패 구간", "책갈피&조커 사용 통계", "상점 통계", "아이템&스크롤 사용 통계"])

    # 선택된 보기 모드에 따라 UI 표시
    if view_mode == "로그 뷰":
        show_log_viewer(filtered_files)
    elif view_mode == "Mind Map 뷰":
        show_mindmap_viewer(filtered_files)
    elif view_mode == "책갈피 조합":
        show_bookmark_stats(filtered_files)
    elif view_mode == "랭킹전 도달 라운드":
        show_round_index_stats(filtered_files)
    elif view_mode == "챕터 실패 구간":
        show_fail_chapter_stats(filtered_files)
    elif view_mode == "책갈피&조커 사용 통계":
        show_bookmark_usage_stats(filtered_files)
    elif view_mode == "상점 통계":
        show_shop_stats(filtered_files, log_type)
    elif view_mode == "아이템&스크롤 사용 통계":
        show_item_scroll_usage_stats(filtered_files)

def show_log_viewer(filtered_files):
    """로그 뷰어 모드 UI"""
    selected_log = st.selectbox("📄 로그 파일 선택", filtered_files)

    # 성능 최적화 옵션
    col1, col2 = st.columns([3, 1])
    with col1:
        st.info(f"총 {len(filtered_files)}개의 로그 파일 중 선택된 파일: {selected_log}")
    with col2:
        max_events = st.selectbox("📊 최대 이벤트 수", [1000, 5000, 10000, 50000, None],
                                 index=2, help="대용량 파일의 경우 이벤트 수를 제한하여 빠른 로딩")

    if not selected_log:
        st.stop()

    # 로딩 시간 측정
    import time
    start_time = time.time()

    with st.spinner("로그 파일을 파싱하는 중..."):
        # 게임 정보 요약 추출 (캐싱됨)
        game_info = get_game_info_summary(selected_log)

        # 랭킹전 로그인 경우 마지막 몬스터 ID 추출
        last_monster_id = None
        if "Rank" in selected_log:
            last_monster_id = get_last_monster_id(selected_log)

        # 데이터프레임 파싱 (캐싱됨)
        df = get_parsed_log(selected_log, max_events=max_events)

    load_time = time.time() - start_time
    st.success(f"✅ 파싱 완료! ({len(df):,}개 이벤트, {load_time:.2f}초)")

    # 게임 정보 상단 표시
    if any(v is not None for v in game_info.values()):
        st.subheader("🎮 게임 정보")
        col1, col2, col3 = st.columns(3)

        with col1:
            user_id = game_info["userID"]
            if user_id is not None and str(user_id).strip():
                st.metric("👤 User ID", user_id)
            else:
                st.metric("👤 User ID", "정보 없음")

        with col2:
            user_level = game_info["userLevel"]
            if user_level is not None and str(user_level).strip():
                st.metric("📊 User Level", user_level)
            else:
                st.metric("📊 User Level", "정보 없음")

        with col3:
            seed = game_info["seed"]
            if seed is not None and str(seed).strip():
                st.metric("🌱 Seed", seed)
            else:
                st.metric("🌱 Seed", "정보 없음")

        st.divider()

    # 데이터프레임 표시용 복사본 생성
    display_df = df.copy()

    # 책갈피와 조커 컬럼을 한국어 이름으로 변환
    if "bookmarkDataIndex" in display_df.columns:
        display_df["책갈피"] = display_df["bookmarkDataIndex"].apply(
            lambda x: get_bookmark_name(int(x)) if pd.notna(x) and str(x).isdigit() and int(x) > 0 else ""
        )
    elif "책갈피 IDX" in display_df.columns:
        display_df["책갈피"] = display_df["책갈피 IDX"].apply(
            lambda x: get_bookmark_name(int(x)) if pd.notna(x) and str(x).isdigit() and int(x) > 0 else ""
        )

    if "jokerDataIndex" in display_df.columns:
        display_df["조커"] = display_df["jokerDataIndex"].apply(
            lambda x: get_joker_name(int(x)) if pd.notna(x) and str(x).isdigit() and int(x) > 0 else ""
        )
    elif "조커 IDX" in display_df.columns:
        display_df["조커"] = display_df["조커 IDX"].apply(
            lambda x: get_joker_name(int(x)) if pd.notna(x) and str(x).isdigit() and int(x) > 0 else ""
        )

    # 카드 인덱스 한글화
    if "cardDataIndex" in display_df.columns:
        display_df["카드"] = display_df["cardDataIndex"].apply(
            lambda x: get_playing_card_name(int(x)) if pd.notna(x) and str(x).isdigit() and int(x) > 0 else ""
        )

    # 아이템 인덱스 한글화 (itemIndex 컬럼이 있는 경우)
    if "itemIndex" in display_df.columns:
        display_df["아이템"] = display_df["itemIndex"].apply(
            lambda x: get_item_name_by_type("UNKNOWN", int(x)) if pd.notna(x) and str(x).isdigit() and int(x) > 0 else ""
        )

    # 소모 아이템 인덱스 한글화
    if "consumeItemDataIndex" in display_df.columns:
        display_df["소모아이템"] = display_df["consumeItemDataIndex"].apply(
            lambda x: get_consume_item_name(int(x)) if pd.notna(x) and str(x).isdigit() and int(x) > 0 else ""
        )

    # 스크롤 아이템 인덱스 한글화
    if "scrollItemDataIndex" in display_df.columns:
        display_df["스크롤아이템"] = display_df["scrollItemDataIndex"].apply(
            lambda x: get_scroll_item_name(int(x)) if pd.notna(x) and str(x).isdigit() and int(x) > 0 else ""
        )

    # 실제 존재하는 컬럼만 표시
    available_columns = []
    desired_columns = [
        "라운드", "행동 순서", "로그 순서",
        "이벤트 타입", "책갈피", "조커", "카드", "아이템", "소모아이템", "스크롤아이템",
        "몬스터 체력", "몬스터 최대 체력", "배수", "공격력"
    ]

    for col in desired_columns:
        if col in display_df.columns:
            available_columns.append(col)

    if "평가 결과" in display_df.columns:
        available_columns.append("평가 결과")

    # 컬럼이 없는 경우 전체 컬럼 표시
    if not available_columns:
        available_columns = list(display_df.columns)
        st.warning("⚠️ 예상된 컬럼을 찾을 수 없어 전체 컬럼을 표시합니다.")
        st.info(f"사용 가능한 컬럼: {list(display_df.columns)}")

    st.dataframe(display_df[available_columns], use_container_width=True, height=700)

    # 랭킹전 로그인 경우 마지막 몬스터 ID 표시
    if last_monster_id is not None:
        st.subheader("🐉 랭킹전 정보")
        st.metric("마지막 몬스터 ID", last_monster_id)
        st.divider()

    if "평가 결과" in df.columns and not df["평가 결과"].dropna().empty:
        with st.expander("📦 Evaluate Result 상세 보기"):
            eval_results = df["평가 결과"].dropna()
            total_count = len(eval_results)

            if total_count > 100:
                st.info(f"📊 총 {total_count:,}개의 평가 결과가 있습니다.")

                # 표시 옵션 선택
                col1, col2 = st.columns([2, 1])
                with col1:
                    display_option = st.radio(
                        "표시 방식 선택:",
                        ["처음 100개만", "개수 직접 선택", "전체 보기"],
                        horizontal=True
                    )

                with col2:
                    if display_option == "개수 직접 선택":
                        max_display = st.number_input(
                            "표시할 개수:",
                            min_value=1,
                            max_value=total_count,
                            value=min(500, total_count),
                            step=100
                        )
                    else:
                        max_display = None

                # 선택에 따른 처리
                if display_option == "처음 100개만":
                    eval_results = eval_results.head(100)
                    st.success(f"✅ 처음 100개 평가 결과를 표시합니다.")
                elif display_option == "개수 직접 선택":
                    eval_results = eval_results.head(max_display)
                    st.success(f"✅ 처음 {len(eval_results):,}개 평가 결과를 표시합니다.")
                elif display_option == "전체 보기":
                    if total_count > 1000:
                        st.warning(f"⚠️ {total_count:,}개의 결과를 모두 표시하면 로딩이 오래 걸릴 수 있습니다.")
                        if not st.checkbox("정말로 전체를 표시하시겠습니까?"):
                            eval_results = eval_results.head(100)
                            st.info("기본값인 100개만 표시합니다.")

                    if len(eval_results) == total_count:
                        st.success(f"✅ 전체 {total_count:,}개 평가 결과를 표시합니다.")
            else:
                st.success(f"✅ 총 {total_count}개의 평가 결과를 표시합니다.")

            # 결과 표시
            for i, eval_row in eval_results.items():
                round_val = df.at[i, "라운드"] if "라운드" in df.columns else "-"
                order_val = df.at[i, "행동 순서"] if "행동 순서" in df.columns else "-"
                event_val = df.at[i, "로그 순서"] if "로그 순서" in df.columns else "-"
                st.markdown(f"**{round_val}라운드 {order_val}번 행동 {event_val}번 로그**")
                st.json(eval_row)

def show_bookmark_stats(filtered_files):
    """북마크 통계 모드 UI"""
    st.subheader("📈 전체 로그의 책갈피 4종 조합 통계")

    # 병렬 처리를 위한 설정
    max_workers = min(8, os.cpu_count() or 4)
    all_combs = []
    progress_bar = st.progress(0)

    with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
        future_to_key = {executor.submit(process_bookmark_combinations, key): key for key in filtered_files}

        for i, future in enumerate(concurrent.futures.as_completed(future_to_key)):
            key = future_to_key[future]
            try:
                result = future.result()
                if result:
                    all_combs.extend(result)
            except Exception as e:
                st.warning(f"⚠️ {key} 처리 실패: {e}")
            progress_bar.progress((i + 1) / len(filtered_files))

    stats = Counter(all_combs)

    # 한국어 이름으로 변환
    korean_stats = []
    for combination, count in stats.items():
        # 조합 문자열을 인덱스로 분리
        indices = combination.split(',')
        korean_names = []
        for idx_str in indices:
            try:
                idx = int(idx_str.strip())
                korean_names.append(get_bookmark_name(idx))
            except ValueError:
                korean_names.append(idx_str.strip())

        korean_combination = ', '.join(korean_names)
        korean_stats.append({"조합": korean_combination, "횟수": count})

    stat_df = pd.DataFrame(korean_stats).sort_values("횟수", ascending=False)
    st.dataframe(stat_df, use_container_width=True, height=700)
    st.bar_chart(stat_df.set_index("조합").head(20))  # 상위 20개만 차트로 표시

def process_bookmark_combinations(key):
    """북마크 조합을 추출하는 최적화된 함수 - 각 로그에서 첫 번째 4개 조합만 추출"""
    try:
        pb_path = download_and_decompress(key)
        # 최적화된 빠른 파싱 함수 사용
        combinations = parse_log_fast_bookmark_combinations(pb_path)
        return combinations
    except Exception:
        return []

def show_round_index_stats(filtered_files):
    """라운드 인덱스 통계 모드 UI"""
    st.subheader("📊 랭킹전 최고 도달 라운드 통계")

    max_workers = min(8, os.cpu_count() or 4)
    round_max_list = []
    progress_bar = st.progress(0)

    with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
        future_to_key = {executor.submit(get_max_round, key): key for key in filtered_files}

        for i, future in enumerate(concurrent.futures.as_completed(future_to_key)):
            try:
                max_round = future.result()
                if max_round is not None:
                    round_max_list.append(max_round)
            except Exception:
                pass
            progress_bar.progress((i + 1) / len(filtered_files))

    round_stats = Counter(round_max_list)
    round_df = pd.DataFrame(round_stats.items(), columns=["최종 라운드", "횟수"]).sort_values("최종 라운드")
    st.dataframe(round_df, use_container_width=True, height=600)
    st.bar_chart(round_df.set_index("최종 라운드"))

def get_max_round(key):
    """로그에서 최대 라운드 값을 추출하는 최적화된 함수"""
    try:
        pb_path = download_and_decompress(key)
        # 최적화된 빠른 파싱 함수 사용
        max_round = parse_log_fast_max_round(pb_path)
        return max_round if max_round > 0 else None
    except Exception:
        pass
    return None

def show_fail_chapter_stats(filtered_files):
    """실패한 챕터 통계 모드 UI"""
    st.markdown("### ❌ 챕터 실패 구간 통계")

    # 병렬 처리를 위한 설정
    max_workers = min(8, os.cpu_count() or 4)
    chapter_fail_counter = {}

    progress_text = "로그 분석 중..."
    progress_bar = st.progress(0, text=progress_text)

    # 실패 로그만 필터링
    fail_files = [path for path in filtered_files if "Chapter" in path.split("/") and "FAIL" in path.split("/")]

    with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
        future_to_path = {executor.submit(process_fail_log, path): path for path in fail_files}

        for i, future in enumerate(concurrent.futures.as_completed(future_to_path)):
            path = future_to_path[future]
            try:
                chapter_id, _ = future.result()
                if chapter_id:
                    chapter_fail_counter[chapter_id] = chapter_fail_counter.get(chapter_id, 0) + 1
            except Exception as e:
                st.warning(f"⚠️ {path} 파싱 실패: {e}")
            progress_bar.progress((i + 1) / len(fail_files), text=progress_text)

    progress_bar.empty()

    if chapter_fail_counter:
        fail_df = pd.DataFrame(sorted(chapter_fail_counter.items(), key=lambda x: x[1], reverse=True), columns=["Chapter ID", "Fail Count"])
        st.dataframe(fail_df, use_container_width=True)

def process_fail_log(path):
    """실패 로그를 처리하는 함수"""
    chapter_id = None
    max_round = None

    parts = path.replace("\\", "/").split("/")
    for p in parts:
        if "-" in p:
            chapter_id = p.split("-")[-1]
            break

    try:
        pb_path = download_and_decompress(path)
        df = parse_log(pb_path)
        df = df.rename(columns={k: v for k, v in COLUMN_RENAME_MAP.items() if k in df.columns})
        if "라운드" in df:
            max_round = df["라운드"].max()
    except Exception:
        pass

    return chapter_id, max_round

def show_bookmark_usage_stats(filtered_files):
    """북마크 및 조커 사용 통계 모드 UI"""
    st.subheader("📊 전체 로그의 책갈피 & 조커 사용 통계")

    max_workers = min(8, os.cpu_count() or 4)
    bookmark_counts = Counter()
    joker_counts = Counter()
    joker_in_counts = Counter()
    joker_out_counts = Counter()
    joker_destroy_counts = Counter()

    progress_bar = st.progress(0)

    with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
        # 기존 사용 통계와 In/Out 통계를 병렬로 처리
        usage_futures = {executor.submit(process_bookmark_joker_usage, key): key for key in filtered_files}
        inout_futures = {executor.submit(process_joker_in_out_stats, key): key for key in filtered_files}

        # 사용 통계 처리
        for i, future in enumerate(concurrent.futures.as_completed(usage_futures)):
            try:
                bookmark_set, joker_set = future.result()
                bookmark_counts.update(bookmark_set)
                joker_counts.update(joker_set)
            except Exception:
                pass
            progress_bar.progress((i + 1) / (len(filtered_files) * 2))

        # In/Out 통계 처리
        for i, future in enumerate(concurrent.futures.as_completed(inout_futures)):
            try:
                in_counts, out_counts, destroy_counts = future.result()
                joker_in_counts.update(in_counts)
                joker_out_counts.update(out_counts)
                joker_destroy_counts.update(destroy_counts)
            except Exception:
                pass
            progress_bar.progress((len(filtered_files) + i + 1) / (len(filtered_files) * 2))

    # 북마크 통계 표시
    st.subheader("📙 책갈피 사용 통계")
    if bookmark_counts:
        # 한국어 이름으로 변환
        bookmark_data = []
        for idx, count in bookmark_counts.items():
            # 인덱스를 정수로 변환
            idx_int = int(idx) if str(idx).isdigit() else idx
            bookmark_data.append({
                "책갈피": get_bookmark_name(idx_int),
                "횟수": count
            })
        bookmark_df = pd.DataFrame(bookmark_data).sort_values("횟수", ascending=False)
        st.dataframe(bookmark_df, use_container_width=True, height=350)
        st.bar_chart(bookmark_df.set_index("책갈피").head(20))  # 상위 20개만 차트로 표시
    else:
        st.info("책갈피 사용 데이터가 없습니다.")

    # 조커 통계 표시 (사용 빈도 + In/Out 통합)
    st.subheader("🃏 조커 사용 통계")

    # 모든 조커 인덱스 수집 (사용 빈도 + In/Out/파괴)
    all_joker_indices = set(joker_counts.keys()) | set(joker_in_counts.keys()) | set(joker_out_counts.keys()) | set(joker_destroy_counts.keys())

    if all_joker_indices:
        # 통합 통계 데이터프레임 생성
        joker_data = []
        for joker_idx in sorted(all_joker_indices, key=lambda x: int(x) if x.isdigit() else float('inf')):
            usage_count = joker_counts.get(joker_idx, 0)
            in_count = joker_in_counts.get(joker_idx, 0)
            out_count = joker_out_counts.get(joker_idx, 0)
            destroy_count = joker_destroy_counts.get(joker_idx, 0)
            joker_data.append({
                "조커": get_joker_name(int(joker_idx)) if joker_idx.isdigit() else f"조커 {joker_idx}",
                "사용 횟수": usage_count,
                "In 횟수": in_count,
                "Out 횟수": out_count,
                "파괴 횟수": destroy_count
            })

        joker_df = pd.DataFrame(joker_data).sort_values("사용 횟수", ascending=False)
        st.dataframe(joker_df, use_container_width=True, height=400)

        # 차트 표시 (4개 컬럼)
        col1, col2, col3, col4 = st.columns(4)

        with col1:
            st.subheader("📊 사용 횟수")
            if joker_counts:
                usage_df = joker_df[joker_df["사용 횟수"] > 0].head(15)
                st.bar_chart(usage_df.set_index("조커")["사용 횟수"])
            else:
                st.info("사용 데이터가 없습니다.")

        with col2:
            st.subheader("📈 In 횟수")
            if joker_in_counts:
                in_df = joker_df[joker_df["In 횟수"] > 0].sort_values("In 횟수", ascending=False).head(15)
                st.bar_chart(in_df.set_index("조커")["In 횟수"])
            else:
                st.info("In 이벤트가 없습니다.")

        with col3:
            st.subheader("📉 Out 횟수")
            if joker_out_counts:
                out_df = joker_df[joker_df["Out 횟수"] > 0].sort_values("Out 횟수", ascending=False).head(15)
                st.bar_chart(out_df.set_index("조커")["Out 횟수"])
            else:
                st.info("Out 이벤트가 없습니다.")

        with col4:
            st.subheader("💥 파괴 횟수")
            if joker_destroy_counts:
                destroy_df = joker_df[joker_df["파괴 횟수"] > 0].sort_values("파괴 횟수", ascending=False).head(15)
                st.bar_chart(destroy_df.set_index("조커")["파괴 횟수"])
            else:
                st.info("파괴 이벤트가 없습니다.")
    else:
        st.info("조커 데이터가 없습니다.")

def process_bookmark_joker_usage(key):
    """북마크와 조커 사용 통계를 추출하는 최적화된 함수"""
    try:
        pb_path = download_and_decompress(key)
        # 최적화된 빠른 파싱 함수 사용
        bookmark_set, joker_set = parse_log_fast_bookmark_joker_usage(pb_path)
        return bookmark_set, joker_set
    except Exception:
        return set(), set()

def process_joker_in_out_stats(key):
    """조커 In/Out/파괴 통계를 추출하는 최적화된 함수"""
    try:
        pb_path = download_and_decompress(key)
        # 최적화된 빠른 파싱 함수 사용
        joker_in_counts, joker_out_counts, joker_destroy_counts = parse_log_fast_joker_in_out_stats(pb_path)
        return joker_in_counts, joker_out_counts, joker_destroy_counts
    except Exception:
        return {}, {}, {}

def show_item_scroll_usage_stats(filtered_files):
    """아이템 및 스크롤 사용 통계 모드 UI"""
    st.subheader("📦 전체 로그의 아이템 & 스크롤 사용 통계")

    max_workers = min(8, os.cpu_count() or 4)
    consume_item_counts = Counter()
    scroll_item_counts = Counter()

    progress_bar = st.progress(0)

    with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
        future_to_key = {executor.submit(process_item_scroll_usage, key): key for key in filtered_files}

        for i, future in enumerate(concurrent.futures.as_completed(future_to_key)):
            key = future_to_key[future]
            try:
                consume_items, scroll_items = future.result()
                consume_item_counts.update(consume_items)
                scroll_item_counts.update(scroll_items)
            except Exception as e:
                st.warning(f"⚠️ {key} 처리 실패: {e}")
            progress_bar.progress((i + 1) / len(filtered_files))

    progress_bar.empty()

    # 소모 아이템 통계 표시
    st.subheader("🧪 소모 아이템 사용 통계")
    if consume_item_counts:
        # 한국어 이름으로 변환 (인덱스 0 필터링)
        consume_item_data = []
        for idx, count in consume_item_counts.items():
            # 인덱스를 정수로 변환
            idx_int = int(idx) if str(idx).isdigit() else idx
            # 인덱스가 0인 데이터는 제외
            if isinstance(idx_int, int) and idx_int > 0:
                consume_item_data.append({
                    "소모 아이템": get_consume_item_name(idx_int),
                    "횟수": count
                })

        if consume_item_data:
            consume_item_df = pd.DataFrame(consume_item_data).sort_values("횟수", ascending=False)
            st.dataframe(consume_item_df, use_container_width=True, height=350)
            st.bar_chart(consume_item_df.set_index("소모 아이템").head(20))  # 상위 20개만 차트로 표시
        else:
            st.info("유효한 소모 아이템 사용 데이터가 없습니다.")
    else:
        st.info("소모 아이템 사용 데이터가 없습니다.")

    # 스크롤 아이템 통계 표시
    st.subheader("📜 스크롤 아이템 사용 통계")
    if scroll_item_counts:
        # 한국어 이름으로 변환 (인덱스 0 필터링)
        scroll_item_data = []
        for idx, count in scroll_item_counts.items():
            # 인덱스를 정수로 변환
            idx_int = int(idx) if str(idx).isdigit() else idx
            # 인덱스가 0인 데이터는 제외
            if isinstance(idx_int, int) and idx_int > 0:
                scroll_item_data.append({
                    "스크롤 아이템": get_scroll_item_name(idx_int),
                    "횟수": count
                })

        if scroll_item_data:
            scroll_item_df = pd.DataFrame(scroll_item_data).sort_values("횟수", ascending=False)
            st.dataframe(scroll_item_df, use_container_width=True, height=350)
            st.bar_chart(scroll_item_df.set_index("스크롤 아이템").head(20))  # 상위 20개만 차트로 표시
        else:
            st.info("유효한 스크롤 아이템 사용 데이터가 없습니다.")
    else:
        st.info("스크롤 아이템 사용 데이터가 없습니다.")

def process_item_scroll_usage(key):
    """아이템과 스크롤 사용 통계를 추출하는 최적화된 함수"""
    try:
        pb_path = download_and_decompress(key)
        # 최적화된 빠른 파싱 함수 사용
        consume_item_set, scroll_item_set = parse_log_fast_item_scroll_usage(pb_path)
        return consume_item_set, scroll_item_set
    except Exception:
        return set(), set()

def add_markmap_frontmatter(markdown_content, expand_mode="접힌 상태"):
    """마크다운에 markmap frontmatter를 추가하여 접힌 상태로 시작하도록 설정"""
    # 표시 모드를 숫자로 변환
    if expand_mode == "접힌 상태":
        initial_expand_level = 2  # 모두 접힌 상태
    elif expand_mode == "기본 정보":
        initial_expand_level = 2  # 2레벨까지 펼침
    elif expand_mode == "전체 상세":
        initial_expand_level = -1  # 모든 레벨 펼침
    else:
        initial_expand_level = 0  # 기본값: 접힌 상태

    frontmatter = f"""---
markmap:
  initialExpandLevel: {initial_expand_level}
  zoom: true
  pan: true
  duration: 500
---

"""

    return frontmatter + markdown_content

def get_scalar_icon(field_name):
    """스칼라 필드명에 따른 아이콘 반환"""
    icon_map = {
        'intValue': '🔢',
        'ulongValue': '🔢',
        'boolValue': '✅',
        'stringValue': '📝',
        'floatValue': '🔢',
        'doubleValue': '🔢',
        'overKillType': '💥',
        'abilityType': '🎪',
        'pokerHandsType': '🃏',
        'soundType': '🔊',
        'missionRewardType': '🎁',
        'cardIndices': '🎯'
    }
    return icon_map.get(field_name, '📊')

def get_card_field_icon(field_name):
    """카드 필드명에 따른 아이콘 반환"""
    icon_map = {
        'cardDataIndex': '🆔',
        'chip': '💎',
        'isBack': '🔄',
        'suit': '♠️',
        'rank': '🔢'
    }
    return icon_map.get(field_name, '🃏')

def get_product_field_icon(field_name):
    """상품 필드명에 따른 아이콘 반환"""
    icon_map = {
        'itemType': '🏷️',
        'itemIndex': '🆔',
        'productType': '🏷️',
        'price': '💰',
        'dataIndex': '🆔',
        'isDiscount': '💸'
    }
    return icon_map.get(field_name, '🛒')

def get_reference_field_icon(field_name):
    """레퍼런스 필드명에 따른 아이콘 반환"""
    icon_map = {
        'card': '🃏',
        'cardList': '🃏',
        'productList': '🛒',
        'intList': '🔢',
        'joker': '🎭',
        'stringDictionary': '📚',
        'stringDictionaryList': '📚',
        'product': '🛒',
        'evaluateResult': '🎯',
        'evaluateConfig': '⚙️',
        'gameInfo': '🎮',
        'evaluateContext': '🎯',
        'consumeItem': '📦'
    }
    return icon_map.get(field_name, '🔗')

def get_generic_field_icon(field_name):
    """일반 필드명에 따른 아이콘 반환"""
    icon_map = {
        'chips': '💎',
        'mult': '✖️',
        'dollar': '💵',
        'battleState': '⚔️',
        'coin': '💰',
        'monsterID': '🐉',
        'health': '💚',
        'maxHealth': '💚',
        'level': '📊',
        'count': '🔢',
        'index': '🆔',
        'type': '🏷️',
        'name': '📝',
        'value': '📊'
    }
    return icon_map.get(field_name, '📊')

def process_reference_value_fields(ref_data, markdown_lines, prefix):
    """레퍼런스 값의 각 필드를 마인드맵 노드로 처리"""
    if not ref_data or not isinstance(ref_data, dict):
        return

    for field_name, field_value in ref_data.items():
        if field_value is None:
            continue

        if field_name == "cardList" and isinstance(field_value, list):
            # repeated PlayingCard_PROTO cardList
            if field_value:
                markdown_lines.append(f"{prefix} 🃏 카드목록 ({len(field_value)}개)")
                for i, card in enumerate(field_value):
                    markdown_lines.append(f"{prefix}# 카드{i+1}")
                    if isinstance(card, dict):
                        for card_field, card_val in card.items():
                            if card_val is not None and str(card_val).strip():
                                icon = get_card_field_icon(card_field)
                                markdown_lines.append(f"{prefix}## {icon} {card_field}: {card_val}")

        elif field_name == "productList" and isinstance(field_value, list):
            # repeated InGameShopProduct_PROTO productList
            if field_value:
                markdown_lines.append(f"{prefix} 🛒 상품목록 ({len(field_value)}개)")
                for i, product in enumerate(field_value):
                    markdown_lines.append(f"{prefix}# 상품{i+1}")
                    if isinstance(product, dict):
                        for prod_field, prod_val in product.items():
                            if prod_val is not None and str(prod_val).strip():
                                icon = get_product_field_icon(prod_field)
                                markdown_lines.append(f"{prefix}## {icon} {prod_field}: {prod_val}")

        elif field_name == "intList" and isinstance(field_value, list):
            # repeated int32 intList
            if field_value:
                markdown_lines.append(f"{prefix} 🔢 정수목록 ({len(field_value)}개)")
                for i, int_val in enumerate(field_value):
                    markdown_lines.append(f"{prefix}# 값{i+1}: {int_val}")

        elif field_name == "stringDictionaryList" and isinstance(field_value, list):
            # repeated BattleLogReferenceValue_PROTO_StringDictionary
            if field_value:
                markdown_lines.append(f"{prefix} 📚 문자열사전목록 ({len(field_value)}개)")
                for i, string_dict in enumerate(field_value):
                    markdown_lines.append(f"{prefix}# 사전{i+1}")
                    if isinstance(string_dict, dict):
                        for k, v in string_dict.items():
                            if v is not None and str(v).strip():
                                markdown_lines.append(f"{prefix}## 📝 {k}: {v}")

        elif isinstance(field_value, dict):
            # 단일 객체 필드들 (card, joker, product, gameInfo 등)
            markdown_lines.append(f"{prefix} {get_reference_field_icon(field_name)} {field_name}")
            for sub_field, sub_val in field_value.items():
                if sub_val is not None and str(sub_val).strip():
                    icon = get_generic_field_icon(sub_field)
                    markdown_lines.append(f"{prefix}# {icon} {sub_field}: {sub_val}")

        elif field_value is not None and str(field_value).strip():
            # 단순 값 필드들
            icon = get_reference_field_icon(field_name)
            markdown_lines.append(f"{prefix} {icon} {field_name}: {field_value}")

def create_mindmap_markdown(df, show_details=True, file_name=""):
    """DataFrame을 Mind Map용 마크다운으로 변환 (새로운 구조: 라운드 header + item 방식)"""

    # 파일명 타입 체크 및 변환
    if not isinstance(file_name, str):
        file_name = str(file_name) if file_name is not None else ""

    # 파일명 단순화
    simple_name = file_name.split('/')[-1] if '/' in file_name else file_name

    # Markmap frontmatter 추가 (레벨 제한 없음)
    markdown_lines = [
        "---",
        "title: 배틀 로그 분석",
        "markmap:",
        "  colorFreezeLevel: 3",
        "  maxWidth: 300",
        "---"
    ]

    # 메인 타이틀
    markdown_lines.append(f"# 📘 배틀 로그: {simple_name}")

    # 게임 정보 섹션 (header)
    game_info = extract_game_info_summary(df) if len(df) > 0 else {}
    if game_info and any(v is not None for v in game_info.values()):
        markdown_lines.append("## 🎮 게임 정보 <!-- markmap: fold -->")

        if game_info.get("userID"):
            markdown_lines.append(f"- 👤 User ID: {game_info['userID']}")
        if game_info.get("userLevel"):
            markdown_lines.append(f"- 📊 User Level: {game_info['userLevel']}")
        if game_info.get("seed"):
            markdown_lines.append(f"- 🌱 Seed: {game_info['seed']}")

    # 컬럼명 확인 및 매핑
    round_col = None
    order_col = None
    event_type_col = None
    event_order_col = None

    for col in df.columns:
        if col in ["round_index", "라운드"]:
            round_col = col
        elif col in ["order", "행동 순서"]:
            order_col = col
        elif col in ["event_type", "이벤트 타입"]:
            event_type_col = col
        elif col in ["event_type_order", "로그 순서"]:
            event_order_col = col

    # 라운드별 처리 (각 라운드를 header로)
    if round_col and round_col in df.columns:
        for round_num in sorted(df[round_col].unique()):
            round_df = df[df[round_col] == round_num]

            # 라운드 header (자동 접힘)
            markdown_lines.append(f"## 🎯 라운드 {round_num} <!-- markmap: foldAll -->")

            # 행동 순서별 처리
            if order_col and order_col in df.columns:
                for order_num in sorted(round_df[order_col].unique()):
                    order_df = round_df[round_df[order_col] == order_num]

                    # 행동 순서를 item으로
                    markdown_lines.append(f"- ⚡ 행동 {order_num}")

                    # 각 이벤트를 하위 item으로 (무제한 깊이)
                    for idx, row in order_df.iterrows():
                        event_type = row.get(event_type_col, "UNKNOWN") if event_type_col else "UNKNOWN"
                        log_order = row.get(event_order_col, "") if event_order_col else ""

                        # 이벤트 item
                        markdown_lines.append(f"  - 📝 이벤트 {log_order}: {event_type}")

                        # 페이로드 정보 (무제한 깊이로 item 추가)
                        add_payload_items(row, markdown_lines, "    ", show_details)

                        # 기본 정보 item들
                        add_basic_info_items(row, df, markdown_lines, "    ")
            else:
                # 행동 순서가 없는 경우 이벤트를 직접 item으로
                for idx, row in round_df.iterrows():
                    event_type = row.get(event_type_col, "UNKNOWN") if event_type_col else "UNKNOWN"
                    log_order = row.get(event_order_col, "") if event_order_col else ""

                    markdown_lines.append(f"- 📝 이벤트 {log_order}: {event_type}")
                    add_payload_items(row, markdown_lines, "  ", show_details)
                    add_basic_info_items(row, df, markdown_lines, "  ")
    else:
        # 라운드 정보가 없는 경우
        markdown_lines.append("## 📝 이벤트 목록 <!-- markmap: fold -->")

        if event_type_col:
            for event_type in df[event_type_col].unique():
                event_df = df[df[event_type_col] == event_type]
                count = len(event_df)

                markdown_lines.append(f"- {event_type} ({count}회)")

                # 상세 정보 표시 (5개 이하만)
                if count <= 5:
                    for idx, row in event_df.iterrows():
                        markdown_lines.append(f"  - 상세 {idx + 1}")
                        add_payload_items(row, markdown_lines, "    ", show_details)
        else:
            markdown_lines.append("- ⚠️ 이벤트 타입 정보를 찾을 수 없습니다")

    return "\n".join(markdown_lines)

def add_payload_items(row, markdown_lines, indent, show_details=True):
    """페이로드 정보를 item 형태로 추가 (무제한 깊이)"""
    payload_keys_str = row.get("payload_keys", "")
    payload_types_str = row.get("payload_types", "")
    scalar_values = row.get("scalarValues", {})
    reference_values = row.get("referenceValues", {})

    if payload_keys_str and str(payload_keys_str).strip():
        # 페이로드 키 파싱
        payload_keys = [k.strip() for k in str(payload_keys_str).split(",") if k.strip()]

        # 타입 딕셔너리 생성
        type_dict = {}
        if payload_types_str and str(payload_types_str).strip():
            type_pairs = [t.strip() for t in str(payload_types_str).split(",") if t.strip()]
            for pair in type_pairs:
                if ":" in pair:
                    key, type_val = pair.split(":", 1)
                    type_dict[key.strip()] = type_val.strip()

        # 각 페이로드 키를 item으로 처리 (무제한 깊이!)
        for key in payload_keys:
            markdown_lines.append(f"{indent}- 🔑 {key}")

            # 스칼라 값 처리 (중간 레벨 제거)
            if key in scalar_values and scalar_values[key]:
                scalar_data = scalar_values[key]

                for field_name, field_value in scalar_data.items():
                    if field_value is not None and str(field_value).strip():
                        if field_name == "cardIndices" and isinstance(field_value, list):
                            markdown_lines.append(f"{indent}  - 🎯 카드인덱스 ({len(field_value)}개)")
                            for i, card_idx in enumerate(field_value[:5]):  # 최대 5개만
                                card_name = get_playing_card_name(card_idx)
                                markdown_lines.append(f"{indent}    - 📇 {card_name}")
                        else:
                            markdown_lines.append(f"{indent}  - 📊 {field_name}: {field_value}")

            # 레퍼런스 값 처리 (중간 레벨 제거)
            if key in reference_values and reference_values[key]:
                ref_data = reference_values[key]
                add_reference_items(ref_data, markdown_lines, f"{indent}  ")

            # 타입 정보
            if key in type_dict:
                markdown_lines.append(f"{indent}  - 🏷️ 타입: {type_dict[key]}")

def add_reference_items(ref_data, markdown_lines, indent):
    """레퍼런스 값을 item 형태로 추가 (무제한 깊이)"""
    if not ref_data or not isinstance(ref_data, dict):
        return

    for field_name, field_value in ref_data.items():
        if field_value is None:
            continue

        if field_name == "cardList" and isinstance(field_value, list):
            markdown_lines.append(f"{indent}- � 카드목록 ({len(field_value)}개)")
            # PlayingCard_PROTO 상세 파싱 사용
            detailed_lines = parse_playing_card_detailed_new(field_value, f"{indent}  ")
            markdown_lines.extend(detailed_lines)

        elif field_name == "gameInfo" and isinstance(field_value, dict):
            markdown_lines.append(f"{indent}- 🎮 게임정보")
            gi_data = field_value

            # 모든 게임정보 필드를 재귀적으로 처리
            for gi_key, gi_value in gi_data.items():
                if gi_value is None:
                    continue

                if gi_key == "battleState":
                    markdown_lines.append(f"{indent}  - ⚔️ 배틀상태: {gi_value}")
                elif gi_key == "coin":
                    markdown_lines.append(f"{indent}  - 💰 코인: {gi_value}")
                elif gi_key == "monsterCurrentHP":
                    max_hp = gi_data.get("monsterMaxHP", "")
                    if max_hp:
                        markdown_lines.append(f"{indent}  - 💚 몬스터체력: {gi_value}/{max_hp}")
                    else:
                        markdown_lines.append(f"{indent}  - 💚 현재체력: {gi_value}")
                elif gi_key == "monsterMaxHP" and "monsterCurrentHP" not in gi_data:
                    markdown_lines.append(f"{indent}  - 💚 최대체력: {gi_value}")
                elif gi_key == "currentMissionInfo" and isinstance(gi_value, dict):
                    markdown_lines.append(f"{indent}  - 🎯 현재미션")
                    for mission_key, mission_val in gi_value.items():
                        if mission_val is None:
                            continue
                        if mission_key == "MissionID":
                            markdown_lines.append(f"{indent}    - 🆔 미션ID: {mission_val}")
                        elif mission_key == "rewardData" and isinstance(mission_val, dict):
                            markdown_lines.append(f"{indent}    - 🎁 보상정보")
                            for reward_key, reward_val in mission_val.items():
                                if reward_val is not None:
                                    if reward_key == "type":
                                        markdown_lines.append(f"{indent}      - �️ 타입: {reward_val}")
                                    elif reward_key == "count":
                                        markdown_lines.append(f"{indent}      - 🔢 개수: {reward_val}")
                                    else:
                                        markdown_lines.append(f"{indent}      - {reward_key}: {reward_val}")
                        else:
                            markdown_lines.append(f"{indent}    - {mission_key}: {mission_val}")
                elif isinstance(gi_value, dict):
                    # 중첩된 딕셔너리는 재귀 처리
                    markdown_lines.append(f"{indent}  - {gi_key}")
                    add_reference_items(gi_value, markdown_lines, f"{indent}    ")
                elif isinstance(gi_value, list):
                    # 특정 타입별 상세 파싱 적용
                    if gi_key in ['originFullDeck', 'playingFullDeck', 'playingDeck', 'destroyedPlayingCards']:
                        # PlayingCard_PROTO 리스트
                        markdown_lines.append(f"{indent}  - � {gi_key} ({len(gi_value)}개)")
                        detailed_lines = parse_playing_card_detailed_new(gi_value, f"{indent}    ")
                        markdown_lines.extend(detailed_lines)
                    elif gi_key in ['playingJokers']:
                        # InGameJoker_PROTO 리스트
                        markdown_lines.append(f"{indent}  - � {gi_key} ({len(gi_value)}개)")
                        detailed_lines = parse_joker_detailed(gi_value, f"{indent}    ")
                        markdown_lines.extend(detailed_lines)
                    elif gi_key in ['playingBookMarks']:
                        # InGameBookMark_PROTO 리스트
                        if gi_value:
                            markdown_lines.append(f"{indent}  - 📖 {gi_key} ({len(gi_value)}개)")
                            detailed_lines = parse_bookmark_detailed(gi_value, f"{indent}    ")
                            markdown_lines.extend(detailed_lines)
                        else:
                            markdown_lines.append(f"{indent}  - 📖 {gi_key}(데이터 없음)")
                    elif gi_key in ['pokerHandDataList']:
                        # PokerHandData_PROTO 리스트
                        if gi_value:
                            markdown_lines.append(f"{indent}  - 🃏 {gi_key} ({len(gi_value)}개)")
                            detailed_lines = parse_poker_hand_detailed(gi_value, f"{indent}    ")
                            markdown_lines.extend(detailed_lines)
                        else:
                            markdown_lines.append(f"{indent}  - 🃏 {gi_key}(데이터 없음)")
                    else:
                        # 일반 리스트 처리
                        markdown_lines.append(f"{indent}  - {gi_key} ({len(gi_value)}개)")
                        for i, item in enumerate(gi_value[:3]):  # 최대 3개만
                            if isinstance(item, dict):
                                markdown_lines.append(f"{indent}    - 항목{i+1}")
                                add_reference_items(item, markdown_lines, f"{indent}      ")
                            else:
                                markdown_lines.append(f"{indent}    - 항목{i+1}: {item}")
                else:
                    # 단순 값
                    markdown_lines.append(f"{indent}  - {gi_key}: {gi_value}")

        elif isinstance(field_value, dict):
            # 특정 타입별 상세 파싱 적용
            if field_name in ['card']:
                # PlayingCard_PROTO 단일 객체
                if isinstance(field_value, dict) and field_value.get('cardDataIndex'):
                    card_name = get_playing_card_name(field_value['cardDataIndex'])
                else:
                    card_name = field_name
                markdown_lines.append(f"{indent}- 🂠 {card_name}")
                detailed_lines = parse_playing_card_detailed_new(field_value, f"{indent}  ")
                markdown_lines.extend(detailed_lines)
            elif field_name in ['joker']:
                # InGameJoker_PROTO 단일 객체
                markdown_lines.append(f"{indent}- 🃏 {field_name}")
                detailed_lines = parse_joker_detailed(field_value, f"{indent}  ")
                markdown_lines.extend(detailed_lines)
            else:
                # 일반적인 딕셔너리 필드 처리
                icon = get_field_icon(field_name)
                markdown_lines.append(f"{indent}- {icon} {field_name}")
                add_reference_items(field_value, markdown_lines, f"{indent}  ")

        elif isinstance(field_value, list):
            # 특정 타입별 상세 파싱 적용
            if field_name in ['cardList', 'originFullDeck', 'playingFullDeck', 'playingDeck', 'destroyedPlayingCards']:
                # PlayingCard_PROTO 리스트
                if field_value:
                    markdown_lines.append(f"{indent}- 🂠 {field_name} ({len(field_value)}개)")
                    detailed_lines = parse_playing_card_detailed_new(field_value, f"{indent}  ")
                    markdown_lines.extend(detailed_lines)
                else:
                    markdown_lines.append(f"{indent}- 🂠 {field_name}(데이터 없음)")
            elif field_name in ['joker', 'playingJokers']:
                # InGameJoker_PROTO 리스트
                if field_value:
                    markdown_lines.append(f"{indent}- 🃏 {field_name} ({len(field_value)}개)")
                    detailed_lines = parse_joker_detailed(field_value, f"{indent}  ")
                    markdown_lines.extend(detailed_lines)
                else:
                    markdown_lines.append(f"{indent}- 🃏 {field_name}(데이터 없음)")
            elif field_name in ['playingBookMarks']:
                # InGameBookMark_PROTO 리스트
                if field_value:
                    markdown_lines.append(f"{indent}- 📖 {field_name} ({len(field_value)}개)")
                    detailed_lines = parse_bookmark_detailed(field_value, f"{indent}  ")
                    markdown_lines.extend(detailed_lines)
                else:
                    markdown_lines.append(f"{indent}- 📖 {field_name}(데이터 없음)")
            elif field_name in ['pokerHandDataList']:
                # PokerHandData_PROTO 리스트
                if field_value:
                    markdown_lines.append(f"{indent}- 🃏 {field_name} ({len(field_value)}개)")
                    detailed_lines = parse_poker_hand_detailed(field_value, f"{indent}  ")
                    markdown_lines.extend(detailed_lines)
                else:
                    markdown_lines.append(f"{indent}- 🃏 {field_name}(데이터 없음)")
            else:
                # 일반 리스트 처리
                icon = get_field_icon(field_name)
                if field_value:
                    markdown_lines.append(f"{indent}- {icon} {field_name} ({len(field_value)}개)")
                    for i, item in enumerate(field_value[:3]):  # 최대 3개만
                        if isinstance(item, dict):
                            markdown_lines.append(f"{indent}  - 항목 {i+1}")
                            add_reference_items(item, markdown_lines, f"{indent}    ")
                        else:
                            markdown_lines.append(f"{indent}  - 값{i+1}: {item}")
                else:
                    markdown_lines.append(f"{indent}- {icon} {field_name}(데이터 없음)")

        elif str(field_value).strip():
            # 단순 값 필드 처리
            icon = get_field_icon(field_name)
            markdown_lines.append(f"{indent}- {icon} {field_name}: {field_value}")

def get_field_icon(field_name):
    """필드명에 따른 아이콘 반환"""
    field_name_lower = field_name.lower()

    if "card" in field_name_lower:
        return "🃏"
    elif "monster" in field_name_lower or "hp" in field_name_lower:
        return "🐉"
    elif "coin" in field_name_lower or "money" in field_name_lower:
        return "💰"
    elif "mission" in field_name_lower or "quest" in field_name_lower:
        return "🎯"
    elif "battle" in field_name_lower or "fight" in field_name_lower:
        return "⚔️"
    elif "reward" in field_name_lower or "prize" in field_name_lower:
        return "🎁"
    elif "level" in field_name_lower or "lv" in field_name_lower:
        return "📊"
    elif "user" in field_name_lower or "player" in field_name_lower:
        return "👤"
    elif "game" in field_name_lower:
        return "🎮"
    elif "info" in field_name_lower or "data" in field_name_lower:
        return "📋"
    elif "current" in field_name_lower:
        return "📍"
    elif "max" in field_name_lower or "maximum" in field_name_lower:
        return "🔝"
    elif "is_" in field_name_lower or field_name_lower.startswith("is"):
        return "✅"
    else:
        return "🔹"

def get_active_payload_data(payload_type, scalar_values, reference_values):
    """PayloadType에 따라 실제로 사용되는 데이터만 반환"""
    active_data = {}

    if not payload_type:
        # PayloadType이 없으면 모든 데이터 반환 (기존 동작)
        return scalar_values, reference_values

    payload_type_str = str(payload_type).upper()

    # Scalar Value 타입들
    if payload_type_str in ['INT', 'INTVALUE']:
        if scalar_values and 'intValue' in scalar_values:
            active_data['intValue'] = scalar_values['intValue']
    elif payload_type_str in ['ULONG', 'ULONGVALUE']:
        if scalar_values and 'ulongValue' in scalar_values:
            active_data['ulongValue'] = scalar_values['ulongValue']
    elif payload_type_str in ['BOOL', 'BOOLVALUE']:
        if scalar_values and 'boolValue' in scalar_values:
            active_data['boolValue'] = scalar_values['boolValue']
    elif payload_type_str in ['STRING', 'STRINGVALUE']:
        if scalar_values and 'stringValue' in scalar_values:
            active_data['stringValue'] = scalar_values['stringValue']
    elif payload_type_str in ['OVERKILL', 'OVERKILLTYPE']:
        if scalar_values and 'overKillType' in scalar_values:
            active_data['overKillType'] = scalar_values['overKillType']
    elif payload_type_str in ['ABILITY', 'ABILITYTYPE']:
        if scalar_values and 'abilityType' in scalar_values:
            active_data['abilityType'] = scalar_values['abilityType']
    elif payload_type_str in ['POKERHANDS', 'POKERHANDSTYPE']:
        if scalar_values and 'pokerHandsType' in scalar_values:
            active_data['pokerHandsType'] = scalar_values['pokerHandsType']
    elif payload_type_str in ['SOUND', 'SOUNDTYPE']:
        if scalar_values and 'soundType' in scalar_values:
            active_data['soundType'] = scalar_values['soundType']
    elif payload_type_str in ['MISSIONREWARD', 'MISSIONREWARDTYPE']:
        if scalar_values and 'missionRewardType' in scalar_values:
            active_data['missionRewardType'] = scalar_values['missionRewardType']
    elif payload_type_str in ['CARDINDICES']:
        if scalar_values and 'cardIndices' in scalar_values:
            active_data['cardIndices'] = scalar_values['cardIndices']

    # Reference Value 타입들 (scalarValue 제거, referenceValue만 반환)
    elif payload_type_str in ['GAMEINFO', 'GAMEINFOSNAPSHOTTYPE', 'GAMEINFOSNAPSHOT']:
        # GAMEINFO 타입: scalarValue는 완전히 무시하고 referenceValue.gameInfo만 반환
        if reference_values and 'gameInfo' in reference_values:
            return {}, {'gameInfo': reference_values['gameInfo']}
        else:
            return {}, {}
    elif payload_type_str in ['CARD']:
        if reference_values and 'card' in reference_values:
            return {}, {'card': reference_values['card']}
        else:
            return {}, {}
    elif payload_type_str in ['CARDLIST']:
        if reference_values and 'cardList' in reference_values:
            return {}, {'cardList': reference_values['cardList']}
        else:
            return {}, {}
    elif payload_type_str in ['JOKER']:
        if reference_values and 'joker' in reference_values:
            return {}, {'joker': reference_values['joker']}
        else:
            return {}, {}
    elif payload_type_str in ['PRODUCT']:
        if reference_values and 'product' in reference_values:
            return {}, {'product': reference_values['product']}
        else:
            return {}, {}
    elif payload_type_str in ['EVALUATERESULT']:
        if reference_values and 'evaluateResult' in reference_values:
            return {}, {'evaluateResult': reference_values['evaluateResult']}
        else:
            return {}, {}
    elif payload_type_str in ['EVALUATECONTEXT']:
        if reference_values and 'evaluateContext' in reference_values:
            return {}, {'evaluateContext': reference_values['evaluateContext']}
        else:
            return {}, {}
    elif payload_type_str in ['STRINGDICTIONARY']:
        if reference_values and 'stringDictionary' in reference_values:
            return {}, {'stringDictionary': reference_values['stringDictionary']}
        else:
            return {}, {}
    elif payload_type_str in ['STRINGDICTIONARYLIST']:
        if reference_values and 'stringDictionaryList' in reference_values:
            return {}, {'stringDictionaryList': reference_values['stringDictionaryList']}
        else:
            return {}, {}
    elif payload_type_str in ['INTLIST']:
        if reference_values and 'intList' in reference_values:
            return {}, {'intList': reference_values['intList']}
        else:
            return {}, {}
    elif payload_type_str in ['PRODUCTLIST']:
        if reference_values and 'productList' in reference_values:
            return {}, {'productList': reference_values['productList']}
        else:
            return {}, {}

    # 알 수 없는 타입이면 모든 데이터 반환
    else:
        return scalar_values, reference_values

    return active_data, {}

def parse_playing_card_detailed(card_data, prefix=""):
    """PlayingCard_PROTO 메시지를 상세히 파싱"""
    if not card_data:
        return []

    lines = []
    if isinstance(card_data, dict):
        # 단일 카드
        if card_data.get('cardDataIndex'):
            lines.append(f"{prefix}- � 카드인덱스: {card_data['cardDataIndex']}")
        if card_data.get('chip'):
            lines.append(f"{prefix}- � 칩: {card_data['chip']}")
        if card_data.get('isBack') is not None:
            lines.append(f"{prefix}- 🔄 뒷면: {card_data['isBack']}")

        # InGameCardLabel_PROTO 파싱
        if card_data.get('label'):
            lines.append(f"{prefix}- 🏷️ 라벨")
            lines.extend(parse_card_label_detailed(card_data['label'], prefix + "  "))
        else:
            lines.append(f"{prefix}- 🏷️ 라벨(없음)")

    elif isinstance(card_data, list):
        # 카드 리스트 (제한 없음)
        for i, card in enumerate(card_data):  # 모든 카드
            lines.append(f"{prefix}- � 카드 {i+1}")
            lines.extend(parse_playing_card_detailed_new(card, prefix + "  "))

    return lines

def parse_playing_card_detailed_new(card_data, prefix=""):
    """PlayingCard_PROTO 메시지를 상세히 파싱 (카드인덱스를 제목으로 사용)"""
    if not card_data:
        return []

    lines = []
    if isinstance(card_data, dict):
        # 단일 카드 (카드인덱스는 제목에서 이미 표시되므로 제거)
        if card_data.get('chip'):
            lines.append(f"{prefix}- 💰 칩: {card_data['chip']}")
        if card_data.get('isBack') is not None:
            lines.append(f"{prefix}- 🔄 뒷면: {card_data['isBack']}")

        # InGameCardLabel_PROTO 파싱
        if card_data.get('label'):
            lines.append(f"{prefix}- 🏷️ 라벨")
            lines.extend(parse_card_label_detailed(card_data['label'], prefix + "  "))
        else:
            lines.append(f"{prefix}- 🏷️ 라벨(없음)")

    elif isinstance(card_data, list):
        # 카드 리스트 (제한 없음)
        for i, card in enumerate(card_data):  # 모든 카드
            # 카드인덱스가 있으면 한국어 이름으로 사용, 없으면 기본 번호
            if isinstance(card, dict) and card.get('cardDataIndex'):
                card_name = get_playing_card_name(card['cardDataIndex'])
            else:
                card_name = f"카드 {i+1}"
            lines.append(f"{prefix}- 🂠 {card_name}")
            lines.extend(parse_playing_card_detailed_new(card, prefix + "  "))

    return lines

def parse_poker_hand_detailed(poker_data, prefix="", show_type=True):
    """PokerHandData_PROTO 메시지를 상세히 파싱"""
    if not poker_data:
        return []

    lines = []
    if isinstance(poker_data, dict):
        # 단일 포커 핸드
        if poker_data.get('dataIndex'):
            lines.append(f"{prefix}- 🆔 데이터인덱스: {poker_data['dataIndex']}")
        # 타입은 제목에 표시되었으면 생략
        if poker_data.get('type') and show_type:
            lines.append(f"{prefix}- 🃏 타입: {poker_data['type']}")
        if poker_data.get('lv'):
            lines.append(f"{prefix}- 📊 레벨: {poker_data['lv']}")
        if poker_data.get('chip'):
            lines.append(f"{prefix}- 💰 칩: {poker_data['chip']}")
        if poker_data.get('multiple'):
            lines.append(f"{prefix}- ✖️ 배수: {poker_data['multiple']}")
        if poker_data.get('playCount'):
            lines.append(f"{prefix}- 🎮 플레이횟수: {poker_data['playCount']}")
        if poker_data.get('throwCount'):
            lines.append(f"{prefix}- 🗑️ 버린횟수: {poker_data['throwCount']}")

    elif isinstance(poker_data, list):
        # 포커 핸드 리스트
        for i, hand in enumerate(poker_data):
            # 타입을 우선적으로 표시, 없으면 dataIndex, 그것도 없으면 번호
            if isinstance(hand, dict):
                if hand.get('type'):
                    hand_name = f"타입: {hand['type']}"
                elif hand.get('dataIndex'):
                    hand_name = f"포커핸드 {hand['dataIndex']}"
                else:
                    hand_name = f"포커핸드 {i+1}"
            else:
                hand_name = f"포커핸드 {i+1}"
            lines.append(f"{prefix}- 🃏 {hand_name}")
            # 타입이 제목에 표시되었으면 내용에서는 타입 생략
            show_type = not (isinstance(hand, dict) and hand.get('type'))
            lines.extend(parse_poker_hand_detailed(hand, prefix + "  ", show_type))

    return lines

def handle_empty_list_display(field_name, field_value, indent, icon="📋"):
    """빈 리스트 처리를 위한 헬퍼 함수"""
    if field_value:
        return f"{indent}- {icon} {field_name} ({len(field_value)}개)"
    else:
        return f"{indent}- {icon} {field_name}(데이터 없음)"

def parse_card_label_detailed(label_data, prefix=""):
    """InGameCardLabel_PROTO 메시지를 상세히 파싱"""
    if not label_data:
        return [f"{prefix}- 📝 데이터 없음"]

    lines = []
    has_data = False

    if label_data.get('labelDataIndex'):
        label_name = get_card_label_name(label_data['labelDataIndex'])
        lines.append(f"{prefix}- 🆔 카드라벨: {label_name}")
        has_data = True

    # InGameAbility_PROTO 리스트 파싱
    if label_data.get('abilities'):
        lines.append(f"{prefix}- ⚡ 능력들 ({len(label_data['abilities'])}개)")
        lines.extend(parse_abilities_detailed(label_data['abilities'], prefix + "  "))
        has_data = True

    # 데이터가 없으면 안내 메시지 추가
    if not has_data:
        lines.append(f"{prefix}- 📝 라벨 데이터 없음")

    return lines

def parse_joker_detailed(joker_data, prefix=""):
    """InGameJoker_PROTO 메시지를 상세히 파싱"""
    if not joker_data:
        return []

    lines = []
    if isinstance(joker_data, dict):
        # 단일 조커
        if joker_data.get('jokerDataIndex'):
            joker_name = get_joker_name(joker_data['jokerDataIndex'])
            lines.append(f"{prefix}- 🎭 조커: {joker_name}")
        if joker_data.get('userDataLv'):
            lines.append(f"{prefix}- 📊 레벨: {joker_data['userDataLv']}")
        if joker_data.get('isBack') is not None:
            lines.append(f"{prefix}- 🔄 뒷면: {joker_data['isBack']}")

        # InGameAbility_PROTO 리스트 파싱
        if joker_data.get('abilities'):
            lines.append(f"{prefix}- ⚡ 어빌리티 목록 ({len(joker_data['abilities'])}개)")
            lines.extend(parse_abilities_detailed(joker_data['abilities'], prefix + "  "))
        else:
            lines.append(f"{prefix}- ⚡ 어빌리티 목록(없음)")

    elif isinstance(joker_data, list):
        # 조커 리스트 (제한 없음)
        for i, joker in enumerate(joker_data):  # 모든 조커
            lines.append(f"{prefix}- � 조커 {i+1}")
            lines.extend(parse_joker_detailed(joker, prefix + "  "))

    return lines

def parse_abilities_detailed(abilities_data, prefix=""):
    """InGameAbility_PROTO 리스트를 상세히 파싱"""
    if not abilities_data:
        return []

    lines = []
    for i, ability in enumerate(abilities_data):  # 모든 능력
        if isinstance(ability, dict):
            # 어빌리티 인덱스를 제목에 표시
            ability_idx = ability.get('abilityDataIndex', i+1)
            lines.append(f"{prefix}- ⚡ 어빌리티 Idx ({ability_idx})")

            # 능력인덱스는 제목에 표시했으므로 중복 제거
            if ability.get('calculateType'):
                lines.append(f"{prefix}  - 🧮 계산타입: {ability['calculateType']}")
            if ability.get('calculateArea'):
                lines.append(f"{prefix}  - 📍 계산영역: {ability['calculateArea']}")
            if ability.get('turnType'):
                lines.append(f"{prefix}  - 🔄 턴타입: {ability['turnType']}")
            if ability.get('round'):
                lines.append(f"{prefix}  - 🎯 라운드: {ability['round']}")

            # InGameAbilityValue_PROTO 파싱
            if ability.get('values'):
                lines.append(f"{prefix}  - 📊 values ({len(ability['values'])}개)")
                lines.extend(parse_ability_values_detailed(ability['values'], prefix + "    "))
            else:
                lines.append(f"{prefix}  - 📊 values(없음)")

            if ability.get('conditions'):
                lines.append(f"{prefix}  - 🎯 conditions ({len(ability['conditions'])}개)")
                lines.extend(parse_ability_values_detailed(ability['conditions'], prefix + "    "))
            else:
                lines.append(f"{prefix}  - 🎯 conditions(없음)")

    return lines

def parse_ability_values_detailed(values_data, prefix=""):
    """InGameAbilityValue_PROTO 리스트를 상세히 파싱"""
    if not values_data:
        return []

    lines = []
    for i, value in enumerate(values_data):  # 모든 값
        if isinstance(value, dict):
            # key를 제목으로 사용, 없으면 번호 사용
            value_key = value.get('key', f'항목 {i+1}')
            lines.append(f"{prefix}- 🔑 {value_key}")

            # InGameAbilityValue_PROTO 구조에 맞게 파싱
            if value.get('type'):
                lines.append(f"{prefix}  - 🏷️ type: {value['type']}")
            if value.get('value'):
                lines.append(f"{prefix}  - 📊 value: {value['value']}")

    return lines

def parse_bookmark_detailed(bookmark_data, prefix=""):
    """InGameBookMark_PROTO 메시지를 상세히 파싱"""
    if not bookmark_data:
        return []

    lines = []
    if isinstance(bookmark_data, dict):
        # 단일 북마크
        if bookmark_data.get('bookmarkDataIndex'):
            bookmark_name = get_bookmark_name(bookmark_data['bookmarkDataIndex'])
            lines.append(f"{prefix}- 📖 책갈피: {bookmark_name}")
        if bookmark_data.get('userDataLv'):
            lines.append(f"{prefix}- 📊 레벨: {bookmark_data['userDataLv']}")
        if bookmark_data.get('_enhanceLv'):
            lines.append(f"{prefix}- ⬆️ 강화레벨: {bookmark_data['_enhanceLv']}")

        # InGameBookMarkSkill_PROTO 리스트 파싱
        if bookmark_data.get('_listSkills'):
            lines.append(f"{prefix}- 🎯 스킬들 ({len(bookmark_data['_listSkills'])}개)")
            lines.extend(parse_bookmark_skills_detailed(bookmark_data['_listSkills'], prefix + "  "))
        else:
            lines.append(f"{prefix}- 🎯 스킬(없음)")

        if bookmark_data.get('_listSkillsPlayed'):
            lines.append(f"{prefix}- 🎮 사용된스킬들 ({len(bookmark_data['_listSkillsPlayed'])}개)")
            lines.extend(parse_bookmark_skills_detailed(bookmark_data['_listSkillsPlayed'], prefix + "  "))
        else:
            lines.append(f"{prefix}- 🎮 사용된스킬(없음)")

    elif isinstance(bookmark_data, list):
        # 북마크 리스트 (제한 없음)
        for i, bookmark in enumerate(bookmark_data):  # 모든 북마크
            lines.append(f"{prefix}- 📖 북마크 {i+1}")
            lines.extend(parse_bookmark_detailed(bookmark, prefix + "  "))

    return lines

def parse_bookmark_skills_detailed(skills_data, prefix=""):
    """InGameBookMarkSkill_PROTO 리스트를 상세히 파싱"""
    if not skills_data:
        return []

    lines = []
    for i, skill in enumerate(skills_data):  # 모든 스킬
        if isinstance(skill, dict):
            lines.append(f"{prefix}- 🎯 스킬 {i+1}")
            if skill.get('bookmarkDataIndex'):
                bookmark_name = get_bookmark_name(skill['bookmarkDataIndex'])
                lines.append(f"{prefix}  - 🆔 책갈피: {bookmark_name}")

            # InGameAbility_PROTO 리스트 파싱
            if skill.get('abilities'):
                lines.append(f"{prefix}  - ⚡ 능력들 ({len(skill['abilities'])}개)")
                lines.extend(parse_abilities_detailed(skill['abilities'], prefix + "    "))
            else:
                lines.append(f"{prefix}  - ⚡ 능력(없음)")

    return lines



def add_basic_info_items(row, df, markdown_lines, indent):
    """기본 정보를 item 형태로 추가"""
    # 컬럼명 매핑
    bookmark_col = "bookmarkDataIndex" if "bookmarkDataIndex" in df.columns else "책갈피 IDX"
    joker_col = "jokerDataIndex" if "jokerDataIndex" in df.columns else "조커 IDX"
    hp_col = "monsterCurrentHP" if "monsterCurrentHP" in df.columns else "몬스터 체력"
    max_hp_col = "monsterMaxHP" if "monsterMaxHP" in df.columns else "몬스터 최대 체력"
    mult_col = "multiple" if "multiple" in df.columns else "배수"
    chip_col = "chip" if "chip" in df.columns else "공격력"

    if row.get(bookmark_col) and str(row[bookmark_col]).strip():
        bookmark_idx = int(row[bookmark_col]) if str(row[bookmark_col]).isdigit() else row[bookmark_col]
        bookmark_name = get_bookmark_name(bookmark_idx) if isinstance(bookmark_idx, int) else f"책갈피 {bookmark_idx}"
        markdown_lines.append(f"{indent}- 📖 책갈피: {bookmark_name}")

    if row.get(joker_col) and str(row[joker_col]).strip():
        joker_idx = int(row[joker_col]) if str(row[joker_col]).isdigit() else row[joker_col]
        joker_name = get_joker_name(joker_idx) if isinstance(joker_idx, int) else f"조커 {joker_idx}"
        markdown_lines.append(f"{indent}- 🃏 조커: {joker_name}")

    if row.get(hp_col) and str(row[hp_col]).strip():
        max_hp = row.get(max_hp_col, "")
        if max_hp and str(max_hp).strip():
            markdown_lines.append(f"{indent}- 🐉 몬스터체력: {row[hp_col]}/{max_hp}")
        else:
            markdown_lines.append(f"{indent}- 🐉 몬스터체력: {row[hp_col]}")

    if row.get(mult_col) and str(row[mult_col]).strip():
        markdown_lines.append(f"{indent}- ✖️ 배수: {row[mult_col]}")

    if row.get(chip_col) and str(row[chip_col]).strip():
        markdown_lines.append(f"{indent}- ⚔️ 공격력: {row[chip_col]}")

def show_mindmap_viewer(filtered_files):
    """Mind Map 뷰어 모드 UI"""
    st.subheader("🧠 Mind Map 뷰")

    # 레벨 테스트 코드 삭제됨

    # 사용법 안내
    with st.expander("💡 Mind Map 뷰 사용법", expanded=False):
        st.markdown("""
        **Mind Map 뷰는 배틀 로그를 시각적인 마인드맵으로 표현합니다:**

        - 🎮 **게임 정보**: User ID, Level, Seed 등 기본 정보
        - 🎯 **라운드별 구조**: 각 라운드 → 행동 → 이벤트 순서로 계층화
        - 📊 **상세 데이터**: 책갈피, 조커, 몬스터 상태, 평가 결과 등 모든 정보 포함
        - 🔍 **인터랙티브**: 노드 클릭으로 확장/축소 가능

        **성능 팁**: 이벤트 수가 많을 경우 로딩 시간이 길어질 수 있으니 적절한 이벤트 수를 선택하세요.
        """)

    if not filtered_files:
        st.warning("표시할 로그 파일이 없습니다.")
        st.stop()

    selected_log = st.selectbox("📄 로그 파일 선택", filtered_files, key="mindmap_log_select")



    # 성능 최적화 옵션
    col1, col2, col3 = st.columns([2, 1, 1])
    with col1:
        st.info(f"총 {len(filtered_files)}개의 로그 파일 중 선택된 파일: {selected_log}")
    with col2:
        max_events = st.selectbox("📊 최대 이벤트 수", [100, 500, 1000, 2000, 5000],
                                 index=2, help="Mind Map의 경우 너무 많은 이벤트는 가독성을 해칠 수 있습니다",
                                 key="mindmap_max_events")
    with col3:
        show_details = st.checkbox("📋 상세 정보 표시", value=True,
                                  help="체크 해제 시 기본 정보만 표시하여 더 간결한 표시")

    # 사이드바 설정 (원래대로 복원)
    with st.sidebar:
        st.subheader("�️ Mind Map 설정")

        # 높이 설정
        map_height = st.slider("📏 Mind Map 높이", 400, 1200, 800, 50,
                              help="Mind Map의 표시 높이를 조정합니다")

        # 표시 모드 선택
        expand_mode = st.selectbox(
            "🔽 초기 표시 모드",
            ["접힌 상태", "펼친 상태"],
            help="Mind Map의 초기 표시 상태를 선택합니다"
        )

        # PayloadType 필터링 옵션
        use_payload_filtering = st.checkbox(
            "🎯 PayloadType 기반 필터링 사용",
            value=True,
            help="PayloadType에 따라 실제로 사용되는 데이터만 표시합니다. 체크 해제시 모든 데이터를 표시합니다."
        )

    if not selected_log:
        st.warning("로그 파일을 선택해주세요.")
        st.stop()

    # 로딩 시간 측정
    import time
    start_time = time.time()

    with st.spinner("로그 파일을 파싱하고 Mind Map을 생성하는 중..."):
        # 게임 정보 요약 추출 (캐싱됨)
        game_info = get_game_info_summary(selected_log)

        # 확장된 데이터프레임 파싱 (캐싱됨) - 모든 항목 포함
        df = get_parsed_log_extended(selected_log, max_events=max_events)

        # 대용량 데이터 처리 알림
        if len(df) > 2000:
            st.info(f"📊 대용량 데이터 ({len(df):,}개 이벤트) 처리 중... 잠시만 기다려주세요.")

        # Mind Map용 마크다운 생성 (최적화된 버전)
        markdown_content = convert_log_to_markdown_extended(df, game_info, selected_log, show_details, use_payload_filtering)

    load_time = time.time() - start_time
    st.success(f"✅ Mind Map 생성 완료! ({len(df):,}개 이벤트, {load_time:.2f}초)")

    # Mind Map 표시
    st.subheader("🗺️ 배틀 로그 Mind Map")

    try:
        # Mind Map 높이 조절 옵션
        col1, col2 = st.columns([3, 1])
        with col2:
            map_height = st.slider("📏 Mind Map 높이", 400, 1000, 600, 50, help="Mind Map의 높이를 조절합니다")

        # 새로운 구조의 마인드맵 표시 (frontmatter 이미 포함됨)
        # 라운드 header + item 구조로 레벨 제한 없음
        markmap(markdown_content, height=map_height)

        # 디버깅용 마크다운 미리보기 추가
        with st.expander("🔍 생성된 마크다운 미리보기"):
            st.code(markdown_content[:3000] + "..." if len(markdown_content) > 3000 else markdown_content,
                   language="markdown")

    except Exception as e:
        st.error(f"❌ Mind Map 생성 중 오류가 발생했습니다: {str(e)}")
        st.info("💡 이벤트 수를 줄이거나 다른 로그 파일을 선택해보세요.")

        # 오류 발생 시 대체 정보 표시
        with st.expander("🔍 원본 마크다운 보기"):
            st.code(markdown_content, language="markdown")

    # 상세 정보 표시 옵션
    with st.expander("📊 상세 통계 정보"):
        col1, col2, col3, col4 = st.columns(4)

        with col1:
            st.metric("📋 총 이벤트 수", len(df))

        with col2:
            if "round_index" in df.columns:
                max_round = df["round_index"].max()
                st.metric("🎯 최대 라운드", max_round)
            elif "라운드" in df.columns:
                max_round = df["라운드"].max()
                st.metric("🎯 최대 라운드", max_round)

        with col3:
            if "event_type" in df.columns:
                unique_events = df["event_type"].nunique()
                st.metric("🎪 이벤트 타입 수", unique_events)
            elif "이벤트 타입" in df.columns:
                unique_events = df["이벤트 타입"].nunique()
                st.metric("🎪 이벤트 타입 수", unique_events)

        with col4:
            markdown_size = len(markdown_content)
            st.metric("📄 마크다운 크기", f"{markdown_size:,} 문자")

        # 이벤트 타입별 분포 차트
        if "event_type" in df.columns:
            st.subheader("📈 이벤트 타입별 분포")
            event_counts = df["event_type"].value_counts().head(10)
            st.bar_chart(event_counts)
        elif "이벤트 타입" in df.columns:
            st.subheader("📈 이벤트 타입별 분포")
            event_counts = df["이벤트 타입"].value_counts().head(10)
            st.bar_chart(event_counts)

        # 배틀 상태 디버깅 정보
        if "battleState" in df.columns:
            st.subheader("🔍 배틀 상태 디버깅")
            battle_states = df["battleState"].dropna().unique()
            if len(battle_states) > 0:
                st.write("**발견된 배틀 상태 값들:**")
                for state in battle_states:
                    st.code(f"배틀 상태: {state}")
            else:
                st.info("배틀 상태 데이터가 없습니다.")

        # 스칼라 값 디버깅 정보
        if "scalarValues" in df.columns:
            st.subheader("🔍 스칼라 값 디버깅")
            scalar_sample = df["scalarValues"].dropna().head(3)
            for i, scalar_data in enumerate(scalar_sample):
                if isinstance(scalar_data, dict) and scalar_data:
                    st.write(f"**스칼라 값 샘플 {i+1}:**")
                    st.json(scalar_data)

        # Reference Values 디버깅 정보
        if "referenceValues" in df.columns:
            st.subheader("🔍 Reference Values 디버깅")
            ref_sample = df["referenceValues"].dropna().head(2)
            for i, ref_data in enumerate(ref_sample):
                if isinstance(ref_data, dict) and ref_data:
                    st.write(f"**Reference Values 샘플 {i+1}:**")
                    # 너무 크면 일부만 표시
                    display_data = {}
                    for key, value in ref_data.items():
                        if isinstance(value, dict):
                            # 각 reference value의 키만 표시
                            display_data[key] = list(value.keys())
                        else:
                            display_data[key] = value
                    st.json(display_data)

@st.cache_data(ttl=3600)  # 1시간 캐시
def get_shop_statistics_cached(file_paths_tuple):
    """캐시된 상점 통계를 가져옵니다."""
    file_paths = list(file_paths_tuple)
    return get_shop_statistics_summary(file_paths)

def show_shop_stats(filtered_files, log_type="All"):
    """상점 통계 모드 UI"""
    st.subheader("🛒 상점 통계")
    st.markdown("**전체 로그의 상점 아이템 구매/판매 통계를 분석합니다**")

    if not filtered_files:
        st.warning("표시할 로그 파일이 없습니다.")
        return

    # 진행상황 표시
    progress_bar = st.progress(0)
    status_text = st.empty()
    info_text = st.empty()

    try:
        # 파일 수 확인
        total_files = len(filtered_files)

        info_text.info(f"{total_files}개 파일을 분석합니다.")

        status_text.text(f"상점 데이터를 분석하는 중... (0/{total_files})")
        progress_bar.progress(0.0)

        # 상점 통계 수집
        import time
        start_time = time.time()

        # 로그 출력을 위한 컨테이너
        log_container = st.empty()

        with st.spinner("상점 데이터 분석 중..."):
            # 캐시를 위해 리스트를 튜플로 변환
            file_paths_tuple = tuple(filtered_files)
            shop_summary = get_shop_statistics_cached(file_paths_tuple)

        elapsed_time = time.time() - start_time

        progress_bar.progress(1.0)
        status_text.text(f"분석 완료! ({elapsed_time:.1f}초 소요)")

        # 잠시 결과 표시 후 정리
        time.sleep(1)
        progress_bar.empty()
        status_text.empty()
        info_text.empty()
        log_container.empty()

    except Exception as e:
        progress_bar.empty()
        status_text.empty()
        info_text.empty()
        st.error(f"상점 통계 분석 중 오류가 발생했습니다: {e}")
        st.error("로그를 확인하여 자세한 오류 정보를 확인하세요.")
        return

    # 기본 통계 표시
    col1, col2, col3, col4 = st.columns(4)

    with col1:
        st.metric("전체 파일 수", shop_summary['total_files'])

    with col2:
        st.metric("상점 데이터 포함 파일", shop_summary['files_with_shop_data'])

    with col3:
        st.metric("총 상점 이벤트", f"{shop_summary['total_shop_events']:,}")

    with col4:
        if shop_summary['total_files'] > 0:
            coverage = (shop_summary['files_with_shop_data'] / shop_summary['total_files']) * 100
            st.metric("데이터 커버리지", f"{coverage:.1f}%")
        else:
            st.metric("데이터 커버리지", "0.0%")

    # 구매/판매 통계 표시
    if shop_summary['total_shop_events'] > 0:
        st.subheader("📊 구매/판매 통계")

        col1, col2, col3 = st.columns(3)

        with col1:
            purchase_count = shop_summary['purchase_stats'].get('total_count', 0)
            st.metric("🛒 총 구매", f"{purchase_count:,}")

        with col2:
            sale_count = shop_summary['sale_stats'].get('total_count', 0)
            st.metric("💰 총 판매", f"{sale_count:,}")

        with col3:
            if purchase_count + sale_count > 0:
                purchase_ratio = (purchase_count / (purchase_count + sale_count)) * 100
                st.metric("구매 비율", f"{purchase_ratio:.1f}%")
            else:
                st.metric("구매 비율", "0.0%")

    if shop_summary['total_shop_events'] == 0:
        st.info("📊 상점 데이터가 없습니다. 다른 로그 파일을 선택해보세요.")
        return

    # 탭으로 구분된 상세 통계
    tab1, tab2, tab3 = st.tabs(["📊 아이템 타입별", "🛒 구매 통계", "💰 판매 통계"])

    with tab1:
        st.subheader("🏷️ 아이템 타입별 통계")

        if shop_summary['item_type_stats']:
            # 차트 표시 (한국어 타입명 사용)
            type_data = []
            for item_type, count in shop_summary['item_type_stats'].items():
                korean_type_name = get_shop_item_type_korean(item_type)
                type_data.append({'아이템 타입': korean_type_name, '개수': count})

            type_df = pd.DataFrame(type_data)
            type_df = type_df.sort_values('개수', ascending=False)

            col1, col2 = st.columns([2, 1])

            with col1:
                st.bar_chart(type_df.set_index('아이템 타입'))

            with col2:
                # 구매/판매 구분 테이블 (한국어 타입명 사용)
                detailed_type_data = []
                for item_type, count in shop_summary['item_type_stats'].items():
                    korean_type_name = get_shop_item_type_korean(item_type)
                    detailed_type_data.append({
                        '아이템 타입': korean_type_name,
                        '총 개수': count,
                        '구매': shop_summary['combined_stats'].get(item_type, {}).get('purchase_count', 0),
                        '판매': shop_summary['combined_stats'].get(item_type, {}).get('sale_count', 0),
                        '비율(%)': f"{(count/shop_summary['total_shop_events']*100):.1f}%"
                    })

                detailed_type_df = pd.DataFrame(detailed_type_data)
                st.dataframe(detailed_type_df, use_container_width=True)
        else:
            st.info("아이템 타입 데이터가 없습니다.")

    with tab2:
        st.subheader("🛒 구매 통계")

        if shop_summary['purchase_stats'] and shop_summary['purchase_stats'].get('total_count', 0) > 0:
            purchase_stats = shop_summary['purchase_stats']

            # 구매 기본 정보
            col1, col2 = st.columns(2)

            with col1:
                st.metric("총 구매 건수", f"{purchase_stats['total_count']:,}")

            with col2:
                if shop_summary['total_shop_events'] > 0:
                    purchase_ratio = (purchase_stats['total_count'] / shop_summary['total_shop_events']) * 100
                    st.metric("전체 대비 구매 비율", f"{purchase_ratio:.1f}%")

            # 구매 아이템 타입별 분포
            if purchase_stats['item_type_stats']:
                st.markdown("**구매 아이템 타입별 분포**")

                purchase_type_df = pd.DataFrame(list(purchase_stats['item_type_stats'].items()),
                                              columns=['아이템 타입', '구매 건수'])
                purchase_type_df = purchase_type_df.sort_values('구매 건수', ascending=False)

                col1, col2 = st.columns([2, 1])

                with col1:
                    st.bar_chart(purchase_type_df.set_index('아이템 타입'))

                with col2:
                    purchase_type_df['비율(%)'] = (purchase_type_df['구매 건수'] / purchase_stats['total_count'] * 100).round(1)
                    st.dataframe(purchase_type_df, use_container_width=True)

            # 구매 상위 아이템 인덱스 (아이템 타입별)
            if purchase_stats['item_type_stats']:
                st.markdown("**🛒 구매 상위 아이템 (아이템 타입별)**")

                # 아이템 타입별로 탭 생성
                item_types = list(purchase_stats['item_type_stats'].keys())
                if len(item_types) > 1:
                    # 한국어 타입명으로 탭 생성
                    korean_type_names = [get_shop_item_type_korean(item_type) for item_type in item_types]
                    type_tabs = st.tabs(korean_type_names)

                    for i, item_type in enumerate(item_types):
                        with type_tabs[i]:
                            # 해당 아이템 타입의 구매 데이터만 필터링
                            if 'raw_data' in shop_summary and not shop_summary['raw_data'].empty:
                                type_purchase_data = shop_summary['raw_data'][
                                    (shop_summary['raw_data']['transaction_type'] == '구매') &
                                    (shop_summary['raw_data']['item_type'] == item_type)
                                ]

                                if not type_purchase_data.empty:
                                    # 아이템 인덱스별 구매 횟수 계산
                                    index_counts = type_purchase_data['item_index'].value_counts().head(10)

                                    if len(index_counts) > 0:
                                        # Rank 로그 유형일 때만 라운드 정보 표시
                                        if log_type == "Rank":
                                            # 각 아이템 인덱스별로 가장 많이 구매한 라운드 계산
                                            round_info = {}
                                            for item_index in index_counts.index:
                                                item_data = type_purchase_data[type_purchase_data['item_index'] == item_index]
                                                round_counts = item_data['round_index'].value_counts()
                                                if len(round_counts) > 0:
                                                    most_frequent_round = round_counts.index[0]
                                                    round_count = round_counts.iloc[0]
                                                    round_info[item_index] = f"{most_frequent_round} ({round_count}번)"
                                                else:
                                                    round_info[item_index] = '-'

                                            # 한국어 이름으로 변환
                                            item_names = []
                                            for idx in index_counts.index:
                                                item_names.append(get_item_name_by_type(item_type, idx))

                                            index_df = pd.DataFrame({
                                                '아이템': item_names,
                                                '구매 횟수': index_counts.values,
                                                '주요 라운드': [round_info.get(idx, '-') for idx in index_counts.index]
                                            })
                                        else:
                                            # 한국어 이름으로 변환
                                            item_names = []
                                            for idx in index_counts.index:
                                                item_names.append(get_item_name_by_type(item_type, idx))

                                            index_df = pd.DataFrame({
                                                '아이템': item_names,
                                                '구매 횟수': index_counts.values
                                            })

                                        st.dataframe(index_df, use_container_width=True)
                                        st.caption(f"상위 {len(index_df)}개 표시")
                                    else:
                                        st.info(f"{item_type} 구매 데이터가 없습니다.")
                                else:
                                    st.info(f"{item_type} 구매 데이터가 없습니다.")
                            else:
                                st.info("원본 데이터가 없습니다.")
                else:
                    # 아이템 타입이 1개인 경우
                    item_type = item_types[0]
                    korean_type_name = get_shop_item_type_korean(item_type)
                    st.subheader(f"📦 {korean_type_name} 구매 상세")
                    if 'raw_data' in shop_summary and not shop_summary['raw_data'].empty:
                        type_purchase_data = shop_summary['raw_data'][
                            (shop_summary['raw_data']['transaction_type'] == '구매') &
                            (shop_summary['raw_data']['item_type'] == item_type)
                        ]

                        if not type_purchase_data.empty:
                            index_counts = type_purchase_data['item_index'].value_counts().head(10)

                            if len(index_counts) > 0:
                                # Rank 로그 유형일 때만 라운드 정보 표시
                                if log_type == "Rank":
                                    # 각 아이템 인덱스별로 가장 많이 구매한 라운드 계산
                                    round_info = {}
                                    for item_index in index_counts.index:
                                        item_data = type_purchase_data[type_purchase_data['item_index'] == item_index]
                                        round_counts = item_data['round_index'].value_counts()
                                        if len(round_counts) > 0:
                                            most_frequent_round = round_counts.index[0]
                                            round_count = round_counts.iloc[0]
                                            round_info[item_index] = f"{most_frequent_round} ({round_count}번)"
                                        else:
                                            round_info[item_index] = '-'

                                    # 한국어 이름으로 변환
                                    item_names = []
                                    for idx in index_counts.index:
                                        item_names.append(get_item_name_by_type(item_type, idx))

                                    index_df = pd.DataFrame({
                                        '아이템': item_names,
                                        '구매 횟수': index_counts.values,
                                        '주요 라운드': [round_info.get(idx, '-') for idx in index_counts.index]
                                    })
                                else:
                                    # 한국어 이름으로 변환
                                    item_names = []
                                    for idx in index_counts.index:
                                        item_names.append(get_item_name_by_type(item_type, idx))

                                    index_df = pd.DataFrame({
                                        '아이템': item_names,
                                        '구매 횟수': index_counts.values
                                    })

                                st.dataframe(index_df, use_container_width=True)
                                st.caption(f"상위 {len(index_df)}개 표시")
                            else:
                                st.info(f"{item_type} 구매 데이터가 없습니다.")
                        else:
                            st.info(f"{item_type} 구매 데이터가 없습니다.")
                    else:
                        st.info("원본 데이터가 없습니다.")
        else:
            st.info("구매 데이터가 없습니다.")

    with tab3:
        st.subheader("💰 판매 통계")

        if shop_summary['sale_stats'] and shop_summary['sale_stats'].get('total_count', 0) > 0:
            sale_stats = shop_summary['sale_stats']

            # 판매 기본 정보
            col1, col2 = st.columns(2)

            with col1:
                st.metric("총 판매 건수", f"{sale_stats['total_count']:,}")

            with col2:
                if shop_summary['total_shop_events'] > 0:
                    sale_ratio = (sale_stats['total_count'] / shop_summary['total_shop_events']) * 100
                    st.metric("전체 대비 판매 비율", f"{sale_ratio:.1f}%")

            # 판매 아이템 타입별 분포
            if sale_stats['item_type_stats']:
                st.markdown("**판매 아이템 타입별 분포**")

                sale_type_df = pd.DataFrame(list(sale_stats['item_type_stats'].items()),
                                          columns=['아이템 타입', '판매 건수'])
                sale_type_df = sale_type_df.sort_values('판매 건수', ascending=False)

                col1, col2 = st.columns([2, 1])

                with col1:
                    st.bar_chart(sale_type_df.set_index('아이템 타입'))

                with col2:
                    sale_type_df['비율(%)'] = (sale_type_df['판매 건수'] / sale_stats['total_count'] * 100).round(1)
                    st.dataframe(sale_type_df, use_container_width=True)

            # 판매 상위 아이템 인덱스 (아이템 타입별)
            if sale_stats['item_type_stats']:
                st.markdown("**💰 판매 상위 아이템 (아이템 타입별)**")

                # 아이템 타입별로 탭 생성
                item_types = list(sale_stats['item_type_stats'].keys())
                if len(item_types) > 1:
                    # 한국어 타입명으로 탭 생성
                    korean_type_names = [get_shop_item_type_korean(item_type) for item_type in item_types]
                    type_tabs = st.tabs(korean_type_names)

                    for i, item_type in enumerate(item_types):
                        with type_tabs[i]:
                            # 해당 아이템 타입의 판매 데이터만 필터링
                            if 'raw_data' in shop_summary and not shop_summary['raw_data'].empty:
                                type_sale_data = shop_summary['raw_data'][
                                    (shop_summary['raw_data']['transaction_type'] == '판매') &
                                    (shop_summary['raw_data']['item_type'] == item_type)
                                ]

                                if not type_sale_data.empty:
                                    # 아이템 인덱스별 판매 횟수 계산
                                    index_counts = type_sale_data['item_index'].value_counts().head(10)

                                    if len(index_counts) > 0:
                                        # Rank 로그 유형일 때만 라운드 정보 표시
                                        if log_type == "Rank":
                                            # 각 아이템 인덱스별로 가장 많이 판매한 라운드 계산
                                            round_info = {}
                                            for item_index in index_counts.index:
                                                item_data = type_sale_data[type_sale_data['item_index'] == item_index]
                                                round_counts = item_data['round_index'].value_counts()
                                                if len(round_counts) > 0:
                                                    most_frequent_round = round_counts.index[0]
                                                    round_count = round_counts.iloc[0]
                                                    round_info[item_index] = f"{most_frequent_round} ({round_count}번)"
                                                else:
                                                    round_info[item_index] = '-'

                                            # 한국어 이름으로 변환
                                            item_names = []
                                            for idx in index_counts.index:
                                                item_names.append(get_item_name_by_type(item_type, idx))

                                            index_df = pd.DataFrame({
                                                '아이템': item_names,
                                                '판매 횟수': index_counts.values,
                                                '주요 라운드': [round_info.get(idx, '-') for idx in index_counts.index]
                                            })
                                        else:
                                            # 한국어 이름으로 변환
                                            item_names = []
                                            for idx in index_counts.index:
                                                item_names.append(get_item_name_by_type(item_type, idx))

                                            index_df = pd.DataFrame({
                                                '아이템': item_names,
                                                '판매 횟수': index_counts.values
                                            })

                                        st.dataframe(index_df, use_container_width=True)
                                        st.caption(f"상위 {len(index_df)}개 표시")
                                    else:
                                        st.info(f"{item_type} 판매 데이터가 없습니다.")
                                else:
                                    st.info(f"{item_type} 판매 데이터가 없습니다.")
                            else:
                                st.info("원본 데이터가 없습니다.")
                else:
                    # 아이템 타입이 1개인 경우
                    item_type = item_types[0]
                    korean_type_name = get_shop_item_type_korean(item_type)
                    st.subheader(f"📦 {korean_type_name} 판매 상세")
                    if 'raw_data' in shop_summary and not shop_summary['raw_data'].empty:
                        type_sale_data = shop_summary['raw_data'][
                            (shop_summary['raw_data']['transaction_type'] == '판매') &
                            (shop_summary['raw_data']['item_type'] == item_type)
                        ]

                        if not type_sale_data.empty:
                            index_counts = type_sale_data['item_index'].value_counts().head(10)

                            if len(index_counts) > 0:
                                # Rank 로그 유형일 때만 라운드 정보 표시
                                if log_type == "Rank":
                                    # 각 아이템 인덱스별로 가장 많이 판매한 라운드 계산
                                    round_info = {}
                                    for item_index in index_counts.index:
                                        item_data = type_sale_data[type_sale_data['item_index'] == item_index]
                                        round_counts = item_data['round_index'].value_counts()
                                        if len(round_counts) > 0:
                                            most_frequent_round = round_counts.index[0]
                                            round_count = round_counts.iloc[0]
                                            round_info[item_index] = f"{most_frequent_round} ({round_count}번)"
                                        else:
                                            round_info[item_index] = '-'

                                    # 한국어 이름으로 변환
                                    item_names = []
                                    for idx in index_counts.index:
                                        item_names.append(get_item_name_by_type(item_type, idx))

                                    index_df = pd.DataFrame({
                                        '아이템': item_names,
                                        '판매 횟수': index_counts.values,
                                        '주요 라운드': [round_info.get(idx, '-') for idx in index_counts.index]
                                    })
                                else:
                                    # 한국어 이름으로 변환
                                    item_names = []
                                    for idx in index_counts.index:
                                        item_names.append(get_item_name_by_type(item_type, idx))

                                    index_df = pd.DataFrame({
                                        '아이템': item_names,
                                        '판매 횟수': index_counts.values
                                    })

                                st.dataframe(index_df, use_container_width=True)
                                st.caption(f"상위 {len(index_df)}개 표시")
                            else:
                                st.info(f"{item_type} 판매 데이터가 없습니다.")
                        else:
                            st.info(f"{item_type} 판매 데이터가 없습니다.")
                    else:
                        st.info("원본 데이터가 없습니다.")
        else:
            st.info("판매 데이터가 없습니다.")





def convert_log_to_markdown_extended(df, game_info, file_name, show_details=True, use_payload_filtering=True):
    """확장된 로그 데이터를 Mind Map용 마크다운으로 변환 - item 형식 (레벨 제한 없음)"""
    # 파일명에서 간단한 이름 추출
    simple_name = file_name.split('/')[-1] if '/' in file_name else file_name

    # Markmap frontmatter 추가 (레벨 제한 없음, 초기 레벨 2)
    markdown_lines = [
        "---",
        "title: 확장 배틀 로그 분석",
        "markmap:",
        "  colorFreezeLevel: 3",
        "  maxWidth: 350",
        "  initialExpandLevel: 2",
        "---"
    ]

    # 메인 타이틀
    markdown_lines.append(f"# 📘 배틀 로그: {simple_name}")

    # 게임 정보 섹션 - item 형식으로
    if game_info and any(v is not None for v in game_info.values()):
        markdown_lines.append("## 🎮 게임 정보 <!-- markmap: fold -->")

        if game_info.get("userID"):
            markdown_lines.append(f"- 👤 User ID: {game_info['userID']}")
        if game_info.get("userLevel"):
            markdown_lines.append(f"- 📊 User Level: {game_info['userLevel']}")
        if game_info.get("seed"):
            markdown_lines.append(f"- 🌱 Seed: {game_info['seed']}")

    # 데이터프레임에서 직접 게임 정보 추출 (첫 번째 행에서)
    if len(df) > 0:
        first_row = df.iloc[0]
        additional_info = []

        # 추가 게임 정보 필드들
        info_fields = {
            'battleState': '⚔️ 배틀 상태',
            'currentMonsterID': '🐉 현재 몬스터 ID',
            'coin': '💰 코인',
            'dollarUse': '💵 달러 사용',
            'handCountMax': '🃏 최대 핸드 수',
            'jokerCountMax': '🎭 최대 조커 수',
            'itemCountMax': '📦 최대 아이템 수',
            'attackCountMax': '⚔️ 최대 공격 수',
            'throwCountMax': '🎯 최대 버리기 수'
        }

        for field, label in info_fields.items():
            if field in first_row and first_row[field] and str(first_row[field]).strip():
                additional_info.append(f"- {label}: {first_row[field]}")

        if additional_info:
            if "## 🎮 게임 정보 <!-- markmap: fold -->" not in markdown_lines:
                markdown_lines.append("## 🎮 게임 정보 <!-- markmap: fold -->")
            markdown_lines.extend(additional_info)

    # 라운드별 로그 섹션 - item 형식으로
    markdown_lines.append("## 📋 라운드별 로그 <!-- markmap: fold -->")

    # 컬럼명 확인 및 매핑
    round_col = None
    order_col = None
    event_type_col = None
    event_order_col = None

    # 라운드 컬럼 찾기
    if "round_index" in df.columns:
        round_col = "round_index"
    elif "라운드" in df.columns:
        round_col = "라운드"

    # 행동 순서 컬럼 찾기
    if "order" in df.columns:
        order_col = "order"
    elif "행동 순서" in df.columns:
        order_col = "행동 순서"

    # 이벤트 타입 컬럼 찾기
    if "event_type" in df.columns:
        event_type_col = "event_type"
    elif "이벤트 타입" in df.columns:
        event_type_col = "이벤트 타입"

    # 이벤트 순서 컬럼 찾기
    if "event_type_order" in df.columns:
        event_order_col = "event_type_order"
    elif "로그 순서" in df.columns:
        event_order_col = "로그 순서"

    if round_col and round_col in df.columns:
        # 라운드별로 그룹화 - 헤더 + item 구조
        for round_num in sorted(df[round_col].unique()):
            round_df = df[df[round_col] == round_num]
            markdown_lines.append(f"### 🎯 라운드 {round_num}")

            # 행동 순서별로 그룹화
            if order_col and order_col in df.columns:
                for order_num in sorted(round_df[order_col].unique()):
                    order_df = round_df[round_df[order_col] == order_num]
                    markdown_lines.append(f"- ⚡ 행동 {order_num}")

                    # 각 이벤트 표시 - item 형식
                    for _, row in order_df.iterrows():
                        event_type = row.get(event_type_col, "UNKNOWN") if event_type_col else "UNKNOWN"
                        log_order = row.get(event_order_col, "") if event_order_col else ""

                        markdown_lines.append(f"  - 📝 이벤트 {log_order}: {event_type}")

                        # 페이로드 처리 (이벤트별로) - item 형식
                        payload_keys_str = row.get("payload_keys", "")
                        payload_types_str = row.get("payload_types", "")
                        scalar_values = row.get("scalarValues", {})
                        reference_values = row.get("referenceValues", {})

                        if payload_keys_str and str(payload_keys_str).strip():
                            # 페이로드 키와 타입을 파싱
                            payload_keys = [k.strip() for k in str(payload_keys_str).split(",") if k.strip()]

                            # 타입 딕셔너리 생성
                            type_dict = {}
                            if payload_types_str and str(payload_types_str).strip():
                                type_pairs = [t.strip() for t in str(payload_types_str).split(",") if t.strip()]
                                for pair in type_pairs:
                                    if ":" in pair:
                                        key, type_val = pair.split(":", 1)
                                        type_dict[key.strip()] = type_val.strip()

                            # 각 페이로드 키를 item으로 처리 (PayloadType 기반 필터링 적용)
                            for key in payload_keys:
                                # PayloadType 정보 가져오기
                                payload_type = type_dict.get(key, "")

                                # PayloadType에 따라 실제 사용되는 데이터만 필터링 (옵션에 따라)
                                key_scalar_values = scalar_values.get(key, {}) if scalar_values else {}
                                key_reference_values = reference_values.get(key, {}) if reference_values else {}

                                if use_payload_filtering:
                                    active_scalar, active_reference = get_active_payload_data(
                                        payload_type, key_scalar_values, key_reference_values
                                    )
                                else:
                                    # 필터링 비활성화시 모든 데이터 사용
                                    active_scalar = key_scalar_values
                                    active_reference = key_reference_values

                                # 필터링 후 데이터가 없으면 해당 키 자체를 숨김
                                if use_payload_filtering and not active_scalar and not active_reference:
                                    continue

                                # 페이로드 키 표시
                                markdown_lines.append(f"    - 🔑 {key}")

                                # PayloadType 표시 (필터링 비활성화시에만)
                                if payload_type and not use_payload_filtering:
                                    markdown_lines.append(f"      - 🏷️ 타입: {payload_type}")

                                # 활성 스칼라 값 표시 (중간 레벨 제거)
                                if active_scalar:
                                    # 스칼라 값의 각 필드를 item으로
                                    for field_name, field_value in active_scalar.items():
                                        if field_value is not None and str(field_value).strip():
                                            if field_name == "cardIndices" and isinstance(field_value, list):
                                                # repeated int32 cardIndices 처리
                                                if field_value:
                                                    markdown_lines.append(f"      - 🎯 카드인덱스 ({len(field_value)}개)")
                                                    for i, card_idx in enumerate(field_value[:5]):  # 최대 5개만
                                                        card_name = get_playing_card_name(card_idx)
                                                        markdown_lines.append(f"        - 📇 {card_name}")
                                            else:
                                                # 일반 스칼라 값
                                                icon = get_field_icon(field_name)
                                                markdown_lines.append(f"      - {icon} {field_name}: {field_value}")

                                # 활성 레퍼런스 값 표시 (중간 레벨 제거)
                                if active_reference:
                                    # 레퍼런스 값을 item 형식으로 처리
                                    add_reference_items(active_reference, markdown_lines, "      ")

                                # 데이터가 없는 경우 정보 표시 (필터링 비활성화시에만)
                                if not use_payload_filtering and not active_scalar and not active_reference:
                                    if payload_type:
                                        markdown_lines.append(f"      - ⚠️ {payload_type} 타입 데이터 없음")
                                    else:
                                        markdown_lines.append(f"      - ❓ 타입 및 데이터 정보 없음")

                        # 기본 정보를 item 형식으로 표시
                        bookmark_col = "bookmarkDataIndex" if "bookmarkDataIndex" in df.columns else "책갈피 IDX"
                        joker_col = "jokerDataIndex" if "jokerDataIndex" in df.columns else "조커 IDX"
                        hp_col = "monsterCurrentHP" if "monsterCurrentHP" in df.columns else "몬스터 체력"
                        max_hp_col = "monsterMaxHP" if "monsterMaxHP" in df.columns else "몬스터 최대 체력"
                        mult_col = "multiple" if "multiple" in df.columns else "배수"
                        chip_col = "chip" if "chip" in df.columns else "공격력"

                        if row.get(bookmark_col) and str(row[bookmark_col]).strip():
                            bookmark_idx = int(row[bookmark_col]) if str(row[bookmark_col]).isdigit() else row[bookmark_col]
                            bookmark_name = get_bookmark_name(bookmark_idx) if isinstance(bookmark_idx, int) else f"책갈피 {bookmark_idx}"
                            markdown_lines.append(f"    - 📖 책갈피: {bookmark_name}")

                        if row.get(joker_col) and str(row[joker_col]).strip():
                            joker_idx = int(row[joker_col]) if str(row[joker_col]).isdigit() else row[joker_col]
                            joker_name = get_joker_name(joker_idx) if isinstance(joker_idx, int) else f"조커 {joker_idx}"
                            markdown_lines.append(f"    - 🃏 조커: {joker_name}")

                        if row.get(hp_col) and str(row[hp_col]).strip():
                            max_hp = row.get(max_hp_col, "")
                            if max_hp and str(max_hp).strip():
                                markdown_lines.append(f"    - 🐉 몬스터 체력: {row[hp_col]}/{max_hp}")
                            else:
                                markdown_lines.append(f"    - 🐉 몬스터 체력: {row[hp_col]}")

                        if row.get(mult_col) and str(row[mult_col]).strip():
                            markdown_lines.append(f"    - ✖️ 배수: {row[mult_col]}")

                        if row.get(chip_col) and str(row[chip_col]).strip():
                            markdown_lines.append(f"    - ⚔️ 공격력: {row[chip_col]}")



                        # 확장된 정보들을 간단한 item 형식으로 표시
                        if show_details:
                            if row.get("source") and str(row["source"]).strip():
                                markdown_lines.append(f"    - 📍 소스: {row['source']}")

                        # Evaluate Result 정보를 간단한 item 형식으로 표시
                        eval_col = "evaluateResult" if "evaluateResult" in df.columns else "평가 결과"
                        if row.get(eval_col) and isinstance(row[eval_col], dict):
                            eval_result = row[eval_col]
                            eval_info = []

                            if eval_result.get("chips"):
                                eval_info.append(f"칩: {eval_result['chips']}")
                            if eval_result.get("mult"):
                                eval_info.append(f"배수: {eval_result['mult']}")
                            if eval_result.get("pDollars"):
                                eval_info.append(f"달러: {eval_result['pDollars']}")

                            if eval_info:
                                markdown_lines.append(f"    - 🎯 평가 결과: {', '.join(eval_info)}")
            else:
                # 행동 순서가 없는 경우 이벤트만 표시 - item 형식
                for _, row in round_df.iterrows():
                    event_type = row.get(event_type_col, "UNKNOWN") if event_type_col else "UNKNOWN"
                    log_order = row.get(event_order_col, "") if event_order_col else ""
                    markdown_lines.append(f"- 📝 이벤트 {log_order}: {event_type}")
    else:
        # 라운드 정보가 없는 경우 이벤트 타입별로 그룹화 - item 형식
        markdown_lines.append("### 📝 이벤트 목록")

        # 이벤트 타입 컬럼 찾기
        event_type_col = None
        if "event_type" in df.columns:
            event_type_col = "event_type"
        elif "이벤트 타입" in df.columns:
            event_type_col = "이벤트 타입"

        if event_type_col:
            for event_type in df[event_type_col].unique():
                event_df = df[df[event_type_col] == event_type]
                count = len(event_df)
                markdown_lines.append(f"- {event_type} ({count}회)")

                # 각 이벤트 타입의 상세 정보 요약
                if count <= 5:  # 5개 이하인 경우만 상세 표시
                    for _, row in event_df.iterrows():
                        details = []

                        # 컬럼명 매핑 고려
                        bookmark_col = "bookmarkDataIndex" if "bookmarkDataIndex" in df.columns else "책갈피 IDX"
                        joker_col = "jokerDataIndex" if "jokerDataIndex" in df.columns else "조커 IDX"

                        if row.get(bookmark_col) and str(row[bookmark_col]).strip():
                            details.append(f"책갈피: {row[bookmark_col]}")
                        if row.get(joker_col) and str(row[joker_col]).strip():
                            details.append(f"조커: {row[joker_col]}")

                        # Payload 정보도 추가
                        if row.get("payload_keys") and str(row["payload_keys"]).strip():
                            details.append(f"페이로드: {row['payload_keys']}")

                        if details:
                            markdown_lines.append(f"  - {', '.join(details)}")
        else:
            markdown_lines.append("- ⚠️ 이벤트 타입 정보를 찾을 수 없습니다")
            # 디버깅을 위해 사용 가능한 컬럼 표시
            markdown_lines.append(f"  - 사용 가능한 컬럼: {', '.join(df.columns.tolist()[:10])}")

    # 성능 최적화: 문자열 결합 최적화
    return "\n".join(markdown_lines)



if __name__ == "__main__":
    main()
