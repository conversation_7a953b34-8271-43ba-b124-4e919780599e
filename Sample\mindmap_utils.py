"""
마인드맵 유틸리티 모듈

이 모듈은 로그 데이터를 마인드맵 형태의 마크다운으로 변환하는 기능을 제공합니다.
"""

import pandas as pd
from typing import Dict, List
from data_utils import get_field_icon, format_data_value
from config import MINDMAP_SETTINGS, EVENT_TYPE_ICONS


class MindmapGenerator:
    """마인드맵 생성을 담당하는 클래스"""
    
    def __init__(self, show_details: bool = True, use_payload_filtering: bool = True):
        self.show_details = show_details
        self.use_payload_filtering = use_payload_filtering
        self.empty_suffix = MINDMAP_SETTINGS["empty_data_suffix"]
    
    def convert_log_to_markdown(self, df: pd.DataFrame, game_info: Dict, 
                               selected_log: str) -> str:
        """로그 데이터를 마인드맵용 마크다운으로 변환합니다."""
        lines = []
        
        # 헤더 추가
        lines.append(f"# 🎮 ACE Battle Log Analysis")
        lines.append(f"## 📁 {selected_log.split('/')[-1]}")
        
        # 게임 정보 추가
        if game_info:
            lines.extend(self._generate_game_info_section(game_info))
        
        # 라운드별 데이터 처리
        if not df.empty:
            lines.extend(self._generate_rounds_section(df))
        else:
            lines.append("## ⚠️ 데이터 없음")
            lines.append("- 파싱된 로그 데이터가 없습니다.")
        
        return "\n".join(lines)
    
    def _generate_game_info_section(self, game_info: Dict) -> List[str]:
        """게임 정보 섹션을 생성합니다."""
        lines = []
        lines.append("## 🎯 게임 정보")
        
        for key, value in game_info.items():
            if value is not None:
                icon = get_field_icon(key)
                formatted_value = format_data_value(value, key)
                lines.append(f"- {icon} **{key}**: {formatted_value}")
        
        return lines
    
    def _generate_rounds_section(self, df: pd.DataFrame) -> List[str]:
        """라운드별 섹션을 생성합니다."""
        lines = []
        
        # 라운드별로 그룹화
        if '라운드' in df.columns:
            grouped = df.groupby('라운드')
            
            for round_num, round_data in grouped:
                lines.append(f"## 🔄 라운드 {round_num}")
                lines.extend(self._generate_round_events(round_data))
        else:
            lines.append("## 📊 전체 이벤트")
            lines.extend(self._generate_events_list(df))
        
        return lines
    
    def _generate_round_events(self, round_data: pd.DataFrame) -> List[str]:
        """라운드 내 이벤트들을 생성합니다."""
        lines = []
        
        # 행동 순서별로 정렬
        if '행동 순서' in round_data.columns:
            sorted_data = round_data.sort_values('행동 순서')
        else:
            sorted_data = round_data
        
        for idx, row in sorted_data.iterrows():
            event_lines = self._generate_event_item(row, idx)
            lines.extend(event_lines)
        
        return lines
    
    def _generate_events_list(self, df: pd.DataFrame) -> List[str]:
        """이벤트 리스트를 생성합니다."""
        lines = []
        
        for idx, row in df.iterrows():
            event_lines = self._generate_event_item(row, idx)
            lines.extend(event_lines)
        
        return lines
    
    def _generate_event_item(self, row: pd.Series, idx: int) -> List[str]:
        """개별 이벤트 아이템을 생성합니다."""
        lines = []
        
        # 이벤트 타입 확인
        event_type = row.get('이벤트 타입', 'unknown')
        icon = EVENT_TYPE_ICONS.get(event_type, "🔹")
        
        # 이벤트 제목
        title = f"{icon} 이벤트 {idx + 1}"
        if event_type != 'unknown':
            title += f" ({event_type})"
        
        lines.append(f"### {title}")
        
        # 기본 정보
        for col, value in row.items():
            if pd.notna(value) and value != "":
                icon = get_field_icon(col)
                formatted_value = format_data_value(value, col)
                lines.append(f"- {icon} **{col}**: {formatted_value}")
        
        return lines
    



# 편의 함수들
def convert_log_to_markdown_extended(df: pd.DataFrame, game_info: Dict, 
                                   selected_log: str, show_details: bool = True,
                                   use_payload_filtering: bool = True) -> str:
    """확장된 로그를 마인드맵 마크다운으로 변환합니다."""
    generator = MindmapGenerator(show_details, use_payload_filtering)
    return generator.convert_log_to_markdown(df, game_info, selected_log)









