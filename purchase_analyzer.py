#!/usr/bin/env python3
"""
유료 구매 시점 분석 모듈

IAP 구매 로그를 기준으로 구매 전 2분간의 사용자 동선을 분석합니다.
"""

import pandas as pd
import json
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
from collections import Counter
from concurrent.futures import ThreadPoolExecutor, as_completed
import os
import pickle
import hashlib
import zstandard as zstd
import io
import sys
from s3_utils import download_multiple_s3_files

# 로깅 설정
logger = logging.getLogger(__name__)

class LogCapture:
    """터미널 출력을 캡처하는 클래스"""
    def __init__(self):
        self.logs = []
        self.original_stdout = sys.stdout

    def __enter__(self):
        sys.stdout = self
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        sys.stdout = self.original_stdout

    def write(self, text):
        # 원래 stdout에도 출력
        self.original_stdout.write(text)
        self.original_stdout.flush()
        # 로그에도 저장
        if text.strip():  # 빈 줄은 제외
            self.logs.append(text.strip())

    def flush(self):
        self.original_stdout.flush()

    def get_logs(self):
        return self.logs

    def save_to_file(self, filename: str):
        """캡처된 로그를 파일로 저장"""
        try:
            # logs 디렉토리가 없으면 생성
            log_dir = "logs"
            if not os.path.exists(log_dir):
                os.makedirs(log_dir)

            # 타임스탬프를 포함한 파일명 생성
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            if not filename.endswith('.txt'):
                filename = f"{filename}_{timestamp}.txt"
            else:
                filename = f"{filename[:-4]}_{timestamp}.txt"

            filepath = os.path.join(log_dir, filename)

            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(f"구매 분석 로그 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write("=" * 80 + "\n\n")

                for log in self.logs:
                    f.write(log + "\n")

            print(f"📄 분석 로그가 저장되었습니다: {filepath}")
            return filepath

        except Exception as e:
            print(f"❌ 로그 저장 실패: {e}")
            return None
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

def get_cache_key(s3_files: List[str], max_files: Optional[int] = None) -> str:
    """
    S3 파일 리스트와 설정으로부터 캐시 키를 생성합니다.
    """
    # 파일 리스트와 max_files, 분석 로직 버전을 조합해서 해시 생성
    cache_data = {
        'files': sorted(s3_files[:max_files] if max_files else s3_files),
        'max_files': max_files,
        'analysis_version': '2.1',  # 분석 로직 변경 시 버전 업데이트
        'minutes_before': 2,  # 분석 시간 기준
        'filtering_logic': 'v2.1'  # 필터링 로직 버전
    }
    cache_str = json.dumps(cache_data, sort_keys=True)
    return hashlib.md5(cache_str.encode()).hexdigest()

def get_file_cache_key(s3_file: str) -> str:
    """
    개별 파일에 대한 캐시 키를 생성합니다.
    """
    cache_data = {
        'file': s3_file,
        'analysis_version': '2.1',
        'minutes_before': 2,
        'filtering_logic': 'v2.1'
    }
    cache_str = json.dumps(cache_data, sort_keys=True)
    return hashlib.md5(cache_str.encode()).hexdigest()

def get_file_cache_path(s3_file: str, cache_dir: str = "cache") -> str:
    """
    개별 파일 캐시 경로를 반환합니다.
    """
    os.makedirs(cache_dir, exist_ok=True)
    cache_key = get_file_cache_key(s3_file)
    return os.path.join(cache_dir, f"file_analysis_{cache_key}.pkl")

def save_file_analysis_cache(s3_file: str, analysis_result: List[Dict], cache_dir: str = "cache"):
    """
    개별 파일의 분석 결과를 캐시에 저장합니다.
    """
    try:
        cache_path = get_file_cache_path(s3_file, cache_dir)
        with open(cache_path, 'wb') as f:
            pickle.dump(analysis_result, f)
        logger.debug(f"파일 분석 결과 캐시 저장: {os.path.basename(s3_file)}")
    except Exception as e:
        logger.error(f"파일 캐시 저장 실패 {s3_file}: {e}")

def load_file_analysis_cache(s3_file: str, cache_dir: str = "cache") -> Optional[List[Dict]]:
    """
    개별 파일의 분석 결과를 캐시에서 로드합니다.
    """
    try:
        cache_path = get_file_cache_path(s3_file, cache_dir)
        if os.path.exists(cache_path):
            with open(cache_path, 'rb') as f:
                result = pickle.load(f)
            logger.debug(f"파일 분석 결과 캐시 로드: {os.path.basename(s3_file)}")
            return result
    except Exception as e:
        logger.error(f"파일 캐시 로드 실패 {s3_file}: {e}")
    return None

def get_cache_path(cache_key: str, cache_dir: str = "cache") -> str:
    """
    캐시 파일 경로를 반환합니다.
    """
    os.makedirs(cache_dir, exist_ok=True)
    return os.path.join(cache_dir, f"purchase_analysis_{cache_key}.pkl")

def save_analysis_cache(analysis_result: Dict, cache_key: str, cache_dir: str = "cache"):
    """
    분석 결과를 캐시에 저장합니다.
    """
    try:
        cache_path = get_cache_path(cache_key, cache_dir)
        with open(cache_path, 'wb') as f:
            pickle.dump(analysis_result, f)
        logger.info(f"분석 결과 캐시 저장: {cache_path}")
    except Exception as e:
        logger.error(f"캐시 저장 실패: {e}")

def load_analysis_cache(cache_key: str, cache_dir: str = "cache") -> Optional[Dict]:
    """
    캐시에서 분석 결과를 로드합니다.
    """
    try:
        cache_path = get_cache_path(cache_key, cache_dir)
        if os.path.exists(cache_path):
            with open(cache_path, 'rb') as f:
                result = pickle.load(f)
            logger.info(f"캐시에서 분석 결과 로드: {cache_path}")
            return result
    except Exception as e:
        logger.error(f"캐시 로드 실패: {e}")
    return None

def clear_old_purchase_cache(cache_dir: str = "cache"):
    """
    오래된 구매 분석 캐시를 정리합니다.
    """
    try:
        if os.path.exists(cache_dir):
            for filename in os.listdir(cache_dir):
                if filename.startswith("purchase_analysis_") and filename.endswith(".pkl"):
                    file_path = os.path.join(cache_dir, filename)
                    os.remove(file_path)
                    logger.info(f"오래된 캐시 파일 삭제: {file_path}")
    except Exception as e:
        logger.error(f"캐시 정리 실패: {e}")

def parse_log_file_for_purchase_analysis(file_path: str) -> List[Dict]:
    """
    로그 파일을 파싱하여 구매 분석에 필요한 데이터를 추출합니다.
    기존 log_parser.py의 process_log_file 함수를 활용합니다.

    Args:
        file_path: 로그 파일 경로 (S3 키 또는 로컬 경로)

    Returns:
        파싱된 로그 데이터 리스트
    """
    try:
        from log_parser import process_log_file

        # 기존 log_parser의 process_log_file 함수 사용
        df = process_log_file(file_path)

        if df.empty:
            return []

        # DataFrame을 Dict 리스트로 변환
        logs = []

        # payload_time만 사용 (항상 올바른 값)
        if 'payload_time' not in df.columns:
            logger.error(f"파일 {os.path.basename(file_path)}에 payload_time 컬럼이 없습니다")
            return []

        for _, row in df.iterrows():
            # payload_time만 사용
            time_value = None
            if 'payload_time' in row and pd.notna(row['payload_time']):
                time_value = row['payload_time']
            else:
                # payload_time이 없는 로그는 건너뛰기
                continue

            # 시간이 문자열인 경우 datetime으로 변환
            if isinstance(time_value, str):
                try:
                    from datetime import datetime
                    # ISO 형식 시간 파싱
                    if 'T' in time_value:
                        time_value = datetime.fromisoformat(time_value.replace('Z', '+00:00'))
                    else:
                        time_value = datetime.strptime(time_value, '%Y-%m-%d %H:%M:%S')
                except Exception as e:
                    time_value = None

            # pandas nan 값을 None으로 변환하는 헬퍼 함수
            def clean_value(value):
                if pd.isna(value) or str(value).strip() == 'nan':
                    return None
                return value

            log_data = {
                'time': time_value,
                'event_type': clean_value(row.get('event_type')),
                'user_id': clean_value(row.get('user_id')) or clean_value(row.get('userUUID'))
            }

            # payload 관련 필드들 추가 (nan 값 처리)
            for col in df.columns:
                if col.startswith('payload_'):
                    log_data[col] = clean_value(row.get(col))

            logs.append(log_data)

        return logs

    except Exception as e:
        logger.error(f"파일 파싱 실패: {os.path.basename(file_path)}, 오류: {e}")
        return []

def parse_single_file_wrapper(file_path: str) -> tuple:
    """
    병렬 처리를 위한 단일 파일 파싱 래퍼

    Returns:
        (file_path, logs, purchase_logs)
    """
    try:
        logs = parse_log_file_for_purchase_analysis(file_path)
        # 파일명에서 userID 추출 및 할당
        logs_with_user_id = assign_user_id_from_filename(logs, file_path)
        purchase_logs = find_purchase_logs(logs_with_user_id)
        return (file_path, logs_with_user_id, purchase_logs)
    except Exception as e:
        logger.error(f"파일 처리 실패: {os.path.basename(file_path)}, 오류: {e}")
        return (file_path, [], [])

def find_purchase_logs(logs: List[Dict]) -> List[Dict]:
    """
    IAP 구매 로그를 찾습니다. (성능 최적화)

    Args:
        logs: 파싱된 로그 데이터 리스트

    Returns:
        구매 로그 리스트
    """
    purchase_logs = []
    a_param_count = 0
    raw_count = 0

    for log in logs:
        # payload_a_param에 "IAP"가 포함된 로그 찾기
        if 'payload_a_param' in log and log['payload_a_param']:
            a_param_count += 1
            try:
                a_param = str(log['payload_a_param'])
                if 'IAP' in a_param:
                    purchase_logs.append(log)
            except:
                continue
        # payload_raw에서도 IAP 검색 (JSON 파싱 실패한 경우)
        elif 'payload_raw' in log and log['payload_raw']:
            raw_count += 1
            try:
                if 'IAP' in str(log['payload_raw']):
                    purchase_logs.append(log)
            except:
                continue



    if purchase_logs:
        purchase_msg = f"💰 구매 로그 발견: {len(purchase_logs)}개"
        print(purchase_msg)
        logger.info(purchase_msg)

        # 구매 로그 상세 정보 출력 (디버깅)
        print(f"🔍 구매 로그 상세 정보:")
        for i, purchase_log in enumerate(purchase_logs):
            purchase_time = purchase_log.get('time')
            user_id = purchase_log.get('user_id')
            iap_product_id = None

            # iapProductID 추출
            try:
                a_param = purchase_log.get('payload_a_param', '')
                if isinstance(a_param, str) and 'iapProductID' in a_param:
                    import json
                    if a_param.startswith('{'):
                        data = json.loads(a_param)
                        iap_product_id = data.get('iapProductID', 'Unknown')
            except:
                pass

            print(f"  구매 #{i+1}: 시간={purchase_time}, 사용자={user_id}, 상품={iap_product_id}")
    else:
        no_purchase_msg = "❌ 구매 로그를 찾을 수 없음"
        logger.debug(no_purchase_msg)
    return purchase_logs

def detect_login_sessions(logs: List[Dict]) -> List[Dict]:
    """
    로그 리스트에서 로그인 세션을 감지하고 세션 정보를 추가합니다.

    Args:
        logs: 시간 순서대로 정렬된 로그 리스트

    Returns:
        세션 정보가 추가된 로그 리스트
    """
    if not logs:
        return []

    logger.info(f"로그인 세션 감지 시작: {len(logs)}개 로그")

    # 로그인 이벤트 찾기
    login_events = []
    for i, log in enumerate(logs):
        event_type = log.get('event_type', '')
        if event_type == 'MAF_ANALYTICS_LOG_EVENT_TYPE_LOG_LOGIN':
            user_id = log.get('user_id') or log.get('userId') or log.get('USER_ID')
            login_time = log.get('time')
            if user_id and login_time:
                login_events.append({
                    'index': i,
                    'user_id': user_id,
                    'login_time': login_time,
                    'log': log
                })

    logger.info(f"로그인 이벤트 발견: {len(login_events)}개")

    # 세션 정보를 추가한 로그 리스트 생성
    session_logs = []
    current_session_id = None
    current_user_id = None
    current_login_time = None

    login_event_index = 0

    for i, log in enumerate(logs):
        log_copy = log.copy()

        # 현재 위치가 로그인 이벤트인지 확인
        if (login_event_index < len(login_events) and
            i == login_events[login_event_index]['index']):

            # 새로운 세션 시작
            login_event = login_events[login_event_index]
            current_session_id = f"session_{login_event['user_id']}_{login_event['login_time'].strftime('%Y%m%d_%H%M%S')}"
            current_user_id = login_event['user_id']
            current_login_time = login_event['login_time']

            logger.debug(f"새 세션 시작: {current_session_id}")
            login_event_index += 1

        # 세션 정보 추가
        if current_session_id:
            log_copy['session_id'] = current_session_id
            log_copy['session_user_id'] = current_user_id
            log_copy['session_login_time'] = current_login_time

        session_logs.append(log_copy)

    # 세션 통계
    session_counts = {}
    for log in session_logs:
        session_id = log.get('session_id')
        if session_id:
            session_counts[session_id] = session_counts.get(session_id, 0) + 1

    logger.info(f"세션 감지 완료: {len(session_counts)}개 세션, 평균 {sum(session_counts.values()) / len(session_counts) if session_counts else 0:.1f}개 로그/세션")

    return session_logs

def extract_user_id_from_filename(file_path: str) -> str:
    """
    파일 경로에서 userID를 추출합니다.
    파일명 패턴: {userID}_{시간정보}.pb

    Args:
        file_path: 파일 경로

    Returns:
        추출된 userID (없으면 None)
    """
    try:
        # 파일명만 추출
        filename = os.path.basename(file_path)

        # 확장자 제거
        if filename.endswith('.pb.zst'):
            filename = filename[:-7]  # .pb.zst 제거
        elif filename.endswith('.pb'):
            filename = filename[:-3]  # .pb 제거

        # 첫 번째 언더스코어 앞까지가 userID
        if '_' in filename:
            user_id = filename.split('_')[0]
            if user_id and user_id.strip():
                return user_id.strip()
    except Exception as e:
        logger.warning(f"파일명에서 userID 추출 실패: {file_path}, 오류: {e}")

    return None

def assign_user_id_from_filename(logs: List[Dict], file_path: str) -> List[Dict]:
    """
    파일명에서 추출한 userID를 모든 로그에 할당합니다.

    Args:
        logs: 로그 리스트
        file_path: 파일 경로

    Returns:
        userID가 할당된 로그 리스트
    """
    if not logs:
        return logs

    # 파일명에서 userID 추출
    file_user_id = extract_user_id_from_filename(file_path)

    if not file_user_id:
        logger.warning(f"파일명에서 userID를 추출할 수 없음: {file_path}")
        return logs

    logger.info(f"파일명에서 userID 추출: {file_user_id} (파일: {os.path.basename(file_path)})")

    # 모든 로그에 userID 할당
    updated_logs = []
    for log in logs:
        log_copy = log.copy()

        # 기존 user_id가 있는지 확인
        existing_user_id = log.get('user_id') or log.get('userId') or log.get('USER_ID')

        if existing_user_id and str(existing_user_id).strip() and str(existing_user_id) != 'nan':
            # 기존 userID가 있으면 파일명의 userID와 비교
            if str(existing_user_id).strip() != file_user_id:
                logger.debug(f"로그의 userID({existing_user_id})와 파일명의 userID({file_user_id})가 다름")

        # 파일명의 userID로 설정 (우선순위: 파일명 > 로그 내 userID)
        log_copy['user_id'] = file_user_id
        log_copy['_user_id_from_filename'] = True  # 파일명에서 추출했음을 표시

        updated_logs.append(log_copy)

    logger.info(f"파일명 기반 userID 할당 완료: {len(updated_logs)}개 로그에 userID '{file_user_id}' 할당")

    return updated_logs

def group_logs_by_session(logs: List[Dict]) -> Dict[str, List[Dict]]:
    """
    로그를 세션별로 그룹화합니다.

    Args:
        logs: 세션 정보가 포함된 로그 리스트

    Returns:
        세션 ID를 키로 하는 로그 그룹 딕셔너리
    """
    session_groups = {}
    logs_without_session = []

    for log in logs:
        session_id = log.get('session_id')
        if session_id:
            if session_id not in session_groups:
                session_groups[session_id] = []
            session_groups[session_id].append(log)
        else:
            logs_without_session.append(log)

    logger.info(f"세션별 그룹화 완료: {len(session_groups)}개 세션, {len(logs_without_session)}개 세션 없는 로그")

    # 세션별 통계 출력
    for session_id, session_logs in session_groups.items():
        logger.debug(f"세션 {session_id}: {len(session_logs)}개 로그")

    return session_groups

def find_purchase_session(purchase_log: Dict, session_groups: Dict[str, List[Dict]]) -> str:
    """
    구매 로그가 속한 세션을 찾습니다.

    Args:
        purchase_log: 구매 로그
        session_groups: 세션별 로그 그룹

    Returns:
        구매가 속한 세션 ID (없으면 None)
    """
    purchase_time = purchase_log.get('time')
    purchase_user_id = purchase_log.get('user_id') or purchase_log.get('userId') or purchase_log.get('USER_ID')

    if not purchase_time or not purchase_user_id:
        logger.warning(f"구매 로그에 시간 또는 사용자 ID가 없음: {purchase_log}")
        return None

    # 각 세션에서 구매 로그와 매칭되는 세션 찾기
    for session_id, session_logs in session_groups.items():
        if not session_logs:
            continue

        # 세션의 사용자 ID 확인
        session_user_id = session_logs[0].get('session_user_id')
        if session_user_id != purchase_user_id:
            continue

        # 세션의 시간 범위 확인
        session_start_time = session_logs[0].get('session_login_time')
        session_end_time = max(log.get('time') for log in session_logs if log.get('time'))

        if (session_start_time and session_end_time and
            session_start_time <= purchase_time <= session_end_time):
            logger.debug(f"구매 세션 발견: {session_id} (사용자: {purchase_user_id}, 시간: {purchase_time})")
            return session_id

    logger.warning(f"구매 세션을 찾을 수 없음: 사용자 {purchase_user_id}, 시간 {purchase_time}")
    return None

def analyze_pre_purchase_behavior(logs: List[Dict], purchase_log: Dict, minutes_before: int = None) -> Dict:
    """
    구매 전 사용자 행동을 세션 기반으로 분석합니다.

    Args:
        logs: 전체 로그 데이터
        purchase_log: 구매 로그
        minutes_before: 구매 전 분석할 시간 (분, None이면 모든 이전 로그)

    Returns:
        분석 결과
    """
    purchase_time = purchase_log.get('time')  # 파싱 시 payload_time이 time으로 저장됨

    # 구매 시간이 None인 경우 처리
    if purchase_time is None:
        # 현재 시간을 기본값으로 사용
        from datetime import datetime
        purchase_time = datetime.now()

    # 시간 제한 설정 (None이면 모든 이전 로그)
    if minutes_before is not None:
        start_time = purchase_time - timedelta(minutes=minutes_before)
        time_filter_msg = f"구매 전 {minutes_before}분간"
    else:
        start_time = None  # 시간 제한 없음
        time_filter_msg = "구매 시점 이전 모든"

    # 1단계: 파일명에서 userID 추출 및 할당 (logs는 이미 파일별로 처리되어야 함)
    # 주의: 이 함수는 단일 파일의 로그를 처리할 때 사용되어야 함
    # 다중 파일 로그의 경우 각 파일별로 미리 처리되어야 함
    tracked_logs = logs  # 이미 파일별로 userID가 할당되었다고 가정

    # 2단계: 로그인 세션 감지
    session_logs = detect_login_sessions(tracked_logs)

    # 3단계: 세션별 로그 그룹화
    session_groups = group_logs_by_session(session_logs)

    # 4단계: 구매가 속한 세션 찾기
    purchase_session_id = find_purchase_session(purchase_log, session_groups)

    if not purchase_session_id:
        logger.warning("구매 세션을 찾을 수 없어 전체 로그에서 분석합니다.")
        # 세션을 찾을 수 없는 경우 기존 방식으로 폴백
        session_logs_to_analyze = tracked_logs
    else:
        # 구매가 속한 세션의 로그만 사용
        session_logs_to_analyze = session_groups[purchase_session_id]

    # 5단계: 세션 내에서 구매 전 로그 필터링
    pre_purchase_logs = []
    for log in session_logs_to_analyze:
        try:
            # 시간 비교를 더 안전하게 처리 (payload_time 사용)
            log_time = log.get('time')  # 파싱 시 payload_time이 time으로 저장됨
            log_user_id = log.get('user_id') or log.get('userId') or log.get('USER_ID')
            purchase_user_id = purchase_log.get('user_id') or purchase_log.get('userId') or purchase_log.get('USER_ID')

            # 세션 기반 분석에서는 같은 세션 내의 로그만 고려
            # 세션이 있는 경우 세션 내 로그, 없는 경우 기존 user_id 매칭
            if purchase_session_id:
                # 세션 기반: 같은 세션 내의 로그만 고려
                session_match = log.get('session_id') == purchase_session_id
            else:
                # 폴백: 기존 user_id 매칭
                session_match = (log_user_id == purchase_user_id) or (log_user_id is None and purchase_user_id is None)

            # 시간 범위: 구매 전 로그 수집 (구매 시간 포함)
            # start_time이 None이면 모든 이전 로그 포함
            time_match = (log_time is not None and log_time <= purchase_time)  # 구매 시간 포함
            if start_time is not None:
                time_match = time_match and (start_time <= log_time)  # 시작 시간 이후만

            if session_match and time_match:
                pre_purchase_logs.append(log)
        except Exception as e:
            # 시간 비교 오류 시 로그 기록
            logger.warning(f"시간 비교 오류: {e}, log_time: {log.get('time')}, purchase_time: {purchase_time}")
            continue

    # 디버깅: 세션 기반 구매 전 로그 수 확인
    purchase_user_id = purchase_log.get('user_id') or purchase_log.get('userId') or purchase_log.get('USER_ID')
    print(f"🕐 세션 기반 필터링 결과:")
    print(f"  구매 사용자 ID: {purchase_user_id}")
    print(f"  구매 시간: {purchase_time}")

    if minutes_before is not None:
        print(f"  시작 시간 (구매 전 {minutes_before}분): {start_time}")
        time_desc = f"구매 전 {minutes_before}분간"
    else:
        print(f"  시작 시간: 제한 없음 (구매 시점 이전/동시 모든 로그)")
        time_desc = "구매 시점 이전/동시 모든"

    print(f"  구매 세션 ID: {purchase_session_id}")
    print(f"  전체 로그 수: {len(logs)}")
    print(f"  세션 내 로그 수: {len(session_logs_to_analyze)}")
    print(f"  {time_desc} 로그 수: {len(pre_purchase_logs)}")

    # 디버깅: 시간 필드 확인 및 필터링 로직 검증
    print(f"\n🔍 시간 필드 및 필터링 로직 디버깅:")
    print(f"  구매 시간: {purchase_time}")
    print(f"  시작 시간: {start_time}")

    # 세션 내 로그들의 시간 분포 상세 확인
    time_before_purchase = 0
    time_at_purchase = 0
    time_after_purchase = 0

    print(f"\n  세션 내 로그 시간 분석:")
    for i, log in enumerate(session_logs_to_analyze[:10]):  # 처음 10개만
        log_time = log.get('time')
        event_type = log.get('event_type', 'Unknown')

        if log_time:
            if log_time < purchase_time:
                time_before_purchase += 1
                status = "구매 전"
            elif log_time == purchase_time:
                time_at_purchase += 1
                status = "구매 시점"
            else:
                time_after_purchase += 1
                status = "구매 후"

            print(f"    로그 {i+1}: {log_time} ({status}) - {event_type}")
        else:
            print(f"    로그 {i+1}: 시간 없음 - {event_type}")

    print(f"\n  시간 분포 요약:")
    print(f"    구매 전: {time_before_purchase}개")
    print(f"    구매 시점: {time_at_purchase}개")
    print(f"    구매 후: {time_after_purchase}개")
    print(f"    필터링 결과: {len(pre_purchase_logs)}개 (구매 시점 포함)")

    # 세션 내 로그 시간 분포 확인
    if session_logs_to_analyze:
        # 세션 내 로그의 시간을 수집하여 분포 확인
        time_distribution = {}
        for log in session_logs_to_analyze:
            log_time = log.get('time')
            if log_time:
                time_str = str(log_time)
                time_distribution[time_str] = time_distribution.get(time_str, 0) + 1

        print(f"  세션 내 로그 시간 분포:")
        print(f"    총 {len(time_distribution)}개의 서로 다른 시간")

        # 시간별 로그 수 표시 (상위 5개)
        sorted_times = sorted(time_distribution.items(), key=lambda x: x[1], reverse=True)
        for i, (time_str, count) in enumerate(sorted_times[:5]):
            print(f"    시간 {i+1}: {time_str} ({count}개 로그)")

        print(f"  세션 내 로그 시간 샘플 (처음 3개):")
        for i, log in enumerate(session_logs_to_analyze[:3]):
            log_time = log.get('time')
            if start_time is not None:
                in_range = start_time <= log_time <= purchase_time if log_time else False
                range_desc = f"구매 전 {minutes_before}분 범위 내: {in_range}"
            else:
                in_range = log_time <= purchase_time if log_time else False
                range_desc = f"구매 시점 이전/동시: {in_range}"
            print(f"    로그 {i+1}: {log_time} ({range_desc})")

    if minutes_before is not None:
        time_desc = f"구매 전 {minutes_before}분간"
    else:
        time_desc = "구매 시점 이전/동시 모든"
    logger.debug(f"{time_desc} 로그 수: {len(pre_purchase_logs)}, 사용자: {purchase_user_id}, 구매시간: {purchase_time}, 시작시간: {start_time}")
    
    # UIShopPopup, UILoading, Toast 포함 UI 제외
    filtered_logs = []
    excluded_count = 0
    for log in pre_purchase_logs:
        ui_name = log.get('payload_ui_name', '') or ''
        event_type = log.get('event_type', '') or ''

        # 제외할 조건들
        should_exclude = False

        # 1. UIShopPopup 제외 (기존)
        if ui_name == 'UIShopPopup':
            should_exclude = True

        # 2. MAF_ANALYTICS_LOG_EVENT_TYPE_LOG_UI_OPEN에서 UILoading 제외
        elif event_type == 'MAF_ANALYTICS_LOG_EVENT_TYPE_LOG_UI_OPEN' and ui_name == 'UILoading':
            should_exclude = True

        # 3. MAF_ANALYTICS_LOG_EVENT_TYPE_LOG_UI_OPEN에서 Toast 포함 이름 제외
        elif event_type == 'MAF_ANALYTICS_LOG_EVENT_TYPE_LOG_UI_OPEN' and 'Toast' in ui_name:
            should_exclude = True

        if should_exclude:
            excluded_count += 1
        else:
            filtered_logs.append(log)

    # 디버깅: 필터링 결과 확인
    logger.debug(f"필터링 결과 - 전체: {len(pre_purchase_logs)}, 제외: {excluded_count}, 남은 로그: {len(filtered_logs)}")

    # 이벤트 타입별 집계
    event_counter = Counter()
    ui_counter = Counter()
    code_counter = Counter()

    def should_exclude_event(event_name: str, event_type: str = None) -> bool:
        """제외할 이벤트인지 판단"""
        if not event_name:
            return False

        event_lower = event_name.lower()

        # IAP/inapp 관련 이벤트 제외
        if 'iap' in event_lower or 'inapp' in event_lower:
            return True

        # Shop이 포함된 이벤트 제외
        if 'shop' in event_lower:
            return True

        # USER_CURRENCY_DATA 이벤트 타입 제외
        if event_type == 'MAF_ANALYTICS_LOG_EVENT_TYPE_USER_CURRENCY_DATA':
            return True
        
        if event_type == 'Unknown':
            return True

        return False

    for i, log in enumerate(filtered_logs):
        event_type = log.get('event_type', 'Unknown')

        # UI 이벤트 처리
        if 'payload_ui_name' in log and log['payload_ui_name']:
            ui_name = log['payload_ui_name']
            event_name = f"{ui_name}({event_type})"

            # IAP/inapp/Shop 관련 이벤트 및 USER_CURRENCY_DATA 제외
            if should_exclude_event(ui_name, event_type):
                continue

            ui_counter[event_name] += 1
            event_counter[event_name] += 1

        # MAF_ANALYTICS_LOG_EVENT_TYPE_LOG_SERVER_FUNCTION의 경우 payload_name 또는 payload_a_param의 code 사용
        elif event_type == 'MAF_ANALYTICS_LOG_EVENT_TYPE_LOG_SERVER_FUNCTION':
            function_name = None

            # 1. payload_name 우선 확인
            payload_name = log.get('payload_name')
            if payload_name and str(payload_name).strip() and str(payload_name) != 'nan':
                function_name = str(payload_name).strip()

            # 2. payload_name이 없으면 payload_a_param에서 직접 code 추출
            if not function_name:
                try:
                    payload_a_param = log.get('payload_a_param')
                    if payload_a_param and str(payload_a_param).strip() and str(payload_a_param) != 'nan':
                        import json
                        a_param_str = str(payload_a_param).strip()

                        # JSON 파싱해서 직접 code 추출
                        a_param_json = json.loads(a_param_str)
                        if isinstance(a_param_json, dict):
                            code_value = a_param_json.get('code')
                            if code_value and str(code_value).strip() and str(code_value) != 'nan':
                                function_name = str(code_value).strip()
                except Exception as e:
                    pass

            # 3. payload_code도 확인 (백업)
            if not function_name:
                payload_code = log.get('payload_code')
                if payload_code and str(payload_code).strip() and str(payload_code) != 'nan':
                    function_name = str(payload_code).strip()

            if function_name:
                event_name = f"{function_name}({event_type})"

                # IAP/inapp/Shop 관련 이벤트 및 USER_CURRENCY_DATA 제외
                if should_exclude_event(function_name, event_type):
                    continue

                code_counter[event_name] += 1
                event_counter[event_name] += 1
            else:
                # USER_CURRENCY_DATA 이벤트 타입 자체도 제외
                if should_exclude_event("", event_type):
                    continue

                event_counter[f"UnknownServerFunction({event_type})"] += 1

        # MAF_ANALYTICS_LOG_EVENT_TYPE_LOG_FUNNEL의 경우 payload_name 또는 payload_a_param의 code 사용
        elif event_type == 'MAF_ANALYTICS_LOG_EVENT_TYPE_LOG_FUNNEL':
            funnel_name = None

            # 1. payload_name 우선 확인
            payload_name = log.get('payload_name')
            if payload_name and str(payload_name).strip() and str(payload_name) != 'nan':
                funnel_name = str(payload_name).strip()

            # 2. payload_name이 없으면 payload_a_param에서 직접 code 추출
            if not funnel_name:
                try:
                    payload_a_param = log.get('payload_a_param')
                    if payload_a_param and str(payload_a_param).strip() and str(payload_a_param) != 'nan':
                        import json
                        a_param_str = str(payload_a_param).strip()

                        # JSON 파싱해서 직접 code 추출
                        a_param_json = json.loads(a_param_str)
                        if isinstance(a_param_json, dict):
                            code_value = a_param_json.get('code')
                            if code_value and str(code_value).strip() and str(code_value) != 'nan':
                                funnel_name = str(code_value).strip()
                except Exception as e:
                    pass

            # 3. payload_code도 확인 (백업)
            if not funnel_name:
                payload_code = log.get('payload_code')
                if payload_code and str(payload_code).strip() and str(payload_code) != 'nan':
                    funnel_name = str(payload_code).strip()

            if funnel_name:
                event_name = f"{funnel_name}({event_type})"

                # IAP/inapp/Shop 관련 이벤트 및 USER_CURRENCY_DATA 제외
                if should_exclude_event(funnel_name, event_type):
                    continue

                code_counter[event_name] += 1
                event_counter[event_name] += 1
            else:
                # USER_CURRENCY_DATA 이벤트 타입 자체도 제외
                if should_exclude_event("", event_type):
                    continue

                event_counter[f"UnknownFunnel({event_type})"] += 1

        # 서버 함수 코드 처리 (기존 방식)
        elif 'payload_code' in log and log['payload_code']:
            code = log['payload_code']
            event_name = f"{code}({event_type})"

            # IAP/inapp/Shop 관련 이벤트 및 USER_CURRENCY_DATA 제외
            if should_exclude_event(code, event_type):
                continue

            code_counter[event_name] += 1
            event_counter[event_name] += 1

        # 기타 이벤트 - 더 상세한 처리
        else:
            processed = False
            # payload_name이 있으면 사용
            if 'payload_name' in log and log['payload_name'] and str(log['payload_name']).strip() and str(log['payload_name']) != 'nan':
                payload_name = str(log['payload_name']).strip()
                event_name = f"{payload_name}({event_type})"

                # IAP/inapp/Shop 관련 이벤트 및 USER_CURRENCY_DATA 제외
                if should_exclude_event(payload_name, event_type):
                    processed = True
                    continue

                event_counter[event_name] += 1
                processed = True
            # payload_a_param에서 정보 추출 시도
            elif 'payload_a_param' in log and log['payload_a_param']:
                try:
                    import json
                    a_param_str = str(log['payload_a_param']).strip()
                    a_param_json = json.loads(a_param_str)
                    if isinstance(a_param_json, dict):
                        # 다양한 키에서 의미있는 값 찾기
                        for key in ['code', 'name', 'type', 'action', 'event']:
                            if key in a_param_json and a_param_json[key]:
                                value = str(a_param_json[key]).strip()
                                if value and value != 'nan':
                                    event_name = f"{value}({event_type})"

                                    # IAP/inapp/Shop 관련 이벤트 및 USER_CURRENCY_DATA 제외
                                    if should_exclude_event(value, event_type):
                                        processed = True
                                        break

                                    event_counter[event_name] += 1
                                    processed = True
                                    break
                        if not processed:
                            # 아무것도 찾지 못한 경우
                            event_counter[f"Unknown({event_type})"] += 1
                    else:
                        event_counter[f"Unknown({event_type})"] += 1
                except Exception as e:
                    event_counter[f"Unknown({event_type})"] += 1
            else:
                # USER_CURRENCY_DATA 이벤트 타입 자체도 제외
                if should_exclude_event("", event_type):
                    continue

                event_counter[f"Unknown({event_type})"] += 1
    
    # 상위 4개 추출
    top_events = event_counter.most_common(4)


    
    # iapProductID 추출 - jsonData에서 JSON 파싱
    iap_product_id = None
    try:
        # payload_a_param에서 JSON 파싱으로 jsonData 내의 iapProductID 추출
        if 'payload_a_param' in purchase_log and purchase_log['payload_a_param']:
            import json
            try:
                a_param_str = str(purchase_log['payload_a_param'])
                # JSON 파싱 시도
                a_param_json = json.loads(a_param_str)
                if isinstance(a_param_json, dict):
                    # jsonData 필드에서 iapProductID 찾기
                    json_data = a_param_json.get('jsonData')
                    if json_data:
                        if isinstance(json_data, str):
                            # jsonData가 문자열인 경우 다시 파싱
                            json_data = json.loads(json_data)
                        if isinstance(json_data, dict):
                            iap_product_id = json_data.get('iapProductID')

                    # jsonData가 없으면 직접 iapProductID 찾기
                    if not iap_product_id:
                        iap_product_id = a_param_json.get('iapProductID')

            except json.JSONDecodeError:
                # JSON이 아닌 경우 문자열에서 직접 추출 시도
                if 'iapProductID' in a_param_str:
                    import re
                    match = re.search(r'"?iapProductID"?\s*:\s*"?([^",}]+)"?', a_param_str)
                    if match:
                        iap_product_id = match.group(1)
    except Exception as e:
        logger.error(f"iapProductID 추출 중 오류: {e}")
        iap_product_id = None
    
    return {
        'purchase_time': purchase_time,
        'iap_product_id': iap_product_id,
        'pre_purchase_period': f"{minutes_before}분",
        'total_pre_logs': len(pre_purchase_logs),
        'filtered_logs': len(filtered_logs),
        'top_events': top_events,
        'detailed_logs': filtered_logs
    }


def analyze_multi_file_pre_purchase_behavior(purchase_log: Dict, s3_files: List[str], download_dir: str, minutes_before: int = 2) -> Dict:
    """
    다중 파일에서 구매 전 행동을 분석합니다.

    Args:
        purchase_log: 구매 로그
        s3_files: 분석할 S3 파일 리스트
        download_dir: 다운로드 디렉토리
        minutes_before: 구매 전 분석할 시간 (분)

    Returns:
        분석 결과
    """
    purchase_time = purchase_log.get('time')
    if not purchase_time:
        logger.error("구매 로그에 시간 정보가 없습니다")
        return {'error': '구매 로그에 시간 정보가 없습니다'}

    purchase_user_id = purchase_log.get('user_id')
    if not purchase_user_id:
        logger.error("구매 로그에 사용자 ID가 없습니다")
        return {'error': '구매 로그에 사용자 ID가 없습니다'}

    # 1단계: 구매 사용자와 관련된 파일들만 필터링
    user_files = []
    for file_path in s3_files:
        file_user_id = extract_user_id_from_filename(file_path)
        if file_user_id == purchase_user_id:
            user_files.append(file_path)

    if not user_files:
        return {'error': '사용자 관련 파일이 없습니다'}

    # 2단계: 사용자 관련 파일들만 다운로드
    try:
        downloaded_files = download_multiple_s3_files(user_files, download_dir)
        if not downloaded_files:
            return {'error': '다운로드된 파일이 없습니다'}
    except Exception as e:
        logger.error(f"파일 다운로드 실패: {e}")
        return {'error': f'파일 다운로드 실패: {e}'}

    # 3단계: 사용자 파일들에서 로그 수집 및 시간 순서 정렬
    all_logs = []
    processed_files = 0

    for file_path in downloaded_files:
        try:
            logs = parse_log_file_for_purchase_analysis(file_path)
            if logs:
                # 파일명에서 userID 추출 및 할당
                logs_with_user_id = assign_user_id_from_filename(logs, file_path)

                # 구매 사용자의 로그만 필터링
                user_logs = [log for log in logs_with_user_id if log.get('user_id') == purchase_user_id]

                if user_logs:
                    # 파일 정보 추가
                    for log in user_logs:
                        log['_source_file'] = os.path.basename(file_path)

                    all_logs.extend(user_logs)
                    processed_files += 1
        except Exception as e:
            logger.warning(f"파일 처리 실패: {file_path}, 오류: {e}")
            continue

    if not all_logs:
        return {'error': '처리된 로그가 없습니다'}

    # 4단계: 시간 순서대로 정렬
    sorted_logs = sorted(all_logs, key=lambda x: x.get('time') or datetime.min)

    # 6단계: 기존 분석 로직 적용 (구매 전 2분간 로그 분석)
    return analyze_pre_purchase_behavior(sorted_logs, purchase_log, minutes_before=minutes_before)

def analyze_purchase_patterns_with_multi_file_analysis(s3_files: List[str], download_dir: str, max_files: Optional[int] = None) -> Dict:
    """
    다중 파일 통합 분석을 사용하여 구매 패턴을 분석합니다.
    각 구매에 대해 관련 파일들에서 구매 전 동선을 분석합니다.
    """
    logger.info(f"다중 파일 구매 패턴 분석 시작: {len(s3_files)}개 파일, max_files: {max_files}")

    # 파일 수 제한
    if max_files and max_files < len(s3_files):
        s3_files = s3_files[:max_files]
        logger.info(f"파일 수를 {max_files}개로 제한")

    # 1단계: 모든 파일에서 구매 로그 찾기
    print(f"🔍 구매 로그 검색 시작: {len(s3_files)}개 파일")

    try:
        downloaded_files = download_multiple_s3_files(s3_files, download_dir)
        if not downloaded_files:
            return {'error': "다운로드된 파일이 없습니다."}

        print(f"✅ 다운로드 완료: {len(downloaded_files)}개 파일")
    except Exception as e:
        logger.error(f"파일 다운로드 중 오류: {e}")
        return {'error': f'파일 다운로드 중 오류: {e}'}

    # 2단계: 모든 구매 로그 수집
    all_purchase_logs = []
    processed_files = 0

    for file_path in downloaded_files:
        try:
            logs = parse_log_file_for_purchase_analysis(file_path)
            if logs:
                # 파일명에서 userID 추출 및 할당
                logs_with_user_id = assign_user_id_from_filename(logs, file_path)

                purchase_logs = find_purchase_logs(logs_with_user_id)
                for purchase_log in purchase_logs:
                    purchase_log['_source_file'] = os.path.basename(file_path)
                    all_purchase_logs.append(purchase_log)
                processed_files += 1
        except Exception as e:
            logger.warning(f"파일 처리 실패: {file_path}, 오류: {e}")
            continue

    if not all_purchase_logs:
        return {'error': '구매 로그를 찾을 수 없습니다'}

    print(f"💰 총 구매 로그 발견: {len(all_purchase_logs)}개 (처리된 파일: {processed_files}개)")

    # 3단계: 각 구매에 대해 다중 파일 분석
    all_purchase_analyses = []

    for i, purchase_log in enumerate(all_purchase_logs):
        try:
            print(f"📊 구매 분석 진행: {i+1}/{len(all_purchase_logs)}")
            analysis = analyze_multi_file_pre_purchase_behavior(
                purchase_log,
                s3_files,
                download_dir,
                minutes_before=2
            )

            if 'error' not in analysis:
                analysis['source_file'] = purchase_log.get('_source_file')
                all_purchase_analyses.append(analysis)
            else:
                logger.warning(f"구매 분석 실패: {analysis['error']}")

        except Exception as e:
            logger.error(f"구매 분석 중 오류: {e}")
            continue

    # 4단계: 결과 집계
    if not all_purchase_analyses:
        return {'error': '분석된 구매가 없습니다'}

    # 전체 패턴 집계
    all_top_events = []
    iap_product_counter = Counter()

    for analysis in all_purchase_analyses:
        all_top_events.extend([event[0] for event in analysis['top_events']])
        if analysis['iap_product_id']:
            iap_product_counter[analysis['iap_product_id']] += 1

    overall_top_events = Counter(all_top_events).most_common(10)

    # 시간대별 구매 패턴 분석
    hourly_purchases = Counter()
    weekday_purchases = Counter()
    weekdays = ['월', '화', '수', '목', '금', '토', '일']

    for analysis in all_purchase_analyses:
        try:
            purchase_time = analysis.get('purchase_time')
            if purchase_time:
                hourly_purchases[purchase_time.hour] += 1
                weekday_purchases[weekdays[purchase_time.weekday()]] += 1
        except Exception as e:
            logger.error(f"패턴 분석 중 오류: {e}")
            continue

    result = {
        'summary': {
            'total_files_processed': processed_files,
            'total_purchases': len(all_purchase_analyses),
            'unique_users': len(set(a.get('user_id') for a in all_purchase_analyses if a.get('user_id'))),
            'most_common_iap_products': iap_product_counter.most_common(5)
        },
        'overall_patterns': overall_top_events,
        'hourly_patterns': dict(hourly_purchases),
        'weekday_patterns': dict(weekday_purchases),
        'individual_analyses': all_purchase_analyses
    }

    return result

def analyze_purchase_patterns_from_s3_files(s3_files: List[str], download_dir: str, max_files: Optional[int] = None) -> Dict:
    """
    S3 파일들에서 구매 패턴을 분석합니다.

    Args:
        s3_files: S3 파일 키 리스트
        download_dir: 다운로드 디렉토리
        max_files: 최대 처리 파일 수

    Returns:
        분석 결과
    """
    logger.info(f"구매 패턴 분석 시작: {len(s3_files)}개 파일, max_files: {max_files}")

    # 오래된 캐시 정리 (한 번만 실행)
    clear_old_purchase_cache()

    # 캐시 키 생성
    cache_key = get_cache_key(s3_files, max_files)

    # 캐시에서 결과 로드 시도
    cached_result = load_analysis_cache(cache_key)
    if cached_result:
        logger.info(f"캐시에서 구매 분석 결과 로드 완료: {cache_key}")
        return cached_result

    logger.info(f"새로운 구매 분석 시작: {cache_key}")

    # 파일 수 제한
    if max_files and max_files < len(s3_files):
        s3_files = s3_files[:max_files]
        logger.info(f"파일 수를 {max_files}개로 제한")
    elif max_files is None:
        logger.info("파일 수 제한 없음 - 모든 파일 분석")

    # S3 파일 다운로드
    try:
        logger.info(f"S3 파일 다운로드 시작: {len(s3_files)}개 파일")
        downloaded_files = download_multiple_s3_files(s3_files, download_dir)
        if not downloaded_files:
            logger.error("다운로드된 파일이 없습니다")
            return {'error': "다운로드된 파일이 없습니다."}

        logger.info(f"다운로드 완료: {len(downloaded_files)}개 파일")
    except Exception as e:
        logger.error(f"파일 다운로드 중 오류: {e}")
        return {'error': f"파일 다운로드 중 오류: {e}"}
    
    # 점진적 캐싱: 캐시된 파일과 새로 처리할 파일 분리
    all_purchase_analyses = []
    total_purchases = 0
    processed_files = 0
    cached_files = 0
    new_files_to_process = []



    # 각 파일별로 캐시 확인 (s3_files와 downloaded_files를 매핑)
    for s3_file, local_file in zip(s3_files, downloaded_files):
        cached_analysis = load_file_analysis_cache(s3_file)
        if cached_analysis is not None:
            # 캐시된 결과 사용
            all_purchase_analyses.extend(cached_analysis)
            total_purchases += len([analysis for analysis in cached_analysis if analysis])
            cached_files += 1

        else:
            # 새로 처리해야 할 파일
            new_files_to_process.append((s3_file, local_file))

    logger.info(f"캐시 확인 완료: 캐시된 파일 {cached_files}개, 새로 처리할 파일 {len(new_files_to_process)}개")

    # 새로 처리할 파일이 있는 경우에만 병렬 처리 실행
    if new_files_to_process:
        logger.info(f"병렬 처리로 새 파일 파싱 시작: {len(new_files_to_process)}개 파일")

        # CPU 코어 수에 따라 워커 수 결정 (최대 8개)
        max_workers = min(8, os.cpu_count() or 4)

        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 새로운 파일들만 병렬로 처리
            future_to_file = {executor.submit(parse_single_file_wrapper, local_file): (s3_file, local_file)
                             for s3_file, local_file in new_files_to_process}

            for future in as_completed(future_to_file):
                s3_file, local_file = future_to_file[future]
                try:
                    file_path_result, logs, purchase_logs = future.result()

                    if not logs:
                        logger.debug(f"파일에서 로그를 찾을 수 없음: {os.path.basename(file_path_result)}")
                        # 빈 결과도 캐시에 저장 (재처리 방지)
                        save_file_analysis_cache(s3_file, [])
                        continue

                    # 파일명에서 userID 추출 및 할당
                    logs_with_user_id = assign_user_id_from_filename(logs, file_path_result)
                    purchase_logs_with_user_id = assign_user_id_from_filename(purchase_logs, file_path_result)

                    logger.debug(f"파일 처리: {os.path.basename(file_path_result)}, 로그 {len(logs_with_user_id)}개, 구매 {len(purchase_logs_with_user_id)}개")

                    # 이 파일의 분석 결과를 저장할 리스트
                    file_analyses = []

                    # 각 구매에 대해 단일 파일 분석 (무한 루프 방지)
                    for purchase_log in purchase_logs_with_user_id:
                        try:
                            # 다중 파일 통합 분석 (구매 전 2분간 로그 분석)
                            analysis = analyze_multi_file_pre_purchase_behavior(
                                purchase_log,
                                downloaded_files,
                                download_dir,
                                minutes_before=2
                            )
                            analysis['file_path'] = file_path_result
                            analysis['user_id'] = purchase_log.get('user_id')

                            file_analyses.append(analysis)
                            all_purchase_analyses.append(analysis)
                        except Exception as e:
                            logger.error(f"구매 분석 중 오류: {e}, 구매 로그: {purchase_log}")
                            continue

                    # 파일별 분석 결과를 캐시에 저장
                    save_file_analysis_cache(s3_file, file_analyses)

                    total_purchases += len(purchase_logs)
                    processed_files += 1

                    # 진행 상황 로그 (5개 파일마다)
                    if processed_files % 5 == 0:
                        progress_msg = f"📊 진행 상황: {processed_files}/{len(new_files_to_process)} 새 파일 처리 완료, 구매 {len(file_analyses)}건 발견"
                        print(progress_msg)
                        logger.info(progress_msg)

                except Exception as e:
                    logger.error(f"파일 처리 중 오류 {os.path.basename(local_file)}: {e}")
                    # 오류가 발생한 파일도 빈 결과로 캐시 저장 (재처리 방지)
                    save_file_analysis_cache(s3_file, [])
                    continue
    else:
        print(f"✅ 모든 파일이 캐시되어 있어 새로운 처리가 필요하지 않습니다!")
    
    # 전체 패턴 집계
    all_top_events = []
    iap_product_counter = Counter()

    for analysis in all_purchase_analyses:
        all_top_events.extend([event[0] for event in analysis['top_events']])
        if analysis['iap_product_id']:
            iap_product_counter[analysis['iap_product_id']] += 1
    
    # 전체 상위 이벤트 패턴
    overall_top_events = Counter(all_top_events).most_common(10)

    # 최종 결과 출력
    total_files = len(downloaded_files)
    final_msg = f"🎉 분석 완료! 총 구매 {total_purchases}건, 처리된 파일 {total_files}개 (캐시: {cached_files}개, 새로 처리: {processed_files}개)"
    print(final_msg)
    logger.info(final_msg)

    # 시간대별 구매 패턴 분석
    hourly_purchases = Counter()
    weekday_purchases = Counter()
    weekdays = ['월', '화', '수', '목', '금', '토', '일']

    if all_purchase_analyses:
        for analysis in all_purchase_analyses:
            try:
                purchase_time = analysis.get('purchase_time')
                if purchase_time:
                    # 시간대별 분석
                    hour = purchase_time.hour
                    hourly_purchases[hour] += 1

                    # 요일별 분석
                    weekday = weekdays[purchase_time.weekday()]
                    weekday_purchases[weekday] += 1
                else:
                    logger.warning(f"purchase_time이 없는 분석 데이터: {analysis}")
            except Exception as e:
                logger.error(f"패턴 분석 중 오류: {e}, 데이터: {analysis}")
                continue

        logger.info(f"시간대별 패턴: {dict(hourly_purchases)}")
        logger.info(f"요일별 패턴: {dict(weekday_purchases)}")
    else:
        logger.warning("구매 분석 데이터가 없어서 패턴 분석을 건너뜁니다")

    # 분석 결과 생성
    result = {
        'summary': {
            'total_files_processed': total_files,
            'cached_files': cached_files,
            'newly_processed_files': processed_files,
            'total_purchases': total_purchases,
            'unique_users': len(set(a['user_id'] for a in all_purchase_analyses)),
            'most_common_iap_products': iap_product_counter.most_common(5)
        },
        'overall_patterns': overall_top_events,
        'hourly_patterns': dict(hourly_purchases),
        'weekday_patterns': dict(weekday_purchases),
        'individual_analyses': all_purchase_analyses,
        'file_processing': {
            'total_files': len(s3_files),
            'downloaded_files': len(downloaded_files),
            'processed_files': processed_files
        }
    }

    # 결과를 캐시에 저장
    save_analysis_cache(result, cache_key)

    return result

def create_purchase_pattern_summary(purchase_result: Dict) -> pd.DataFrame:
    """
    iapProductID별 구매 패턴 분석 결과를 요약 테이블로 생성합니다.

    Args:
        purchase_result: analyze_purchase_patterns_from_s3_files 결과

    Returns:
        iapProductID별 요약 DataFrame
    """
    if not purchase_result or 'individual_analyses' not in purchase_result:
        return pd.DataFrame()

    # iapProductID별로 그룹화
    product_groups = {}
    total_purchases = len(purchase_result['individual_analyses'])

    for analysis in purchase_result['individual_analyses']:
        iap_product_id = analysis.get('iap_product_id', '알 수 없음')
        if iap_product_id not in product_groups:
            product_groups[iap_product_id] = []
        product_groups[iap_product_id].append(analysis)

    summary_data = []

    # 판매 횟수 기준으로 정렬
    sorted_products = sorted(product_groups.items(), key=lambda x: len(x[1]), reverse=True)

    for iap_product_id, analyses in sorted_products:
        # 해당 iapProductID의 모든 구매에서 이벤트 집계
        all_events = Counter()

        for analysis in analyses:
            for event, count in analysis.get('top_events', []):
                all_events[event] += count

        # 상위 4개 이벤트 추출
        top_events = all_events.most_common(4)

        # 판매 횟수와 비율 계산
        sales_count = len(analyses)
        sales_ratio = (sales_count / total_purchases) * 100

        summary_data.append({
            'iapProductID': iap_product_id,
            '동선 패턴 1위': f"{top_events[0][0]}({top_events[0][1]}회)" if len(top_events) > 0 else '없음',
            '동선 패턴 2위': f"{top_events[1][0]}({top_events[1][1]}회)" if len(top_events) > 1 else '없음',
            '동선 패턴 3위': f"{top_events[2][0]}({top_events[2][1]}회)" if len(top_events) > 2 else '없음',
            '동선 패턴 4위': f"{top_events[3][0]}({top_events[3][1]}회)" if len(top_events) > 3 else '없음',
            '판매 횟수': f"{sales_count}건",
            '비율': f"{sales_ratio:.1f}%"
        })

    return pd.DataFrame(summary_data)
