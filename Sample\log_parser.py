import pandas as pd
import BattleStruct_PROTO_pb2 as proto
import BattleEnumsFull_pb2 as enums
from functools import lru_cache
from typing import Optional, Dict, Any, List, Tuple
import logging
from config import PARSING_SETTINGS

# 로깅 설정
logger = logging.getLogger(__name__)

@lru_cache(maxsize=32)
def get_enum_name(enum_type, value):
    """열거형 값의 이름을 가져오고 캐싱합니다."""
    try:
        raw_name = enum_type.Name(value)
        return raw_name.replace(f"{enum_type.DESCRIPTOR.name}_", "")
    except ValueError:
        return f"UNKNOWN_{value}"

def parse_log(file_path: str, max_events: int = None) -> pd.DataFrame:
    """프로토버프 로그 파일을 파싱하여 데이터프레임으로 변환합니다."""
    with open(file_path, "rb") as f:
        data = f.read()

    queue = proto.TotalBattleLogQueue_PROTO()
    queue.ParseFromString(data)

    events = []
    event_count = 0

    for round_block in queue.rounds:
        r_idx = round_block.round_index
        for logqueue in round_block.logs:
            order = logqueue.oder
            event_type_order = 0

            # 배치 처리를 위한 이벤트 리스트
            batch_events = []

            for log in logqueue.logs:
                # 최대 이벤트 수 제한으로 조기 종료
                if max_events and event_count >= max_events:
                    break

                event_type_order += 1
                event_type = get_enum_name(enums.EBT_BATTLE_LOG_EVENT_TYPE, log.EventType)

                e = {
                    "round_index": r_idx,
                    "order": order,
                    "event_type_order": event_type_order,
                    "event_type": event_type,
                    "bookmarkDataIndex": [],
                    "jokerDataIndex": [],
                    "monsterCurrentHP": "",
                    "monsterMaxHP": "",
                    "multiple": "",
                    "chip": "",
                    "evaluateResult": None,
                }

                for payload in log.Payload.values():
                    ref_value = payload.referenceValue

                    if ref_value.HasField("gameInfo"):
                        gi = ref_value.gameInfo
                        e["bookmarkDataIndex"] = [pb.bookmarkDataIndex for pb in gi.playingBookMarks]
                        if event_type not in ["ADD_NEW_JOKER", "REMOVE_JOKER"]:
                            e["jokerDataIndex"] = [jk.jokerDataIndex for jk in gi.playingJokers]
                        e["monsterCurrentHP"] = str(gi.monsterCurrentHP)
                        e["monsterMaxHP"] = str(gi.monsterMaxHP)

                    # ADD_NEW_JOKER나 REMOVE_JOKER 이벤트인 경우 joker 필드에서 가져오기
                    if event_type in ["ADD_NEW_JOKER"] and ref_value.HasField("joker"):
                        joker_idx = ref_value.joker.jokerDataIndex
                        e["jokerDataIndex"] = [joker_idx]

                    if event_type == "REMOVE_JOKER" and payload.HasField("scalarValue"):
                        scalar_value = payload.scalarValue
                        if hasattr(scalar_value, 'ulongValue') and scalar_value.ulongValue > 0:
                            joker_idx = scalar_value.ulongValue
                            e["jokerDataIndex"] = [str(joker_idx)]

                    if ref_value.HasField("evaluateContext"):
                        context = ref_value.evaluateContext
                        e["multiple"] = str(context.multiple)
                        e["chip"] = str(context.chip)

                    if ref_value.HasField("evaluateResult"):
                        res = ref_value.evaluateResult
                        e["evaluateResult"] = {
                            "chips": res.chips,
                            "xChips": res.xChips,
                            "mult": res.mult,
                            "xMult": res.xMult,
                            "pDollars": res.pDollars,
                            "isRepeat": res.isRepeat,
                            "isDestroy": res.isDestroy,
                            "calculateArea": get_enum_name(enums.ETB_CALCULATOR_AREA, res.calculateArea),
                            "calculateTurn": get_enum_name(enums.ETB_IN_GAME_STATE_TURN, res.calculateTurn),
                            "isEffectChanged": res.isEffectChanged,
                            "abilityType": get_enum_name(enums.ETB_ABILITY_TYPE, res.abilityType),
                        }

                # 리스트를 문자열로 변환 (최적화된 방식)
                if e["bookmarkDataIndex"]:
                    e["bookmarkDataIndex"] = ",".join(map(str, sorted(set(e["bookmarkDataIndex"]))))
                else:
                    e["bookmarkDataIndex"] = ""

                if e["jokerDataIndex"]:
                    e["jokerDataIndex"] = ",".join(map(str, sorted(set(e["jokerDataIndex"]))))
                else:
                    e["jokerDataIndex"] = ""

                batch_events.append(e)
                event_count += 1

            events.extend(batch_events)

            # 최대 이벤트 수 도달 시 조기 종료
            if max_events and event_count >= max_events:
                break

        if max_events and event_count >= max_events:
            break

    # 한 번에 데이터프레임 생성
    return pd.DataFrame(events)

def parse_log_fast_bookmark_combinations(file_path: str) -> list:
    """북마크 조합만 빠르게 추출하는 최적화된 함수"""
    with open(file_path, "rb") as f:
        data = f.read()

    queue = proto.TotalBattleLogQueue_PROTO()
    queue.ParseFromString(data)

    combinations = []
    found_combination = False

    for round_block in queue.rounds:
        if found_combination:
            break

        for logqueue in round_block.logs:
            if found_combination:
                break

            for log in logqueue.logs:
                for payload in log.Payload.values():
                    ref_value = payload.referenceValue

                    if ref_value.HasField("gameInfo"):
                        gi = ref_value.gameInfo
                        bookmark_indices = [pb.bookmarkDataIndex for pb in gi.playingBookMarks]

                        # 4개 조합인 경우만 처리
                        if len(bookmark_indices) == 4:
                            sorted_combination = ",".join(map(str, sorted(set(bookmark_indices))))
                            combinations.append(sorted_combination)
                            found_combination = True
                            break

                if found_combination:
                    break

    return combinations

def parse_log_fast_max_round(file_path: str) -> int:
    """최대 라운드만 빠르게 추출하는 최적화된 함수"""
    with open(file_path, "rb") as f:
        data = f.read()

    queue = proto.TotalBattleLogQueue_PROTO()
    queue.ParseFromString(data)

    max_round = 0
    for round_block in queue.rounds:
        max_round = max(max_round, round_block.round_index)

    return max_round

def parse_log_fast_bookmark_joker_usage(file_path: str) -> tuple:
    """북마크와 조커 사용 통계만 빠르게 추출하는 최적화된 함수"""
    with open(file_path, "rb") as f:
        data = f.read()

    queue = proto.TotalBattleLogQueue_PROTO()
    queue.ParseFromString(data)

    bookmark_set = set()
    joker_set = set()

    for round_block in queue.rounds:
        for logqueue in round_block.logs:
            for log in logqueue.logs:
                for payload in log.Payload.values():
                    ref_value = payload.referenceValue

                    if ref_value.HasField("gameInfo"):
                        gi = ref_value.gameInfo

                        # 북마크 인덱스 수집
                        for pb in gi.playingBookMarks:
                            bookmark_set.add(str(pb.bookmarkDataIndex))

                        # 조커 인덱스 수집
                        for jk in gi.playingJokers:
                            joker_set.add(str(jk.jokerDataIndex))

    return bookmark_set, joker_set
def extract_game_info_summary(file_path: str) -> dict:
    """게임 정보 요약(userID, userLevel, seed)을 추출하는 함수"""
    with open(file_path, "rb") as f:
        data = f.read()

    queue = proto.TotalBattleLogQueue_PROTO()
    queue.ParseFromString(data)

    game_info = {
        "userID": None,
        "userLevel": None,
        "seed": None
    }

    # 첫 번째로 발견된 gameInfo에서 정보 추출
    for round_block in queue.rounds:
        for logqueue in round_block.logs:
            for log in logqueue.logs:
                for payload in log.Payload.values():
                    ref_value = payload.referenceValue

                    if ref_value.HasField("gameInfo"):
                        gi = ref_value.gameInfo

                        # 첫 번째로 발견된 정보만 사용
                        if game_info["userID"] is None:
                            game_info["userID"] = getattr(gi, 'userID', None)
                        if game_info["userLevel"] is None:
                            game_info["userLevel"] = getattr(gi, 'userLevel', None)
                        if game_info["seed"] is None:
                            game_info["seed"] = getattr(gi, 'seed', None)

                        # 모든 정보를 찾았으면 조기 종료
                        if all(v is not None for v in game_info.values()):
                            return game_info

    return game_info
def parse_log_fast_item_scroll_usage(file_path: str) -> tuple:
    """아이템과 스크롤 사용 통계만 빠르게 추출하는 최적화된 함수"""
    with open(file_path, "rb") as f:
        data = f.read()

    queue = proto.TotalBattleLogQueue_PROTO()
    queue.ParseFromString(data)

    consume_item_set = set()
    scroll_item_set = set()

    for round_block in queue.rounds:
        for logqueue in round_block.logs:
            for log in logqueue.logs:
                # 이벤트 타입 확인 (아이템 사용 관련)
                if log.EventType in [7, 9, 10, 11]:  # BUY_CONSUME_ITEM, BUY_SCROLL_ITEM, USE_CONSUME_ITEM, USE_SCROLL
                    for payload in log.Payload.values():
                        ref_value = payload.referenceValue

                        # 상점에서 구매한 아이템들 수집 (BUY 이벤트)
                        if log.EventType in [7, 9]:  # BUY_CONSUME_ITEM, BUY_SCROLL_ITEM
                            if hasattr(ref_value, 'productList') and ref_value.productList:
                                for product in ref_value.productList:
                                    if hasattr(product, 'itemType') and hasattr(product, 'itemIndex'):
                                        item_type_enum = product.itemType
                                        item_index = product.itemIndex

                                        if item_type_enum == 3:  # CONSUME_ITEM
                                            consume_item_set.add(str(item_index))
                                        elif item_type_enum == 4:  # SCROLL_ITEM
                                            scroll_item_set.add(str(item_index))

                            if hasattr(ref_value, 'product') and ref_value.product:
                                product = ref_value.product
                                if hasattr(product, 'itemType') and hasattr(product, 'itemIndex'):
                                    item_type_enum = product.itemType
                                    item_index = product.itemIndex

                                    if item_type_enum == 3:  # CONSUME_ITEM
                                        consume_item_set.add(str(item_index))
                                    elif item_type_enum == 4:  # SCROLL_ITEM
                                        scroll_item_set.add(str(item_index))

                        # 사용된 아이템들 수집 (USE 이벤트)
                        elif log.EventType in [10, 11]:  # USE_CONSUME_ITEM, USE_SCROLL
                            # scalarValue에서 아이템 인덱스 추출
                            scalar_value = payload.scalarValue
                            if hasattr(scalar_value, 'intValue') and scalar_value.intValue > 0:
                                if log.EventType == 10:  # USE_CONSUME_ITEM
                                    consume_item_set.add(str(scalar_value.intValue))
                                elif log.EventType == 11:  # USE_SCROLL
                                    scroll_item_set.add(str(scalar_value.intValue))

                            # referenceValue에서도 확인
                            if hasattr(ref_value, 'consumeItem') and ref_value.consumeItem:
                                if hasattr(ref_value.consumeItem, 'consumeItemDataIndex'):
                                    consume_item_set.add(str(ref_value.consumeItem.consumeItemDataIndex))

                            # 스크롤 아이템의 경우 다른 필드에 있을 수 있음
                            if hasattr(ref_value, 'scrollItem') and ref_value.scrollItem:
                                if hasattr(ref_value.scrollItem, 'scrollItemDataIndex'):
                                    scroll_item_set.add(str(ref_value.scrollItem.scrollItemDataIndex))

                # gameInfo에서 현재 보유 중인 아이템들도 수집
                for payload in log.Payload.values():
                    ref_value = payload.referenceValue

                    if hasattr(ref_value, 'gameInfo'):
                        gi = ref_value.gameInfo

                        # 소모 아이템 인덱스 수집
                        for item in gi.playingConsumeItems:
                            consume_item_set.add(str(item.consumeItemDataIndex))

    return consume_item_set, scroll_item_set

def parse_log_fast_joker_in_out_stats(file_path: str) -> tuple:
    """조커 In/Out/파괴 통계만 빠르게 추출하는 최적화된 함수"""
    with open(file_path, "rb") as f:
        data = f.read()

    queue = proto.TotalBattleLogQueue_PROTO()
    queue.ParseFromString(data)

    joker_in_counts = {}  # 조커별 In 횟수
    joker_out_counts = {}  # 조커별 Out 횟수
    joker_destroy_counts = {}  # 조커별 파괴 횟수

    for round_block in queue.rounds:
        for logqueue in round_block.logs:
            for log in logqueue.logs:
                event_type = get_enum_name(enums.EBT_BATTLE_LOG_EVENT_TYPE, log.EventType)

                # ADD_NEW_JOKER 이벤트 처리
                if event_type == "ADD_NEW_JOKER":
                    for payload in log.Payload.values():
                        ref_value = payload.referenceValue
                        if ref_value.HasField("joker"):
                            joker_idx = str(ref_value.joker.jokerDataIndex)
                            joker_in_counts[joker_idx] = joker_in_counts.get(joker_idx, 0) + 1

                # REMOVE_JOKER 이벤트 처리
                elif event_type == "REMOVE_JOKER":
                    for payload in log.Payload.values():
                        if payload.HasField("scalarValue"):
                            scalar_value = payload.scalarValue
                            if hasattr(scalar_value, 'ulongValue') and scalar_value.ulongValue > 0:
                                joker_idx = str(scalar_value.ulongValue)
                                is_destroy = False
                                if hasattr(scalar_value, 'boolValue'):
                                    is_destroy = scalar_value.boolValue

                                # Out 횟수는 파괴 여부 상관없이 카운트
                                joker_out_counts[joker_idx] = joker_out_counts.get(joker_idx, 0) + 1

                                # 파괴된 경우만 별도 카운트
                                if is_destroy:
                                    joker_destroy_counts[joker_idx] = joker_destroy_counts.get(joker_idx, 0) + 1

    return joker_in_counts, joker_out_counts, joker_destroy_counts

def extract_last_monster_id(file_path: str) -> int:
    """마지막 MONSTER_INIT 이벤트의 currentMonsterID를 추출하는 함수"""
    with open(file_path, "rb") as f:
        data = f.read()

    queue = proto.TotalBattleLogQueue_PROTO()
    queue.ParseFromString(data)

    last_monster_id = None

    for round_block in queue.rounds:
        for logqueue in round_block.logs:
            for log in logqueue.logs:
                event_type = get_enum_name(enums.EBT_BATTLE_LOG_EVENT_TYPE, log.EventType)

                if event_type == "MONSTER_INIT":
                    for payload in log.Payload.values():
                        ref_value = payload.referenceValue
                        if ref_value.HasField("gameInfo"):                        
                            gi = ref_value.gameInfo
                            if hasattr(gi, 'currentMonsterID'):
                                last_monster_id = gi.currentMonsterID

    return last_monster_id

def parse_log_extended(file_path: str, max_events: int = None) -> pd.DataFrame:
    """프로토버프 로그 파일을 파싱하여 모든 항목을 포함한 데이터프레임으로 변환합니다.

    Args:
        file_path: 프로토버프 파일 경로
        max_events: 최대 이벤트 수 (None이면 모든 이벤트 처리)

    Returns:
        확장된 정보를 포함한 pandas DataFrame
    """
    try:
        with open(file_path, "rb") as f:
            data = f.read()

        queue = proto.TotalBattleLogQueue_PROTO()
        queue.ParseFromString(data)
    except Exception as e:
        # 파일 읽기 오류 시 빈 DataFrame 반환
        print(f"파일 읽기 오류: {e}")
        return pd.DataFrame()

    events = []
    event_count = 0

    for round_block in queue.rounds:
        r_idx = round_block.round_index
        for logqueue in round_block.logs:
            order = logqueue.oder
            event_type_order = 0

            # 배치 처리를 위한 이벤트 리스트
            batch_events = []

            for log in logqueue.logs:
                # 최대 이벤트 수 제한으로 조기 종료
                if max_events and event_count >= max_events:
                    break

                event_type_order += 1
                event_type = get_enum_name(enums.EBT_BATTLE_LOG_EVENT_TYPE, log.EventType)

                # 확장된 이벤트 구조 - 모든 가능한 필드 포함
                e = {
                    "round_index": r_idx,
                    "order": order,
                    "event_type_order": event_type_order,
                    "event_type": event_type,
                    "source": getattr(log, 'Source', ''),

                    # 기존 필드들
                    "bookmarkDataIndex": [],
                    "jokerDataIndex": [],
                    "monsterCurrentHP": "",
                    "monsterMaxHP": "",
                    "multiple": "",
                    "chip": "",
                    "evaluateResult": None,

                    # 확장 필드들
                    "gameInfo": None,
                    "card": None,
                    "cardList": [],
                    "productList": [],
                    "product": None,
                    "evaluateConfig": None,
                    "evaluateContext": None,
                    "scalarValues": {},
                    "payload_keys": [],
                    "payload_types": [],
                    "referenceValues": {},  # 모든 reference value 저장
                    "joker": None,
                    "stringDictionary": None,
                    "stringDictionaryList": [],
                    "intList": [],
                    "battleState": "",
                    "missionBeforeState": "",
                    "userID": "",
                    "userLevel": "",
                    "seed": "",
                    "currentMonsterID": "",
                    "coin": "",
                    "dollarUse": "",
                    "handCountMax": "",
                    "jokerCountMax": "",
                    "itemCountMax": "",
                    "attackCountMax": "",
                    "throwCountMax": "",
                    "attackTryCount": "",
                    "throwTryCount": "",
                    "freeItemCount": "",
                    "monsterAbilityCount": "",
                    "playingCardsCount": "",
                    "destroyedCardsCount": "",
                }

                # Payload 처리
                payload_keys = []
                payload_types = []
                scalar_values = {}

                # referenceValues 초기화
                e["referenceValues"] = {}

                for key, payload in log.Payload.items():
                    payload_keys.append(key)

                    # Payload 타입 추가
                    if hasattr(payload, 'payloadType'):
                        try:
                            payload_type = get_enum_name(enums.EBT_BATTLE_LOG_PAYLOAD_TYPE, payload.payloadType)
                            payload_types.append(f"{key}:{payload_type}")
                        except Exception:
                            payload_types.append(f"{key}:UNKNOWN")

                    try:
                        # Scalar Value 처리
                        if payload.HasField("scalarValue"):
                            scalar = payload.scalarValue
                            scalar_data = {}

                            # 모든 scalar 필드 안전하게 추출 (proto 파일 기준)
                            scalar_fields = [
                                'intValue', 'ulongValue', 'boolValue', 'stringValue',
                                'floatValue', 'doubleValue'  # proto에서 string 타입으로 정의됨
                            ]

                            for field in scalar_fields:
                                try:
                                    if hasattr(scalar, field):
                                        value = getattr(scalar, field, None)
                                        if value is not None and str(value).strip():
                                            scalar_data[field] = value
                                except Exception:
                                    pass

                            # cardIndices 배열 처리
                            try:
                                if hasattr(scalar, 'cardIndices') and scalar.cardIndices:
                                    scalar_data['cardIndices'] = list(scalar.cardIndices)
                            except Exception:
                                pass

                            # Enum 필드들 처리
                            try:
                                if hasattr(scalar, 'overKillType') and scalar.overKillType != 0:
                                    scalar_data['overKillType'] = get_enum_name(enums.ETB_OVER_KILL, scalar.overKillType)
                                if hasattr(scalar, 'abilityType') and scalar.abilityType != 0:
                                    scalar_data['abilityType'] = get_enum_name(enums.ETB_ABILITY_TYPE, scalar.abilityType)
                                if hasattr(scalar, 'pokerHandsType') and scalar.pokerHandsType != 0:
                                    scalar_data['pokerHandsType'] = get_enum_name(enums.ETB_Poker_Hands_Type, scalar.pokerHandsType)
                                if hasattr(scalar, 'soundType') and scalar.soundType != 0:
                                    scalar_data['soundType'] = get_enum_name(enums.EBT_SOUND_TYPE, scalar.soundType)
                                if hasattr(scalar, 'missionRewardType') and scalar.missionRewardType != 0:
                                    scalar_data['missionRewardType'] = get_enum_name(enums.ETB_IN_GAME_MISSION_REWARD_TYPE, scalar.missionRewardType)
                            except Exception:
                                pass

                            if scalar_data:
                                scalar_values[key] = scalar_data

                        # Reference Value 처리 - 포괄적 추출
                        if payload.HasField("referenceValue"):
                            ref_value = payload.referenceValue

                            # 포괄적인 reference value 추출
                            comprehensive_ref_data = extract_reference_value_comprehensive(ref_value)
                            e["referenceValues"][key] = comprehensive_ref_data

                            # 기존 호환성을 위한 필드들 유지
                            if "gameInfo" in comprehensive_ref_data:
                                gi_data = comprehensive_ref_data["gameInfo"]

                                # 책갈피와 조커 인덱스 추출
                                if "playingBookMarks_sample" in gi_data:
                                    e["bookmarkDataIndex"] = [item["bookmarkDataIndex"] for item in gi_data["playingBookMarks_sample"]]
                                if "playingJokers_sample" in gi_data and event_type not in ["ADD_NEW_JOKER", "REMOVE_JOKER"]:
                                    e["jokerDataIndex"] = [item["jokerDataIndex"] for item in gi_data["playingJokers_sample"]]

                                # 기본 게임 정보 필드들
                                basic_fields = [
                                    'monsterCurrentHP', 'monsterMaxHP', 'userID', 'userLevel',
                                    'seed', 'currentMonsterID', 'coin', 'dollarUse',
                                    'handCountMax', 'jokerCountMax', 'itemCountMax',
                                    'attackCountMax', 'throwCountMax', 'attackTryCount',
                                    'throwTryCount', 'freeItemCount'
                                ]

                                for field in basic_fields:
                                    if field in gi_data:
                                        e[field] = str(gi_data[field])

                                # 배틀 상태
                                if "battleState" in gi_data:
                                    e["battleState"] = gi_data["battleState"]
                                if "missionBeforeState" in gi_data:
                                    e["missionBeforeState"] = gi_data["missionBeforeState"]

                                # 배열 개수 정보
                                count_fields = [
                                    ('monsterAbilityList_count', 'monsterAbilityCount'),
                                    ('playingFullDeck_count', 'playingCardsCount'),
                                    ('destroyedPlayingCards_count', 'destroyedCardsCount')
                                ]

                                for gi_field, e_field in count_fields:
                                    if gi_field in gi_data:
                                        e[e_field] = str(gi_data[gi_field])

                            # 조커 처리 (ADD_NEW_JOKER 이벤트)
                            if event_type in ["ADD_NEW_JOKER"] and "joker" in comprehensive_ref_data:
                                joker_data = comprehensive_ref_data["joker"]
                                if "jokerDataIndex" in joker_data:
                                    e["jokerDataIndex"] = [joker_data["jokerDataIndex"]]

                            # EvaluateContext 처리
                            if "evaluateContext" in comprehensive_ref_data:
                                context_data = comprehensive_ref_data["evaluateContext"]
                                e["multiple"] = str(context_data.get("multiple", ""))
                                e["chip"] = str(context_data.get("chip", ""))

                            # EvaluateResult 처리
                            if "evaluateResult" in comprehensive_ref_data:
                                e["evaluateResult"] = comprehensive_ref_data["evaluateResult"]

                    except Exception:
                        # 예외 발생 시 로그에 기록하지만 처리는 계속
                        pass

                # REMOVE_JOKER 이벤트 특별 처리
                if event_type == "REMOVE_JOKER":
                    try:
                        for payload in log.Payload.values():
                            if payload.HasField("scalarValue"):
                                scalar_value = payload.scalarValue
                                if hasattr(scalar_value, 'ulongValue') and scalar_value.ulongValue > 0:
                                    joker_idx = scalar_value.ulongValue
                                    e["jokerDataIndex"] = [str(joker_idx)]
                                    break
                    except Exception:
                        pass

                # 리스트를 문자열로 변환 (최적화된 방식)
                try:
                    if e["bookmarkDataIndex"]:
                        e["bookmarkDataIndex"] = ",".join(map(str, sorted(set(e["bookmarkDataIndex"]))))
                    else:
                        e["bookmarkDataIndex"] = ""

                    if e["jokerDataIndex"]:
                        e["jokerDataIndex"] = ",".join(map(str, sorted(set(e["jokerDataIndex"]))))
                    else:
                        e["jokerDataIndex"] = ""
                except Exception:
                    e["bookmarkDataIndex"] = ""
                    e["jokerDataIndex"] = ""

                # 추가 정보 저장
                e["payload_keys"] = ",".join(payload_keys) if payload_keys else ""
                e["payload_types"] = ",".join(payload_types) if payload_types else ""
                e["scalarValues"] = scalar_values if scalar_values else {}

                batch_events.append(e)
                event_count += 1

            events.extend(batch_events)

            # 최대 이벤트 수 도달 시 조기 종료
            if max_events and event_count >= max_events:
                break

        if max_events and event_count >= max_events:
            break

    # 한 번에 데이터프레임 생성
    return pd.DataFrame(events)

def extract_reference_value_comprehensive(ref_value):
    """ReferenceValue의 모든 필드를 포괄적으로 추출합니다."""
    result = {}

    try:
        # 1. PlayingCard_PROTO card
        if ref_value.HasField("card"):
            card = ref_value.card
            result["card"] = {
                "cardDataIndex": getattr(card, 'cardDataIndex', 0),
                "isBack": getattr(card, 'isBack', False),
                "chip": getattr(card, 'chip', 0)
            }

            # InGameCardLabel_PROTO label
            if hasattr(card, 'label') and card.HasField("label"):
                label = card.label
                result["card"]["label"] = {
                    "labelDataIndex": getattr(label, 'labelDataIndex', 0),
                    "abilities": extract_abilities_list(getattr(label, 'abilities', []))
                }

        # 2. repeated PlayingCard_PROTO cardList
        if hasattr(ref_value, 'cardList') and ref_value.cardList:
            result["cardList"] = []
            for card in ref_value.cardList:
                card_data = {
                    "cardDataIndex": getattr(card, 'cardDataIndex', 0),
                    "isBack": getattr(card, 'isBack', False),
                    "chip": getattr(card, 'chip', 0)
                }
                if hasattr(card, 'label') and card.HasField("label"):
                    label = card.label
                    card_data["label"] = {
                        "labelDataIndex": getattr(label, 'labelDataIndex', 0),
                        "abilities": extract_abilities_list(getattr(label, 'abilities', []))
                    }
                result["cardList"].append(card_data)

        # 3. repeated InGameShopProduct_PROTO productList
        if hasattr(ref_value, 'productList') and ref_value.productList:
            result["productList"] = []
            for product in ref_value.productList:
                result["productList"].append(extract_shop_product(product))

        # 4. repeated int32 intList
        if hasattr(ref_value, 'intList') and ref_value.intList:
            result["intList"] = list(ref_value.intList)

        # 5. InGameJoker_PROTO joker
        if ref_value.HasField("joker"):
            joker = ref_value.joker
            result["joker"] = {
                "jokerDataIndex": getattr(joker, 'jokerDataIndex', 0),
                "isBack": getattr(joker, 'isBack', False),
                "userDataLv": getattr(joker, 'userDataLv', 0),
                "abilities": extract_abilities_list(getattr(joker, 'abilities', []))
            }

        # 6. BattleLogReferenceValue_PROTO_StringDictionary stringDictionary
        if ref_value.HasField("stringDictionary"):
            string_dict = ref_value.stringDictionary
            if hasattr(string_dict, 'stringDictionary'):
                result["stringDictionary"] = dict(string_dict.stringDictionary)

        # 7. repeated BattleLogReferenceValue_PROTO_StringDictionary stringDictionaryList
        if hasattr(ref_value, 'stringDictionaryList') and ref_value.stringDictionaryList:
            result["stringDictionaryList"] = []
            for string_dict in ref_value.stringDictionaryList:
                if hasattr(string_dict, 'stringDictionary'):
                    result["stringDictionaryList"].append(dict(string_dict.stringDictionary))

        # 8. InGameShopProduct_PROTO product
        if ref_value.HasField("product"):
            result["product"] = extract_shop_product(ref_value.product)

        # 9. EvaluateResult_PROTO evaluateResult
        if ref_value.HasField("evaluateResult"):
            result["evaluateResult"] = extract_evaluate_result(ref_value.evaluateResult)

        # 10. EvaluateAttentionConfig_PROTO evaluateConfig
        if ref_value.HasField("evaluateConfig"):
            result["evaluateConfig"] = extract_evaluate_config(ref_value.evaluateConfig)

        # 11. InGameInfoSnapshot_PROTO gameInfo
        if ref_value.HasField("gameInfo"):
            result["gameInfo"] = extract_game_info_comprehensive(ref_value.gameInfo)

        # 12. EvaluateContext_PROTO evaluateContext
        if ref_value.HasField("evaluateContext"):
            context = ref_value.evaluateContext
            result["evaluateContext"] = {
                "chip": getattr(context, 'chip', 0),
                "multiple": getattr(context, 'multiple', "")
            }

    except Exception as e:
        result["extraction_error"] = str(e)

    return result

def extract_abilities_list(abilities):
    """능력 리스트를 추출합니다. (InGameAbility_PROTO 기준)"""
    if not abilities:
        return []

    result = []
    for ability in abilities:
        ability_data = {}
        try:
            # int32 abilityDataIndex
            if hasattr(ability, 'abilityDataIndex'):
                ability_data['abilityDataIndex'] = getattr(ability, 'abilityDataIndex', 0)

            # ETB_ABILITY_CALCULATE_TYPE calculateType
            if hasattr(ability, 'calculateType'):
                ability_data['calculateType'] = get_enum_name(enums.ETB_ABILITY_CALCULATE_TYPE, ability.calculateType)

            # ETB_CALCULATOR_AREA calculateArea
            if hasattr(ability, 'calculateArea'):
                ability_data['calculateArea'] = get_enum_name(enums.ETB_CALCULATOR_AREA, ability.calculateArea)

            # ETB_IN_GAME_STATE_TURN turnType
            if hasattr(ability, 'turnType'):
                ability_data['turnType'] = get_enum_name(enums.ETB_IN_GAME_STATE_TURN, ability.turnType)

            # int32 round
            if hasattr(ability, 'round'):
                ability_data['round'] = getattr(ability, 'round', 0)

            # repeated InGameAbilityValue_PROTO values
            if hasattr(ability, 'values') and ability.values:
                ability_data['values'] = []
                for value in ability.values:
                    value_data = {
                        'key': getattr(value, 'key', ''),
                        'type': getattr(value, 'type', ''),
                        'value': getattr(value, 'value', '')
                    }
                    ability_data['values'].append(value_data)

            # repeated InGameAbilityValue_PROTO conditions
            if hasattr(ability, 'conditions') and ability.conditions:
                ability_data['conditions'] = []
                for condition in ability.conditions:
                    condition_data = {
                        'key': getattr(condition, 'key', ''),
                        'type': getattr(condition, 'type', ''),
                        'value': getattr(condition, 'value', '')
                    }
                    ability_data['conditions'].append(condition_data)

            # 기존 필드들도 유지 (하위 호환성)
            if hasattr(ability, 'abilityType'):
                ability_data['abilityType'] = get_enum_name(enums.ETB_ABILITY_TYPE, ability.abilityType)
            if hasattr(ability, 'value'):
                ability_data['value'] = getattr(ability, 'value', '')
            if hasattr(ability, 'isDestroy'):
                ability_data['isDestroy'] = getattr(ability, 'isDestroy', False)

            result.append(ability_data)
        except Exception:
            pass
    return result

def extract_shop_product(product):
    """상점 상품 정보를 추출합니다. (InGameShopProduct_PROTO 기준)"""
    if not product:
        return {}

    result = {}
    try:
        # ETB_CARD_TYPE itemType
        if hasattr(product, 'itemType'):
            result['itemType'] = get_enum_name(enums.ETB_CARD_TYPE, product.itemType)

        # int32 itemIndex
        if hasattr(product, 'itemIndex'):
            result['itemIndex'] = getattr(product, 'itemIndex', 0)

        # Abilitiable_PROTO product
        if hasattr(product, 'product') and product.HasField("product"):
            abilitiable = product.product
            result['product'] = {
                'abilities': extract_abilities_list(getattr(abilitiable, 'abilities', [])),
                'isAbilityCopyCompact': getattr(abilitiable, 'isAbilityCopyCompact', False)
            }

            # InGameCardLabel_PROTO label
            if hasattr(abilitiable, 'label') and abilitiable.HasField("label"):
                label = abilitiable.label
                result['product']['label'] = {
                    'labelDataIndex': getattr(label, 'labelDataIndex', 0),
                    'abilities': extract_abilities_list(getattr(label, 'abilities', []))
                }

        # 기존 필드들도 유지 (하위 호환성)
        if hasattr(product, 'productType'):
            result['productType'] = get_enum_name(enums.ETB_SHOP_PRODUCT_TYPE, product.productType)
        if hasattr(product, 'price'):
            result['price'] = getattr(product, 'price', 0)
        if hasattr(product, 'dataIndex'):
            result['dataIndex'] = getattr(product, 'dataIndex', 0)
        if hasattr(product, 'isDiscount'):
            result['isDiscount'] = getattr(product, 'isDiscount', False)
    except Exception:
        pass
    return result

def extract_evaluate_result(eval_result):
    """평가 결과를 추출합니다."""
    if not eval_result:
        return {}

    result = {}
    try:
        basic_fields = ['chips', 'xChips', 'mult', 'xMult', 'pDollars', 'isRepeat', 'isDestroy', 'isEffectChanged']
        for field in basic_fields:
            if hasattr(eval_result, field):
                result[field] = getattr(eval_result, field, None)

        # Enum 필드들
        enum_fields = [
            ('calculateArea', enums.ETB_CALCULATOR_AREA),
            ('calculateTurn', enums.ETB_IN_GAME_STATE_TURN),
            ('abilityType', enums.ETB_ABILITY_TYPE),
            ('calculateType_chips', enums.ETB_ABILITY_CALCULATE_TYPE),
            ('calculateType_xChips', enums.ETB_ABILITY_CALCULATE_TYPE),
            ('calculateType_mult', enums.ETB_ABILITY_CALCULATE_TYPE),
            ('calculateType_xMult', enums.ETB_ABILITY_CALCULATE_TYPE),
            ('calculateType_dollars', enums.ETB_ABILITY_CALCULATE_TYPE)
        ]

        for field_name, enum_type in enum_fields:
            if hasattr(eval_result, field_name):
                value = getattr(eval_result, field_name, 0)
                if value != 0:
                    result[field_name] = get_enum_name(enum_type, value)

        # 중첩된 객체들
        if hasattr(eval_result, 'card') and eval_result.HasField("card"):
            card = eval_result.card
            result["card"] = {
                "cardDataIndex": getattr(card, 'cardDataIndex', 0),
                "isBack": getattr(card, 'isBack', False),
                "chip": getattr(card, 'chip', 0)
            }

        if hasattr(eval_result, 'abilitiable') and eval_result.HasField("abilitiable"):
            abilitiable = eval_result.abilitiable
            result["abilitiable"] = {
                "abilities": extract_abilities_list(getattr(abilitiable, 'abilities', [])),
                "isAbilityCopyCompact": getattr(abilitiable, 'isAbilityCopyCompact', False)
            }

        # extraResults 처리
        if hasattr(eval_result, 'extraResults') and eval_result.extraResults:
            result["extraResults"] = []
            for extra in eval_result.extraResults[:3]:  # 최대 3개만
                result["extraResults"].append(extract_evaluate_result(extra))

    except Exception:
        pass
    return result

def extract_evaluate_config(eval_config):
    """평가 설정을 추출합니다."""
    if not eval_config:
        return {}

    result = {}
    try:
        basic_fields = ['value', 'isDestroy']
        for field in basic_fields:
            if hasattr(eval_config, field):
                result[field] = getattr(eval_config, field, None)

        # Enum 필드들
        if hasattr(eval_config, 'sound'):
            result['sound'] = get_enum_name(enums.EBT_SOUND_TYPE, eval_config.sound)
        if hasattr(eval_config, 'type'):
            result['type'] = get_enum_name(enums.EBT_ATTENTION_UPDATE_TYPE, eval_config.type)

        # 중첩된 객체들
        if hasattr(eval_config, 'card') and eval_config.HasField("card"):
            card = eval_config.card
            result["card"] = {
                "cardDataIndex": getattr(card, 'cardDataIndex', 0),
                "isBack": getattr(card, 'isBack', False),
                "chip": getattr(card, 'chip', 0)
            }

        if hasattr(eval_config, 'evaluateResult') and eval_config.HasField("evaluateResult"):
            result["evaluateResult"] = extract_evaluate_result(eval_config.evaluateResult)

    except Exception:
        pass
    return result

def extract_game_info_comprehensive(game_info):
    """게임 정보를 포괄적으로 추출합니다."""
    if not game_info:
        return {}

    result = {}
    try:
        # 기본 필드들
        basic_fields = [
            'handCountMax', 'jokerCountMax', 'itemCountMax', 'attackCountMax',
            'throwCountMax', 'attackTryCount', 'throwTryCount', 'freeItemCount',
            'dollarUse', 'coin', 'monsterCurrentHP', 'monsterMaxHP', 'currentMonsterID',
            'userID', 'userLevel', 'seed'
        ]

        for field in basic_fields:
            if hasattr(game_info, field):
                value = getattr(game_info, field, None)
                if value is not None:
                    result[field] = value

        # Enum 필드들
        if hasattr(game_info, 'battleState'):
            result['battleState'] = get_enum_name(enums.ETB_BATTLE_STATE, game_info.battleState)
        if hasattr(game_info, 'missionBeforeState'):
            result['missionBeforeState'] = get_enum_name(enums.ETB_BATTLE_STATE, game_info.missionBeforeState)

        # 배열 필드들의 개수와 샘플
        array_fields = [
            'originFullDeck', 'playingFullDeck', 'playingDeck', 'destroyedPlayingCards',
            'monsterAbilityList', 'playingConsumeItems', 'playingJokers', 'playingBookMarks',
            'pokerHandDataList'
        ]

        for field in array_fields:
            if hasattr(game_info, field):
                array_data = getattr(game_info, field, [])
                # count 필드는 제거 (repeated 필드에서 직접 개수 표시)

                if array_data:

                    # 모든 배열 필드의 전체 데이터를 저장 (개수 제한 없음)
                    if field in ['originFullDeck', 'playingFullDeck', 'playingDeck', 'destroyedPlayingCards']:
                        # PlayingCard_PROTO 리스트 전체 저장 (제한 없음)
                        result[field] = []
                        for item in array_data:  # 모든 데이터
                            card_data = extract_playing_card_detailed(item)
                            if card_data:
                                result[field].append(card_data)

                    elif field == 'playingJokers':
                        # InGameJoker_PROTO 리스트 전체 저장 (제한 없음)
                        result[field] = []
                        for item in array_data:  # 모든 데이터
                            joker_data = extract_joker_detailed(item)
                            if joker_data:
                                result[field].append(joker_data)

                    elif field == 'playingBookMarks':
                        # InGameBookMark_PROTO 리스트 전체 저장 (제한 없음)
                        result[field] = []
                        for item in array_data:  # 모든 데이터
                            bookmark_data = extract_bookmark_detailed(item)
                            if bookmark_data:
                                result[field].append(bookmark_data)

                    elif field == 'pokerHandDataList':
                        # PokerHandData_PROTO 리스트 전체 저장 (제한 없음)
                        result[field] = []
                        for item in array_data:  # 모든 데이터
                            poker_data = extract_poker_hand_detailed(item)
                            if poker_data:
                                result[field].append(poker_data)

                    # 기존 샘플 코드 유지 (호환성)
                    elif len(array_data) > 0 and field in ['playingJokers', 'playingBookMarks']:
                        if field == 'playingJokers':
                            result[f"{field}_sample"] = []
                            for item in array_data[:5]:  # 최대 5개 샘플
                                joker_data = {
                                    "jokerDataIndex": getattr(item, 'jokerDataIndex', 0),
                                    "userDataLv": getattr(item, 'userDataLv', 0),
                                    "isBack": getattr(item, 'isBack', False)
                                }
                                if hasattr(item, 'abilities') and item.abilities:
                                    joker_data["abilities_count"] = len(item.abilities)
                                result[f"{field}_sample"].append(joker_data)
                        elif field == 'playingBookMarks':
                            result[f"{field}_sample"] = []
                            for item in array_data[:5]:  # 최대 5개 샘플
                                bookmark_data = {
                                    "bookmarkDataIndex": getattr(item, 'bookmarkDataIndex', 0),
                                    "userDataLv": getattr(item, 'userDataLv', 0),
                                    "_enhanceLv": getattr(item, '_enhanceLv', 0)
                                }
                                if hasattr(item, '_listSkills') and item._listSkills:
                                    bookmark_data["skills_count"] = len(item._listSkills)
                                if hasattr(item, '_listSkillsPlayed') and item._listSkillsPlayed:
                                    bookmark_data["skills_played_count"] = len(item._listSkillsPlayed)
                                result[f"{field}_sample"].append(bookmark_data)

                else:
                    # 빈 배열일 때도 빈 리스트로 저장
                    if field in ['originFullDeck', 'playingFullDeck', 'playingDeck', 'destroyedPlayingCards',
                                'playingJokers', 'playingBookMarks', 'pokerHandDataList']:
                        result[field] = []

        # 미션 정보
        if hasattr(game_info, 'currentMissionInfo') and game_info.HasField("currentMissionInfo"):
            mission = game_info.currentMissionInfo
            result["currentMissionInfo"] = {
                "MissionID": getattr(mission, 'MissionID', 0),
                "count": getattr(mission, 'count', 0),
                "isClear": getattr(mission, 'isClear', False),
                "round": getattr(mission, 'round', 0)
            }

            if hasattr(mission, 'rewardData') and mission.HasField("rewardData"):
                reward = mission.rewardData
                result["currentMissionInfo"]["rewardData"] = {
                    "type": get_enum_name(enums.ETB_IN_GAME_MISSION_REWARD_TYPE, getattr(reward, 'type', 0)),
                    "item_idx": getattr(reward, 'item_idx', 0),
                    "count": getattr(reward, 'count', 0),
                    "rate": getattr(reward, 'rate', 0)
                }

    except Exception:
        pass
    return result

def extract_playing_card_detailed(card):
    """PlayingCard_PROTO를 상세히 추출"""
    if not card:
        return None

    result = {}
    try:
        if hasattr(card, 'cardDataIndex'):
            result['cardDataIndex'] = card.cardDataIndex
        if hasattr(card, 'chip'):
            result['chip'] = card.chip
        if hasattr(card, 'isBack'):
            result['isBack'] = card.isBack

        # InGameCardLabel_PROTO 추출
        if hasattr(card, 'label') and card.label:
            result['label'] = extract_card_label_detailed(card.label)

    except Exception as e:
        print(f"Error extracting playing card: {e}")
        return None

    return result if result else None

def extract_card_label_detailed(label):
    """InGameCardLabel_PROTO를 상세히 추출"""
    if not label:
        return None

    result = {}
    try:
        if hasattr(label, 'labelDataIndex'):
            result['labelDataIndex'] = label.labelDataIndex

        # InGameAbility_PROTO 리스트 추출
        if hasattr(label, 'abilities') and label.abilities:
            result['abilities'] = extract_abilities_list(label.abilities)

    except Exception as e:
        print(f"Error extracting card label: {e}")
        return None

    return result if result else None

def extract_joker_detailed(joker):
    """InGameJoker_PROTO를 상세히 추출"""
    if not joker:
        return None

    result = {}
    try:
        if hasattr(joker, 'jokerDataIndex'):
            result['jokerDataIndex'] = joker.jokerDataIndex
        if hasattr(joker, 'userDataLv'):
            result['userDataLv'] = joker.userDataLv
        if hasattr(joker, 'isBack'):
            result['isBack'] = joker.isBack

        # InGameAbility_PROTO 리스트 추출
        if hasattr(joker, 'abilities') and joker.abilities:
            result['abilities'] = extract_abilities_list(joker.abilities)

    except Exception as e:
        print(f"Error extracting joker: {e}")
        return None

    return result if result else None

def extract_bookmark_detailed(bookmark):
    """InGameBookMark_PROTO를 상세히 추출"""
    if not bookmark:
        return None

    result = {}
    try:
        if hasattr(bookmark, 'bookmarkDataIndex'):
            result['bookmarkDataIndex'] = bookmark.bookmarkDataIndex
        if hasattr(bookmark, 'userDataLv'):
            result['userDataLv'] = bookmark.userDataLv
        if hasattr(bookmark, '_enhanceLv'):
            result['_enhanceLv'] = bookmark._enhanceLv

        # InGameBookMarkSkill_PROTO 리스트 추출
        if hasattr(bookmark, '_listSkills') and bookmark._listSkills:
            result['_listSkills'] = []
            for skill in bookmark._listSkills[:5]:  # 최대 5개
                skill_data = extract_bookmark_skill_detailed(skill)
                if skill_data:
                    result['_listSkills'].append(skill_data)

        if hasattr(bookmark, '_listSkillsPlayed') and bookmark._listSkillsPlayed:
            result['_listSkillsPlayed'] = []
            for skill in bookmark._listSkillsPlayed[:5]:  # 최대 5개
                skill_data = extract_bookmark_skill_detailed(skill)
                if skill_data:
                    result['_listSkillsPlayed'].append(skill_data)

    except Exception as e:
        print(f"Error extracting bookmark: {e}")
        return None

    return result if result else None

def extract_bookmark_skill_detailed(skill):
    """InGameBookMarkSkill_PROTO를 상세히 추출"""
    if not skill:
        return None

    result = {}
    try:
        if hasattr(skill, 'bookmarkDataIndex'):
            result['bookmarkDataIndex'] = skill.bookmarkDataIndex

        # InGameAbility_PROTO 리스트 추출
        if hasattr(skill, 'abilities') and skill.abilities:
            result['abilities'] = extract_abilities_list(skill.abilities)

    except Exception as e:
        print(f"Error extracting bookmark skill: {e}")
        return None

    return result if result else None

def extract_poker_hand_detailed(poker_hand):
    """PokerHandData_PROTO를 상세히 추출"""
    if not poker_hand:
        return None

    result = {}
    try:
        if hasattr(poker_hand, 'dataIndex'):
            result['dataIndex'] = poker_hand.dataIndex
        if hasattr(poker_hand, 'type'):
            result['type'] = get_enum_name(enums.ETB_Poker_Hands_Type, poker_hand.type)
        if hasattr(poker_hand, 'lv'):
            result['lv'] = poker_hand.lv
        if hasattr(poker_hand, 'chip'):
            result['chip'] = poker_hand.chip
        if hasattr(poker_hand, 'multiple'):
            result['multiple'] = poker_hand.multiple
        if hasattr(poker_hand, 'playCount'):
            result['playCount'] = poker_hand.playCount
        if hasattr(poker_hand, 'throwCount'):
            result['throwCount'] = poker_hand.throwCount

    except Exception as e:
        print(f"Error extracting poker hand: {e}")
        return None

    return result if result else None


def get_transaction_type(event_type: int) -> str:
    """이벤트 타입에 따라 거래 유형을 반환합니다."""
    # 구매 이벤트
    if event_type in [5, 7, 9]:  # BUY_JOKER, BUY_CONSUME_ITEM, BUY_SCROLL_ITEM
        return "구매"
    # 판매 이벤트
    elif event_type in [6, 8]:  # SELL_JOKER, SELL_CONSUME_ITEM
        return "판매"
    # 상점 관련 기타 이벤트
    elif event_type in [28, 29]:  # SHOP_OPEN, SHOP_PRODUCT_REROLL
        return "상점_기타"
    else:
        return "기타"

def parse_log_shop_statistics(file_path: str) -> pd.DataFrame:
    """상점 통계 데이터를 추출합니다.

    구매/판매 구분:
    - 구매: BUY_JOKER(5), BUY_CONSUME_ITEM(7), BUY_SCROLL_ITEM(9)
    - 판매: SELL_JOKER(6), SELL_CONSUME_ITEM(8)

    정확한 로그 구조:
    BattleLog_PROTO {
      EBT_BATTLE_LOG_EVENT_TYPE EventType = 1;
      string Source = 2;
      map<string, BattleLogPayloadDTO_PROTO> Payload = 3;
      repeated BattleLogPayloadDTO_PROTO PayloadOrder = 4;
    }

    BattleLogPayloadDTO_PROTO {
      string key = 1;
      EBT_BATTLE_LOG_PAYLOAD_TYPE payloadType = 2;
      BattleLogScalarValue_PROTO scalarValue = 3;
      BattleLogReferenceValue_PROTO referenceValue = 4;
    }

    BattleLogReferenceValue_PROTO에서:
    - repeated InGameShopProduct_PROTO productList = 3;
    - InGameShopProduct_PROTO product = 8;
    """
    import time
    start_time = time.time()

    try:
        # S3에서 파일 다운로드 및 압축 해제
        from s3_utils import download_and_decompress
        local_file_path = download_and_decompress(file_path)

        if local_file_path is None:
            logger.error(f"파일 다운로드 실패: {file_path}")
            return pd.DataFrame()

        # 로컬 파일에서 데이터 읽기 및 프로토 파싱
        with open(local_file_path, "rb") as f:
            data = f.read()

        queue = proto.TotalBattleLogQueue_PROTO()
        queue.ParseFromString(data)



        shop_events = []
        total_logs_checked = 0
        payloads_checked = 0
        reference_values_checked = 0
        processed_sales = set()  # 중복 제거를 위한 set

        for round_block in queue.rounds:
            round_idx = round_block.round_index

            for logqueue in round_block.logs:
                action_order = logqueue.oder

                for log in logqueue.logs:
                    total_logs_checked += 1

                    # Payload 맵 확인 (대문자 P)
                    if hasattr(log, 'Payload') and log.Payload:
                        for key, payload_dto in log.Payload.items():
                            payloads_checked += 1

                            # referenceValue 확인
                            if hasattr(payload_dto, 'referenceValue') and payload_dto.referenceValue:
                                reference_values_checked += 1
                                ref_val = payload_dto.referenceValue

                                # productList 처리 (필드 번호 3) - 상점 관련 키만 필터링
                                if (hasattr(ref_val, 'productList') and len(ref_val.productList) > 0 and
                                    ('product_list' in key or 'shop' in key.lower())):

                                    for i, product in enumerate(ref_val.productList):
                                        # 유효한 아이템만 수집 (itemType > 0)
                                        if product.itemType > 0:
                                            transaction_type = get_transaction_type(log.EventType)
                                            shop_events.append({
                                                'round_index': round_idx,
                                                'action_order': action_order,
                                                'log_sequence': total_logs_checked,
                                                'payload_key': key,
                                                'event_type': 'productList',
                                                'product_index_in_list': i,
                                                'item_type': get_enum_name(enums.ETB_CARD_TYPE, product.itemType),
                                                'item_index': product.itemIndex,
                                                'item_type_raw': product.itemType,
                                                'log_type': get_enum_name(enums.EBT_BATTLE_LOG_EVENT_TYPE, log.EventType),
                                                'log_type_raw': log.EventType,
                                                'transaction_type': transaction_type,
                                                'file_name': file_path.split('/')[-1]
                                            })

                                # product 처리 (필드 번호 8, 단일 상품) - 상점 관련 키만 필터링
                                if (hasattr(ref_val, 'product') and ref_val.product and
                                    ('product' in key or 'shop' in key.lower()) and
                                    ('vip' in key or 'product_list' in key)):  # VIP 상품이나 상점 관련만
                                    product = ref_val.product
                                    # 유효한 아이템만 수집 (itemType > 0)
                                    if product.itemType > 0:
                                        transaction_type = get_transaction_type(log.EventType)

                                        shop_events.append({
                                            'round_index': round_idx,
                                            'action_order': action_order,
                                            'log_sequence': total_logs_checked,
                                            'payload_key': key,
                                            'event_type': 'product',
                                            'product_index_in_list': 0,
                                            'item_type': get_enum_name(enums.ETB_CARD_TYPE, product.itemType),
                                            'item_index': product.itemIndex,
                                            'item_type_raw': product.itemType,
                                            'log_type': get_enum_name(enums.EBT_BATTLE_LOG_EVENT_TYPE, log.EventType),
                                            'log_type_raw': log.EventType,
                                            'transaction_type': transaction_type,
                                            'file_name': file_path.split('/')[-1]
                                        })

                                # 판매 이벤트 특별 처리 (scalarValue에서 아이템 정보 추출)
                                if (log.EventType in [6, 8] and  # SELL_JOKER, SELL_CONSUME_ITEM
                                    ('sell_' in key)):
                                    transaction_type = get_transaction_type(log.EventType)

                                    # 키에서 아이템 타입 추정 (올바른 enum 값 사용)
                                    if 'sell_joker' in key or 'joker' in key:
                                        item_type_enum = 2  # ETB_CARD_TYPE_JOKER
                                    elif 'consume_item' in key:
                                        item_type_enum = 3  # CONSUME_ITEM
                                    elif 'scroll_item' in key:
                                        item_type_enum = 4  # SCROLL_ITEM
                                    else:
                                        item_type_enum = 0  # NONE

                                    # 아이템 인덱스 추출 시도
                                    item_index = 0
                                    if hasattr(payload_dto, 'scalarValue') and payload_dto.scalarValue:
                                        scalar_val = payload_dto.scalarValue
                                        if hasattr(scalar_val, 'intValue') and scalar_val.intValue > 0:
                                            # intValue는 일반적으로 합리적인 범위
                                            item_index = scalar_val.intValue
                                        elif hasattr(scalar_val, 'ulongValue') and scalar_val.ulongValue > 0:
                                            # ulongValue는 너무 클 수 있으므로 범위 체크
                                            ulong_val = scalar_val.ulongValue
                                            if ulong_val < 100000:  # 합리적인 아이템 인덱스 범위
                                                item_index = ulong_val
                                        elif hasattr(scalar_val, 'jokerIndex') and scalar_val.jokerIndex > 0:
                                            item_index = scalar_val.jokerIndex

                                    # 아이템 인덱스 확인 및 기록 여부 결정
                                    should_record = False
                                    if item_index > 0:
                                        # 일반적인 경우: 유효한 인덱스가 있음
                                        should_record = True
                                    elif 'joker' in key and log.EventType == 6:  # SELL_JOKER
                                        # SELL_JOKER는 인덱스가 0이어도 기록 (조커 판매 행위 자체가 중요)
                                        # joker_index 키에서 실제 인덱스 확인 시도
                                        joker_index_found = False
                                        if hasattr(log, 'Payload') and log.Payload:
                                            for check_key, check_payload in log.Payload.items():
                                                if check_key == 'joker_index':
                                                    if (hasattr(check_payload, 'scalarValue') and check_payload.scalarValue and
                                                        hasattr(check_payload.scalarValue, 'intValue')):
                                                        potential_index = check_payload.scalarValue.intValue
                                                        if potential_index > 0:
                                                            item_index = potential_index
                                                            joker_index_found = True
                                                            break

                                        # joker_index에서도 유효한 값을 찾지 못했지만 조커 판매는 기록
                                        should_record = True
                                        if not joker_index_found:
                                            item_index = 0  # 일반 조커 판매로 기록

                                    # 인덱스 0 제외 및 중복 제거
                                    if should_record and item_index > 0:
                                        # 중복 제거를 위한 고유 키 생성
                                        unique_key = f"{round_idx}_{log.EventType}_{key}_{item_index}_{item_type_enum}"

                                        if unique_key not in processed_sales:
                                            processed_sales.add(unique_key)


                                            shop_events.append({
                                                'round_index': round_idx,
                                                'action_order': action_order,
                                                'log_sequence': total_logs_checked,
                                                'payload_key': key,
                                                'event_type': 'scalar_sale',
                                                'product_index_in_list': 0,
                                                'item_type': get_enum_name(enums.ETB_CARD_TYPE, item_type_enum),
                                                'item_index': item_index,
                                                'item_type_raw': item_type_enum,
                                                'log_type': get_enum_name(enums.EBT_BATTLE_LOG_EVENT_TYPE, log.EventType),
                                                'log_type_raw': log.EventType,
                                                'transaction_type': transaction_type,
                                                'file_name': file_path.split('/')[-1]
                                            })


                    # Payload 맵도 확인
                    if hasattr(log, 'Payload') and log.Payload:
                        for key, payload_dto in log.Payload.items():
                            payloads_checked += 1

                            # referenceValue 확인
                            if hasattr(payload_dto, 'referenceValue') and payload_dto.referenceValue:
                                reference_values_checked += 1
                                ref_val = payload_dto.referenceValue
                                # key는 이미 Payload.items()에서 가져옴

                                # productList 처리 - 상점 관련 키만 필터링
                                if (hasattr(ref_val, 'productList') and len(ref_val.productList) > 0 and
                                    ('product_list' in key or 'shop' in key.lower())):

                                    for i, product in enumerate(ref_val.productList):
                                        # 유효한 아이템만 수집 (itemType > 0)
                                        if product.itemType > 0:
                                            transaction_type = get_transaction_type(log.EventType)
                                            shop_events.append({
                                                'round_index': round_idx,
                                                'action_order': action_order,
                                                'log_sequence': total_logs_checked,
                                                'payload_key': key,
                                                'event_type': 'productList_order',
                                                'product_index_in_list': i,
                                                'item_type': get_enum_name(enums.ETB_CARD_TYPE, product.itemType),
                                                'item_index': product.itemIndex,
                                                'item_type_raw': product.itemType,
                                                'log_type': get_enum_name(enums.EBT_BATTLE_LOG_EVENT_TYPE, log.EventType),
                                                'log_type_raw': log.EventType,
                                                'transaction_type': transaction_type,
                                                'file_name': file_path.split('/')[-1]
                                            })

                                # product 처리 - 상점 관련 키만 필터링
                                if (hasattr(ref_val, 'product') and ref_val.product and
                                    ('product' in key or 'shop' in key.lower()) and
                                    ('vip' in key or 'product_list' in key)):  # VIP 상품이나 상점 관련만
                                    product = ref_val.product
                                    # 유효한 아이템만 수집 (itemType > 0)
                                    if product.itemType > 0:
                                        transaction_type = get_transaction_type(log.EventType)

                                        shop_events.append({
                                            'round_index': round_idx,
                                            'action_order': action_order,
                                            'log_sequence': total_logs_checked,
                                            'payload_key': key,
                                            'event_type': 'product_order',
                                            'product_index_in_list': 0,
                                            'item_type': get_enum_name(enums.ETB_CARD_TYPE, product.itemType),
                                            'item_index': product.itemIndex,
                                            'item_type_raw': product.itemType,
                                            'log_type': get_enum_name(enums.EBT_BATTLE_LOG_EVENT_TYPE, log.EventType),
                                            'log_type_raw': log.EventType,
                                            'transaction_type': transaction_type,
                                            'file_name': file_path.split('/')[-1]
                                        })

                                # 판매 이벤트 특별 처리 (Payload에서도)
                                if (log.EventType in [6, 8] and  # SELL_JOKER, SELL_CONSUME_ITEM
                                    ('sell_' in key)):

                                    transaction_type = get_transaction_type(log.EventType)

                                    # 키에서 아이템 타입 추정 (올바른 enum 값 사용)
                                    if 'sell_joker' in key or 'joker' in key:
                                        item_type_enum = 2  # ETB_CARD_TYPE_JOKER
                                    elif 'consume_item' in key:
                                        item_type_enum = 3  # CONSUME_ITEM
                                    elif 'scroll_item' in key:
                                        item_type_enum = 4  # SCROLL_ITEM
                                    else:
                                        item_type_enum = 0  # NONE

                                    # 아이템 인덱스 추출 시도
                                    item_index = 0

                                    # sell_joker 키 우선 처리
                                    if key == 'sell_joker':
                                        # referenceValue.joker.jokerDataIndex에서 직접 추출
                                        try:
                                            if (hasattr(payload_dto, 'referenceValue') and
                                                payload_dto.referenceValue and
                                                hasattr(payload_dto.referenceValue, 'joker') and
                                                payload_dto.referenceValue.joker and
                                                hasattr(payload_dto.referenceValue.joker, 'jokerDataIndex')):
                                                item_index = payload_dto.referenceValue.joker.jokerDataIndex
                                        except Exception:
                                            pass

                                    # 다른 키들은 scalarValue에서 처리
                                    elif hasattr(payload_dto, 'scalarValue') and payload_dto.scalarValue:
                                        scalar_val = payload_dto.scalarValue
                                        if hasattr(scalar_val, 'intValue') and scalar_val.intValue > 0:
                                            item_index = scalar_val.intValue
                                        elif hasattr(scalar_val, 'ulongValue') and scalar_val.ulongValue > 0:
                                            # ulongValue는 일반 아이템만 (합리적인 범위)
                                            ulong_val = scalar_val.ulongValue
                                            if 1 <= ulong_val <= 99999:
                                                item_index = ulong_val

                                    # 기록 여부 결정
                                    if 'sell_joker' in key:
                                        # sell_joker 키는 referenceValue에서 실제 jokerDataIndex를 찾은 경우만 기록
                                        should_record = item_index > 0
                                    else:
                                        # 다른 키는 0보다 큰 값만 기록
                                        should_record = item_index > 0

                                    if should_record:
                                        # 중복 제거를 위한 고유 키 생성
                                        unique_key = f"{round_idx}_{log.EventType}_{key}_{item_index}_{item_type_enum}"

                                        if unique_key not in processed_sales:
                                            processed_sales.add(unique_key)

                                            shop_events.append({
                                                'round_index': round_idx,
                                                'action_order': action_order,
                                                'log_sequence': total_logs_checked,
                                                'payload_key': key,
                                                'event_type': 'scalar_sale_order',
                                                'product_index_in_list': 0,
                                                'item_type': get_enum_name(enums.ETB_CARD_TYPE, item_type_enum),
                                                'item_index': item_index,
                                                'item_type_raw': item_type_enum,
                                                'log_type': get_enum_name(enums.EBT_BATTLE_LOG_EVENT_TYPE, log.EventType),
                                                'log_type_raw': log.EventType,
                                                'transaction_type': transaction_type,
                                                'file_name': file_path.split('/')[-1]
                                            })


        total_time = time.time() - start_time
        logger.info(f"상점 통계 파싱 완료: {total_logs_checked}개 로그, {payloads_checked}개 페이로드, {reference_values_checked}개 참조값, {len(shop_events)}개 상점 이벤트, 총 시간: {total_time:.2f}초")

        if shop_events:
            df = pd.DataFrame(shop_events)
            return df
        else:
            return pd.DataFrame()

    except Exception as e:
        total_time = time.time() - start_time
        logger.error(f"상점 통계 파싱 중 오류 ({total_time:.2f}초): {e}")
        import traceback
        logger.error(traceback.format_exc())
        return pd.DataFrame()


def process_single_file_for_shop(file_path: str) -> tuple:
    """단일 파일의 상점 통계를 처리합니다. (병렬 처리용)"""
    try:
        shop_df = parse_log_shop_statistics(file_path)
        return (file_path, shop_df, None)
    except Exception as e:
        return (file_path, None, str(e))

def get_shop_statistics_summary(file_paths: List[str]) -> Dict[str, Any]:
    """여러 파일의 상점 통계를 집계합니다."""
    import time
    from concurrent.futures import ThreadPoolExecutor, as_completed

    start_time = time.time()

    all_shop_data = []
    processed_files = 0
    failed_files = 0
    files_with_shop_data = 0

    logger.info(f"상점 통계 집계 시작: {len(file_paths)}개 파일")

    # 병렬 처리 (최대 4개 스레드)
    max_workers = min(4, len(file_paths))

    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        # 모든 파일을 병렬로 처리
        future_to_file = {executor.submit(process_single_file_for_shop, file_path): file_path
                         for file_path in file_paths}

        for i, future in enumerate(as_completed(future_to_file)):
            file_path, shop_df, error = future.result()
            processed_files += 1

            if error:
                failed_files += 1
                logger.error(f"❌ 파일 처리 실패: {file_path.split('/')[-1]} - {error}")
            elif shop_df is not None and not shop_df.empty:
                files_with_shop_data += 1
                all_shop_data.append(shop_df)
                logger.info(f"✅ 상점 데이터 발견: {file_path.split('/')[-1]} ({len(shop_df)}개 이벤트)")
            # 진행률 로깅
            if processed_files % 10 == 0 or processed_files == len(file_paths):
                elapsed = time.time() - start_time
                progress = processed_files / len(file_paths) * 100
                logger.info(f"진행률: {processed_files}/{len(file_paths)} ({progress:.1f}%), 경과 시간: {elapsed:.1f}초")

    total_duration = time.time() - start_time
    logger.info(f"상점 통계 처리 완료: 성공 {processed_files - failed_files}/{processed_files}개 파일, 상점 데이터 포함 {files_with_shop_data}개 파일, 총 소요시간: {total_duration:.1f}초")

    if not all_shop_data:
        return {
            'total_files': len(file_paths),
            'files_with_shop_data': 0,
            'total_shop_events': 0,
            'item_type_stats': {},
            'item_index_stats': {},
            'combined_stats': {}
        }

    # 모든 데이터 합치기
    combined_df = pd.concat(all_shop_data, ignore_index=True)

    # 기본 통계 계산
    item_type_stats = combined_df['item_type'].value_counts().to_dict()
    item_index_stats = combined_df['item_index'].value_counts().to_dict()

    # 거래 유형별 통계
    transaction_type_stats = combined_df['transaction_type'].value_counts().to_dict()

    # 구매/판매별 아이템 타입 통계
    purchase_stats = {}
    sale_stats = {}

    purchase_data = combined_df[combined_df['transaction_type'] == '구매']
    sale_data = combined_df[combined_df['transaction_type'] == '판매']

    if not purchase_data.empty:
        purchase_stats = {
            'total_count': len(purchase_data),
            'item_type_stats': purchase_data['item_type'].value_counts().to_dict(),
            'item_index_stats': purchase_data['item_index'].value_counts().to_dict()
        }

    if not sale_data.empty:
        sale_stats = {
            'total_count': len(sale_data),
            'item_type_stats': sale_data['item_type'].value_counts().to_dict(),
            'item_index_stats': sale_data['item_index'].value_counts().to_dict()
        }

    # 아이템 타입별 상세 분포 (구매/판매 구분)
    combined_stats = {}
    for item_type in combined_df['item_type'].unique():
        type_data = combined_df[combined_df['item_type'] == item_type]
        type_purchase = type_data[type_data['transaction_type'] == '구매']
        type_sale = type_data[type_data['transaction_type'] == '판매']

        combined_stats[item_type] = {
            'total_count': len(type_data),
            'purchase_count': len(type_purchase),
            'sale_count': len(type_sale),
            'unique_indices': type_data['item_index'].nunique(),
            'index_distribution': type_data['item_index'].value_counts().to_dict(),
            'purchase_indices': type_purchase['item_index'].value_counts().to_dict() if not type_purchase.empty else {},
            'sale_indices': type_sale['item_index'].value_counts().to_dict() if not type_sale.empty else {}
        }

    return {
        'total_files': len(file_paths),
        'files_with_shop_data': len(all_shop_data),
        'total_shop_events': len(combined_df),
        'item_type_stats': item_type_stats,
        'item_index_stats': item_index_stats,
        'transaction_type_stats': transaction_type_stats,
        'purchase_stats': purchase_stats,
        'sale_stats': sale_stats,
        'combined_stats': combined_stats,
        'raw_data': combined_df
    }