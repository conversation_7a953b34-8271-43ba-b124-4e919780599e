﻿syntax = "proto3";
package google.protobuf;

enum MAF_ANALYTICS_LOG_EVENT_TYPE
{
    MAF_ANALYTICS_LOG_EVENT_TYPE_NONE = 0;
    MAF_ANALYTICS_LOG_EVENT_TYPE_USER_CURRENCY_DATA = 1;
    MAF_ANALYTICS_LOG_EVENT_TYPE_LOG_FUNNEL = 2;
    MAF_ANALYTICS_LOG_EVENT_TYPE_LOG_AD = 3;
    MAF_ANALYTICS_LOG_EVENT_TYPE_LOG_PURCHASE = 4;
    MAF_ANALYTICS_LOG_EVENT_TYPE_LOG_UI_OPEN = 5;
    MAF_ANALYTICS_LOG_EVENT_TYPE_LOG_SERVER_FUNCTION = 6;
    MAF_ANALYTICS_LOG_EVENT_TYPE_LOG_LOGIN = 7;
}
    

enum MAF_ACTION_LOG_PAYLOAD_TYPE
{
    MAF_ACTION_LOG_PAYLOAD_TYPE_None = 0;
    MAF_ACTION_LOG_PAYLOAD_TYPE_Int = 1;
    MAF_ACTION_LOG_PAYLOAD_TYPE_Bool = 2;
    MAF_ACTION_LOG_PAYLOAD_TYPE_String = 3;
    MAF_ACTION_LOG_PAYLOAD_TYPE_IntList = 4;
    MAF_ACTION_LOG_PAYLOAD_TYPE_Float = 5;
    MAF_ACTION_LOG_PAYLOAD_TYPE_Double = 6;
    MAF_ACTION_LOG_PAYLOAD_TYPE_ULong = 7;
    MAF_ACTION_LOG_PAYLOAD_TYPE_StringDictionary = 8;
    MAF_ACTION_LOG_PAYLOAD_TYPE_StringDictionaryList = 9;
}