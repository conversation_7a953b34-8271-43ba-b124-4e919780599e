import os

# S3 관련 설정
AWS_ACCESS_KEY_ID = os.environ.get("AWS_ACCESS_KEY_ID", "********************")
AWS_SECRET_ACCESS_KEY = os.environ.get("AWS_SECRET_ACCESS_KEY", "CpEHZYjyIv9Ugtls7RvwIeeJNb89fxBTvWtATJ+j")
S3_BUCKET_NAME = "maf-ace" # 여기에 실제 S3 버킷 이름을 입력하세요
S3_REGION_NAME = "ap-northeast-2" # 여기에 실제 S3 리전을 입력하세요

# 로그 파일 경로
LOCAL_DOWNLOAD_DIR = "C:/SVN/MAF_UserActionTool/downloads"

# Protobuf 파일 경로 (컴파일된 파일)
ACTION_LOG_PROTO_FILE = "C:/SVN/MAF_UserActionTool/ActionLog_PROTO_pb2.py"
ACTION_LOG_ENUMS_FULL_PROTO_FILE = "C:/SVN/MAF_UserActionTool/ActionLogEnumsFull_pb2.py"

# 환경별 S3 로그 파일 경로 설정
S3_LOG_PREFIXES = {
    "dev": "Log/ActionLog_dev/",
    "live": "Log/ActionLog/"
}

# 기본 환경 (하위 호환성을 위해 유지)
S3_LOG_PREFIX = S3_LOG_PREFIXES["dev"]

# 환경 표시 이름
ENVIRONMENT_NAMES = {
    "dev": "🔧 Development",
    "live": "🚀 Live"
}

# 한국어 이름 매핑 파일 경로
BOOKMARK_NAME_FILE = "bookmakr_name.txt"
JOKER_NAME_FILE = "joker_name.txt"

# Streamlit 앱 설정
APP_TITLE = "User Action Log Analyzer"