"""
UI 컴포넌트 모듈

이 모듈은 Streamlit UI 컴포넌트들을 재사용 가능한 함수로 제공합니다.
"""

import streamlit as st
import pandas as pd
from typing import Dict, List, Any, Optional, Tuple
from config import (
    DEFAULT_MAP_HEIGHT, 
    MIN_MAP_HEIGHT, 
    MAX_MAP_HEIGHT, 
    MAP_HEIGHT_STEP,
    APP_METADATA
)
from data_utils import organize_files_by_date, get_data_summary


def setup_page_config():
    """페이지 설정을 구성합니다."""
    st.set_page_config(
        page_title=APP_METADATA["title"],
        page_icon="🎮",
        layout="wide",
        initial_sidebar_state="expanded"
    )


def display_header():
    """앱 헤더를 표시합니다."""
    st.title(f"🎮 {APP_METADATA['title']}")
    st.markdown(f"*{APP_METADATA['description']} v{APP_METADATA['version']}*")
    st.divider()


def create_file_selector(files: List[str]) -> Optional[str]:
    """파일 선택기를 생성합니다."""
    if not files:
        st.error("❌ 사용 가능한 로그 파일이 없습니다.")
        return None
    
    # 파일을 날짜별로 구조화
    date_map = organize_files_by_date(files)
    
    if not date_map:
        st.error("❌ 파일 구조를 파싱할 수 없습니다.")
        return None
    
    # 날짜 선택
    dates = sorted(date_map.keys(), reverse=True)
    selected_date = st.selectbox(
        "📅 날짜 선택",
        dates,
        help="분석할 로그 파일의 날짜를 선택하세요"
    )
    
    if not selected_date:
        return None
    
    # 로그 타입 선택
    log_types = list(date_map[selected_date].keys())
    selected_log_type = st.selectbox(
        "📂 로그 타입 선택",
        log_types,
        help="분석할 로그의 타입을 선택하세요"
    )
    
    if not selected_log_type:
        return None
    
    # 상태 선택 (Chapter의 경우)
    statuses = list(date_map[selected_date][selected_log_type].keys())
    selected_status = st.selectbox(
        "📊 상태 선택",
        statuses,
        help="분석할 로그의 상태를 선택하세요"
    )
    
    if not selected_status:
        return None
    
    # 파일 선택
    available_files = date_map[selected_date][selected_log_type][selected_status]
    if not available_files:
        st.error("❌ 선택한 조건에 해당하는 파일이 없습니다.")
        return None
    
    # 파일명만 표시하도록 변환
    file_display_names = [f.split('/')[-1] for f in available_files]
    file_mapping = dict(zip(file_display_names, available_files))
    
    selected_display_name = st.selectbox(
        "📄 파일 선택",
        file_display_names,
        help="분석할 구체적인 로그 파일을 선택하세요"
    )
    
    return file_mapping.get(selected_display_name)


def create_analysis_tabs() -> Dict[str, Any]:
    """분석 탭들을 생성합니다."""
    tab_names = [
        "📊 기본 분석",
        "📈 통계 분석", 
        "🗺️ Mind Map",
        "📋 상세 로그",
        "🔍 검색 & 필터"
    ]
    
    tabs = st.tabs(tab_names)
    
    return {
        "basic": tabs[0],
        "stats": tabs[1], 
        "mindmap": tabs[2],
        "detailed": tabs[3],
        "search": tabs[4]
    }


def create_mindmap_sidebar() -> Dict[str, Any]:
    """마인드맵 사이드바 설정을 생성합니다."""
    with st.sidebar:
        st.subheader("🗺️ Mind Map 설정")
        
        # 높이 설정
        map_height = st.slider(
            "📏 Mind Map 높이", 
            MIN_MAP_HEIGHT, 
            MAX_MAP_HEIGHT, 
            DEFAULT_MAP_HEIGHT, 
            MAP_HEIGHT_STEP,
            help="Mind Map의 표시 높이를 조정합니다"
        )
        
        # 표시 모드 선택
        expand_mode = st.selectbox(
            "🔽 초기 표시 모드",
            ["접힌 상태", "펼친 상태"],
            help="Mind Map의 초기 표시 상태를 선택합니다"
        )
        
        # PayloadType 필터링 옵션
        use_payload_filtering = st.checkbox(
            "🎯 PayloadType 기반 필터링 사용",
            value=True,
            help="PayloadType에 따라 실제로 사용되는 데이터만 표시합니다"
        )
        
        # 상세 정보 표시 옵션
        show_details = st.checkbox(
            "📋 상세 정보 표시",
            value=True,
            help="이벤트의 상세 정보를 표시합니다"
        )
        
        # 최대 이벤트 수 설정
        max_events = st.number_input(
            "🔢 최대 이벤트 수",
            min_value=100,
            max_value=50000,
            value=10000,
            step=1000,
            help="처리할 최대 이벤트 수를 설정합니다"
        )
    
    return {
        "height": map_height,
        "expand_mode": expand_mode,
        "use_payload_filtering": use_payload_filtering,
        "show_details": show_details,
        "max_events": max_events
    }


def display_data_summary(df: pd.DataFrame):
    """데이터 요약 정보를 표시합니다."""
    summary = get_data_summary(df)
    
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("📊 총 행 수", f"{summary['total_rows']:,}")
    
    with col2:
        st.metric("📋 총 컬럼 수", summary['total_columns'])
    
    with col3:
        memory_mb = summary['memory_usage'] / (1024 * 1024)
        st.metric("💾 메모리 사용량", f"{memory_mb:.1f} MB")
    
    with col4:
        status = "🔴 대용량" if summary['is_large'] else "🟢 일반"
        st.metric("📈 데이터 크기", status)


def display_loading_progress(message: str, progress: Optional[float] = None):
    """로딩 진행상황을 표시합니다."""
    if progress is not None:
        st.progress(progress, text=message)
    else:
        with st.spinner(message):
            pass


def create_filter_controls() -> Dict[str, Any]:
    """필터 컨트롤을 생성합니다."""
    st.subheader("🔍 필터 설정")
    
    col1, col2 = st.columns(2)
    
    with col1:
        event_type_filter = st.multiselect(
            "이벤트 타입 필터",
            options=[],  # 실제 데이터에서 동적으로 설정
            help="표시할 이벤트 타입을 선택하세요"
        )
    
    with col2:
        round_filter = st.slider(
            "라운드 범위",
            min_value=1,
            max_value=100,
            value=(1, 100),
            help="표시할 라운드 범위를 선택하세요"
        )
    
    return {
        "event_types": event_type_filter,
        "round_range": round_filter
    }


def display_error_message(error: Exception, context: str = ""):
    """에러 메시지를 표시합니다."""
    st.error(f"❌ 오류가 발생했습니다: {context}")
    with st.expander("🔍 상세 오류 정보"):
        st.code(str(error))


def display_success_message(message: str):
    """성공 메시지를 표시합니다."""
    st.success(f"✅ {message}")


def display_info_message(message: str):
    """정보 메시지를 표시합니다."""
    st.info(f"ℹ️ {message}")


def display_warning_message(message: str):
    """경고 메시지를 표시합니다."""
    st.warning(f"⚠️ {message}")


def create_download_button(data: Any, filename: str, label: str = "다운로드") -> bool:
    """다운로드 버튼을 생성합니다."""
    if isinstance(data, pd.DataFrame):
        csv_data = data.to_csv(index=False).encode('utf-8')
        return st.download_button(
            label=f"📥 {label}",
            data=csv_data,
            file_name=f"{filename}.csv",
            mime="text/csv"
        )
    elif isinstance(data, str):
        return st.download_button(
            label=f"📥 {label}",
            data=data.encode('utf-8'),
            file_name=f"{filename}.txt",
            mime="text/plain"
        )
    else:
        return False


def create_data_table(df: pd.DataFrame, height: int = 400):
    """데이터 테이블을 생성합니다."""
    st.dataframe(
        df,
        height=height,
        use_container_width=True
    )


def create_expandable_section(title: str, content: Any, expanded: bool = False):
    """확장 가능한 섹션을 생성합니다."""
    with st.expander(title, expanded=expanded):
        if isinstance(content, str):
            st.markdown(content)
        elif isinstance(content, pd.DataFrame):
            create_data_table(content)
        else:
            st.write(content)


def display_processing_time(start_time: float, end_time: float):
    """처리 시간을 표시합니다."""
    processing_time = end_time - start_time
    st.caption(f"⏱️ 처리 시간: {processing_time:.2f}초")
