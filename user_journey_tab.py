#!/usr/bin/env python3
"""
개별 유저 동선 분석 탭 모듈
"""

import streamlit as st
import pandas as pd
from user_journey_analyzer import search_user_logs

def render_user_journey_tab(selected_project_key, AVAILABLE_PROJECTS, get_s3_files_cached, selected_environment):
    """개별 유저 동선 분석 탭을 렌더링합니다."""
    
    st.header("👤 개별 유저 동선 분석")
    st.markdown("특정 사용자의 **모든 행동 로그**를 날짜별, 로그인 세션별로 분석합니다.")

    # 세션 상태 초기화
    if 'user_journey_result' not in st.session_state:
        st.session_state.user_journey_result = None
    if 'user_journey_user_id' not in st.session_state:
        st.session_state.user_journey_user_id = ""

    # 유저 ID 입력
    user_id = st.text_input("🔍 분석할 유저 ID를 입력하세요:", 
                           value=st.session_state.user_journey_user_id,
                           placeholder="예: 6f842dc0-44dd-11f0-b6cc-5916ecf66b1b")
    
    # 유저 ID가 변경되면 결과 초기화
    if user_id != st.session_state.user_journey_user_id:
        st.session_state.user_journey_user_id = user_id
        st.session_state.user_journey_result = None
    
    if user_id:
        st.markdown(f"**분석 대상 유저:** `{user_id}`")
        
        # 분석 시작 버튼 또는 기존 결과 표시
        if st.session_state.user_journey_result is None:
            if st.button("🚀 유저 동선 분석 시작", type="primary"):
                with st.spinner("사용자 로그를 검색하고 있습니다..."):
                    # 현재 프로젝트와 환경의 S3 파일 목록 가져오기
                    current_project = AVAILABLE_PROJECTS[selected_project_key]
                    s3_files = get_s3_files_cached(current_project['bucket'], selected_environment)
                    
                    if not s3_files:
                        st.error("S3에서 로그 파일을 찾을 수 없습니다.")
                    else:
                        # 사용자 로그 검색
                        result = search_user_logs(user_id, s3_files)
                        
                        if 'error' in result:
                            st.error(result['error'])
                            if 'processed_files' in result:
                                st.info(f"검색 완료: {result['processed_files']}개 파일 처리")
                        else:
                            # 결과를 세션 상태에 저장
                            st.session_state.user_journey_result = result
                            st.rerun()
        
        # 분석 결과가 있으면 표시
        if st.session_state.user_journey_result is not None:
            result = st.session_state.user_journey_result
            
            # 새로운 분석 버튼
            col1, col2 = st.columns([3, 1])
            with col2:
                if st.button("🔄 새로운 분석", type="secondary"):
                    st.session_state.user_journey_result = None
                    st.rerun()
            
            # 검색 결과 표시
            st.success(f"✅ 사용자 로그 발견!")
            
            # 요약 정보
            summary = result['summary']
            col1, col2, col3, col4 = st.columns(4)
            with col1:
                st.metric("총 로그 수", f"{result['total_records']:,}")
            with col2:
                st.metric("활동 일수", summary['total_days'])
            with col3:
                st.metric("총 로그인 횟수", summary['total_logins'])
            with col4:
                st.metric("발견 파일 수", f"{result['found_files']}/{result['processed_files']}")
            
            # 활동 기간
            if summary['date_range']['start']:
                st.info(f"**활동 기간:** {summary['date_range']['start']} ~ {summary['date_range']['end']}")
            
            # 날짜별 분석 결과 표시
            daily_analysis = result['daily_analysis']
            
            if daily_analysis:
                st.subheader("📅 날짜별 활동 분석")
                
                # 날짜 선택
                available_dates = sorted(daily_analysis.keys(), reverse=True)
                selected_date = st.selectbox(
                    "분석할 날짜를 선택하세요:",
                    available_dates,
                    format_func=lambda x: f"{x} ({daily_analysis[x]['login_sessions']}회 로그인, {daily_analysis[x]['total_logs']}개 로그)",
                    key="date_selector"
                )
                
                if selected_date:
                    day_data = daily_analysis[selected_date]
                    
                    st.markdown(f"### 📊 {selected_date} 활동 상세")
                    
                    # 일일 요약
                    col1, col2, col3 = st.columns(3)
                    with col1:
                        st.metric("로그인 세션", day_data['login_sessions'])
                    with col2:
                        st.metric("총 액션 수", day_data['total_logs'])
                    with col3:
                        duration = (day_data['time_range']['end'] - day_data['time_range']['start']).total_seconds() / 3600
                        st.metric("활동 시간", f"{duration:.1f}시간")
                    
                    # 로그인 세션별 분석 (모든 세션을 순서대로 표시)
                    sessions = day_data['sessions']
                    if sessions:
                        st.markdown("#### 🔐 로그인 세션별 분석")

                        # 모든 세션을 순서대로 표시
                        for session_idx, session in enumerate(sessions, 1):
                            # 세션 타이틀 (강조)
                            st.markdown(f"### 🎯 로그인 {session_idx}번 - {session['login_time'].strftime('%H:%M:%S')}")

                            # 세션 요약 정보
                            col1, col2, col3 = st.columns(3)
                            with col1:
                                st.metric("세션 액션 수", session['total_actions'])
                            with col2:
                                st.metric("세션 지속 시간", f"{session['duration_minutes']:.1f}분")
                            with col3:
                                if 'event_summary' in session:
                                    unique_events = len(session['event_summary'])
                                    st.metric("액션 종류", f"{unique_events}개")

                            # 세션 내 액션 분포
                            if 'event_summary' in session and session['event_summary']:
                                st.markdown("**세션 내 액션 분포:**")
                                session_event_df = pd.DataFrame(
                                    list(session['event_summary'].items()),
                                    columns=['Event Type', 'Count']
                                ).sort_values('Count', ascending=False)
                                st.dataframe(session_event_df, use_container_width=True, hide_index=True)
                            else:
                                st.markdown("**세션 내 액션 분포:** 데이터 없음")

                            # 세션 로그 상세 (기본적으로 펼쳐짐)
                            with st.expander(f"🔍 세션 {session_idx} 로그 상세 보기", expanded=True):
                                session_logs_df = pd.DataFrame(session['session_logs'])
                                if not session_logs_df.empty:
                                    # Toast 관련 로그 필터링 (payload_ui_name에 Toast가 포함된 로그 제거)
                                    if 'payload_ui_name' in session_logs_df.columns:
                                        session_logs_df = session_logs_df[
                                            ~session_logs_df['payload_ui_name'].astype(str).str.contains('Toast', case=False, na=False)
                                        ]

                                    # 시간 순으로 정렬
                                    if 'time' in session_logs_df.columns:
                                        session_logs_df = session_logs_df.sort_values('time')

                                    # 컬럼 선택 옵션 제공
                                    st.markdown("**표시할 컬럼 선택:**")

                                    # 기본 컬럼
                                    basic_columns = ['event_type', 'time']
                                    # payload 컬럼들
                                    payload_columns = [col for col in session_logs_df.columns if col.startswith('payload_')]

                                    # 컬럼 선택 UI (2열로 배치)
                                    col1, col2 = st.columns(2)

                                    with col1:
                                        show_basic = st.checkbox("기본 정보 (이벤트 타입, 시간)", value=True, key=f"basic_{session_idx}")
                                        show_all_payload = st.checkbox("모든 Payload 컬럼 표시", value=False, key=f"all_payload_{session_idx}")

                                    with col2:
                                        # 주요 payload 컬럼들 개별 선택
                                        important_payloads = [col for col in payload_columns if any(keyword in col.lower() for keyword in ['ui', 'user_id', 'device_id', 'goods', 'currency'])]
                                        selected_payloads = []

                                        if not show_all_payload and important_payloads:
                                            st.markdown("**주요 Payload:**")
                                            for payload in important_payloads[:6]:  # 최대 6개까지
                                                if st.checkbox(payload.replace('payload_', ''), value=True, key=f"{payload}_{session_idx}"):
                                                    selected_payloads.append(payload)

                                    # 표시할 컬럼 결정
                                    display_columns = []
                                    if show_basic:
                                        display_columns.extend(basic_columns)

                                    if show_all_payload:
                                        display_columns.extend(payload_columns)
                                    else:
                                        display_columns.extend(selected_payloads)

                                    # 실제 존재하는 컬럼만 필터링
                                    available_columns = [col for col in display_columns if col in session_logs_df.columns]

                                    if available_columns:
                                        # 데이터프레임 표시 (높이 제한 제거하고 스크롤 가능하게)
                                        st.dataframe(
                                            session_logs_df[available_columns],
                                            use_container_width=True,
                                            hide_index=True,
                                            height=400  # 고정 높이로 스크롤 가능
                                        )

                                        # 추가 정보: 총 로그 수
                                        st.caption(f"총 {len(session_logs_df)}개의 로그 | 표시 중인 컬럼: {len(available_columns)}개")
                                    else:
                                        st.warning("표시할 컬럼이 선택되지 않았습니다.")
                                else:
                                    st.info("세션 로그가 없습니다.")

                            # 세션 간 구분선
                            if session_idx < len(sessions):
                                st.divider()
            else:
                st.warning("날짜별 분석 데이터가 없습니다.")
    else:
        st.info("👆 위에 유저 ID를 입력하고 분석을 시작해보세요!")
        
        # 사용 예시
        st.markdown("### 💡 사용 예시")
        st.code("6f842dc0-44dd-11f0-b6cc-5916ecf66b1b")
        st.markdown("**분석 결과:**")
        st.markdown("- 📅 **날짜별 탭**: 사용자가 활동한 모든 날짜")
        st.markdown("- 🔐 **로그인 세션**: 각 날짜의 로그인 횟수별 구분")
        st.markdown("- 📊 **행동 분석**: 각 세션에서 수행한 모든 액션 상세")
