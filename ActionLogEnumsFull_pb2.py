# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: ActionLogEnumsFull.proto
# Protobuf Python Version: 4.25.6
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x18\x41\x63tionLogEnumsFull.proto\x12\x0fgoogle.protobuf*\x8f\x03\n\x1cMAF_ANALYTICS_LOG_EVENT_TYPE\x12%\n!MAF_ANALYTICS_LOG_EVENT_TYPE_NONE\x10\x00\x12\x33\n/MAF_ANALYTICS_LOG_EVENT_TYPE_USER_CURRENCY_DATA\x10\x01\x12+\n\'MAF_ANALYTICS_LOG_EVENT_TYPE_LOG_FUNNEL\x10\x02\x12\'\n#MAF_ANALYTICS_LOG_EVENT_TYPE_LOG_AD\x10\x03\x12-\n)MAF_ANALYTICS_LOG_EVENT_TYPE_LOG_PURCHASE\x10\x04\x12,\n(MAF_ANALYTICS_LOG_EVENT_TYPE_LOG_UI_OPEN\x10\x05\x12\x34\n0MAF_ANALYTICS_LOG_EVENT_TYPE_LOG_SERVER_FUNCTION\x10\x06\x12*\n&MAF_ANALYTICS_LOG_EVENT_TYPE_LOG_LOGIN\x10\x07*\xbd\x03\n\x1bMAF_ACTION_LOG_PAYLOAD_TYPE\x12$\n MAF_ACTION_LOG_PAYLOAD_TYPE_None\x10\x00\x12#\n\x1fMAF_ACTION_LOG_PAYLOAD_TYPE_Int\x10\x01\x12$\n MAF_ACTION_LOG_PAYLOAD_TYPE_Bool\x10\x02\x12&\n\"MAF_ACTION_LOG_PAYLOAD_TYPE_String\x10\x03\x12\'\n#MAF_ACTION_LOG_PAYLOAD_TYPE_IntList\x10\x04\x12%\n!MAF_ACTION_LOG_PAYLOAD_TYPE_Float\x10\x05\x12&\n\"MAF_ACTION_LOG_PAYLOAD_TYPE_Double\x10\x06\x12%\n!MAF_ACTION_LOG_PAYLOAD_TYPE_ULong\x10\x07\x12\x30\n,MAF_ACTION_LOG_PAYLOAD_TYPE_StringDictionary\x10\x08\x12\x34\n0MAF_ACTION_LOG_PAYLOAD_TYPE_StringDictionaryList\x10\tb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'ActionLogEnumsFull_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  DESCRIPTOR._options = None
  _globals['_MAF_ANALYTICS_LOG_EVENT_TYPE']._serialized_start=46
  _globals['_MAF_ANALYTICS_LOG_EVENT_TYPE']._serialized_end=445
  _globals['_MAF_ACTION_LOG_PAYLOAD_TYPE']._serialized_start=448
  _globals['_MAF_ACTION_LOG_PAYLOAD_TYPE']._serialized_end=893
# @@protoc_insertion_point(module_scope)
