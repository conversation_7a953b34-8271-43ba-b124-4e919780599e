"""
한국어 이름 매핑 모듈

이 모듈은 게임 내 아이템, 캐릭터, 조커 등의 인덱스를 
한국어 이름으로 매핑하는 기능을 제공합니다.
"""

import os
from functools import lru_cache
from typing import Dict, Optional
from config import BOOKMARK_NAME_FILE, JOKER_NAME_FILE

# 추가 이름 파일들
PLAYING_CARD_NAME_FILE = "playing_card_name.txt"
CARD_LABEL_NAME_FILE = "card_label_name.txt"
SCROLL_ITEM_NAME_FILE = "scroll_item_name.txt"
CONSUME_ITEM_NAME_FILE = "consume_item_name.txt"


class KoreanNameMapper:
    """한국어 이름 매핑을 관리하는 클래스"""

    def __init__(self):
        self._bookmark_names: Dict[int, str] = {}
        self._joker_names: Dict[int, str] = {}
        self._playing_card_names: Dict[int, str] = {}
        self._card_label_names: Dict[int, str] = {}
        self._scroll_item_names: Dict[int, str] = {}
        self._consume_item_names: Dict[int, str] = {}
        self._loaded = False
    
    def _load_names(self):
        """이름 매핑 데이터를 파일에서 로드합니다."""
        if self._loaded:
            return

        self._load_bookmark_names()
        self._load_joker_names()
        self._load_playing_card_names()
        self._load_card_label_names()
        self._load_scroll_item_names()
        self._load_consume_item_names()
        self._loaded = True
    
    def _load_bookmark_names(self):
        """책갈피 이름을 로드합니다."""
        try:
            if os.path.exists(BOOKMARK_NAME_FILE):
                with open(BOOKMARK_NAME_FILE, 'r', encoding='utf-8') as f:
                    for line in f:
                        line = line.strip()
                        if line and ' ' in line:
                            parts = line.split(' ', 1)
                            try:
                                idx = int(parts[0])
                                name = parts[1]
                                self._bookmark_names[idx] = name
                            except ValueError:
                                continue
        except Exception as e:
            print(f"책갈피 이름 로드 중 오류: {e}")
    
    def _load_joker_names(self):
        """조커 이름을 로드합니다."""
        try:
            if os.path.exists(JOKER_NAME_FILE):
                with open(JOKER_NAME_FILE, 'r', encoding='utf-8') as f:
                    for line in f:
                        line = line.strip()
                        if line and ' ' in line:
                            parts = line.split(' ', 1)
                            try:
                                idx = int(parts[0])
                                name = parts[1]
                                self._joker_names[idx] = name
                            except ValueError:
                                continue
        except Exception as e:
            print(f"조커 이름 로드 중 오류: {e}")

    def _load_playing_card_names(self):
        """카드 이름을 로드합니다."""
        try:
            if os.path.exists(PLAYING_CARD_NAME_FILE):
                with open(PLAYING_CARD_NAME_FILE, 'r', encoding='utf-8') as f:
                    for line in f:
                        line = line.strip()
                        if line and ' ' in line:
                            parts = line.split(' ', 1)
                            try:
                                idx = int(parts[0])
                                name = parts[1]
                                self._playing_card_names[idx] = name
                            except ValueError:
                                continue
        except Exception as e:
            print(f"카드 이름 로드 중 오류: {e}")

    def _load_card_label_names(self):
        """카드 라벨 이름을 로드합니다."""
        try:
            if os.path.exists(CARD_LABEL_NAME_FILE):
                with open(CARD_LABEL_NAME_FILE, 'r', encoding='utf-8') as f:
                    for line in f:
                        line = line.strip()
                        if line and ' ' in line:
                            parts = line.split(' ', 1)
                            try:
                                idx = int(parts[0])
                                name = parts[1]
                                self._card_label_names[idx] = name
                            except ValueError:
                                continue
        except Exception as e:
            print(f"카드 라벨 이름 로드 중 오류: {e}")

    def _load_scroll_item_names(self):
        """스크롤 아이템 이름을 로드합니다."""
        try:
            if os.path.exists(SCROLL_ITEM_NAME_FILE):
                with open(SCROLL_ITEM_NAME_FILE, 'r', encoding='utf-8') as f:
                    for line in f:
                        line = line.strip()
                        if line and ' ' in line:
                            parts = line.split(' ', 1)
                            try:
                                idx = int(parts[0])
                                name = parts[1]
                                self._scroll_item_names[idx] = name
                            except ValueError:
                                continue
        except Exception as e:
            print(f"스크롤 아이템 이름 로드 중 오류: {e}")

    def _load_consume_item_names(self):
        """소모 아이템 이름을 로드합니다."""
        try:
            if os.path.exists(CONSUME_ITEM_NAME_FILE):
                with open(CONSUME_ITEM_NAME_FILE, 'r', encoding='utf-8') as f:
                    for line in f:
                        line = line.strip()
                        if line and ' ' in line:
                            parts = line.split(' ', 1)
                            try:
                                idx = int(parts[0])
                                name = parts[1]
                                self._consume_item_names[idx] = name
                            except ValueError:
                                continue
        except Exception as e:
            print(f"소모 아이템 이름 로드 중 오류: {e}")

    def get_bookmark_name(self, index: int) -> str:
        """책갈피 인덱스에 해당하는 한국어 이름을 반환합니다."""
        self._load_names()
        korean_name = self._bookmark_names.get(index)
        if korean_name:
            return f"{korean_name}(Idx:{index})"
        return f"책갈피 {index}"

    def get_joker_name(self, index: int) -> str:
        """조커 인덱스에 해당하는 한국어 이름을 반환합니다."""
        self._load_names()
        korean_name = self._joker_names.get(index)
        if korean_name:
            return f"{korean_name}(Idx:{index})"
        return f"조커 {index}"

    def get_playing_card_name(self, index: int) -> str:
        """카드 인덱스에 해당하는 한국어 이름을 반환합니다."""
        if index == 0:
            return "카드 없음"
        self._load_names()
        korean_name = self._playing_card_names.get(index)
        if korean_name:
            return f"{korean_name}(Idx:{index})"
        return f"Card {index:03d}"

    def get_card_label_name(self, index: int) -> str:
        """카드 라벨 인덱스에 해당하는 한국어 이름을 반환합니다."""
        self._load_names()
        korean_name = self._card_label_names.get(index)
        if korean_name:
            return f"{korean_name}(Idx:{index})"
        return f"카드라벨 {index}"

    def get_scroll_item_name(self, index: int) -> str:
        """스크롤 아이템 인덱스에 해당하는 한국어 이름을 반환합니다."""
        self._load_names()
        korean_name = self._scroll_item_names.get(index)
        if korean_name:
            return f"{korean_name}(Idx:{index})"
        return f"스크롤 {index}"

    def get_consume_item_name(self, index: int) -> str:
        """소모 아이템 인덱스에 해당하는 한국어 이름을 반환합니다."""
        self._load_names()
        korean_name = self._consume_item_names.get(index)
        if korean_name:
            return f"{korean_name}(Idx:{index})"
        return f"소모아이템 {index}"

    def get_card_name(self, index: int) -> str:
        """카드 인덱스에 해당하는 이름을 반환합니다. (호환성 유지)"""
        return self.get_playing_card_name(index)

    def get_ability_name(self, index: int) -> str:
        """어빌리티 인덱스에 해당하는 이름을 반환합니다."""
        return f"어빌리티 Idx ({index})"

    def get_monster_name(self, index: int) -> str:
        """몬스터 인덱스에 해당하는 이름을 반환합니다."""
        return f"몬스터 {index}"

    def get_item_name(self, index: int) -> str:
        """아이템 인덱스에 해당하는 이름을 반환합니다. (일반 아이템용)"""
        return f"아이템 {index}"

    def get_scroll_name(self, index: int) -> str:
        """스크롤 인덱스에 해당하는 이름을 반환합니다. (호환성 유지)"""
        return self.get_scroll_item_name(index)


# 전역 인스턴스 생성
_name_mapper = KoreanNameMapper()


# 편의 함수들
@lru_cache(maxsize=1000)
def get_bookmark_name(index: int) -> str:
    """책갈피 한국어 이름 반환 (캐싱됨)"""
    return _name_mapper.get_bookmark_name(index)


@lru_cache(maxsize=1000)
def get_joker_name(index: int) -> str:
    """조커 한국어 이름 반환 (캐싱됨)"""
    return _name_mapper.get_joker_name(index)


@lru_cache(maxsize=1000)
def get_playing_card_name(index: int) -> str:
    """카드 한국어 이름 반환 (캐싱됨)"""
    return _name_mapper.get_playing_card_name(index)


@lru_cache(maxsize=1000)
def get_card_label_name(index: int) -> str:
    """카드 라벨 한국어 이름 반환 (캐싱됨)"""
    return _name_mapper.get_card_label_name(index)


@lru_cache(maxsize=1000)
def get_scroll_item_name(index: int) -> str:
    """스크롤 아이템 한국어 이름 반환 (캐싱됨)"""
    return _name_mapper.get_scroll_item_name(index)


@lru_cache(maxsize=1000)
def get_consume_item_name(index: int) -> str:
    """소모 아이템 한국어 이름 반환 (캐싱됨)"""
    return _name_mapper.get_consume_item_name(index)


@lru_cache(maxsize=1000)
def get_card_name(index: int) -> str:
    """카드 이름 반환 (캐싱됨) - 호환성 유지"""
    return _name_mapper.get_card_name(index)


@lru_cache(maxsize=1000)
def get_ability_name(index: int) -> str:
    """어빌리티 이름 반환 (캐싱됨)"""
    return _name_mapper.get_ability_name(index)


@lru_cache(maxsize=1000)
def get_monster_name(index: int) -> str:
    """몬스터 이름 반환 (캐싱됨)"""
    return _name_mapper.get_monster_name(index)


@lru_cache(maxsize=1000)
def get_item_name(index: int) -> str:
    """아이템 이름 반환 (캐싱됨)"""
    return _name_mapper.get_item_name(index)


@lru_cache(maxsize=1000)
def get_scroll_name(index: int) -> str:
    """스크롤 이름 반환 (캐싱됨) - 호환성 유지"""
    return _name_mapper.get_scroll_name(index)


def get_localized_name(item_type: str, index: int) -> str:
    """타입에 따라 적절한 한국어 이름을 반환합니다."""
    type_mapping = {
        'bookmark': get_bookmark_name,
        'joker': get_joker_name,
        'playing_card': get_playing_card_name,
        'card': get_card_name,  # 호환성 유지
        'card_label': get_card_label_name,
        'scroll_item': get_scroll_item_name,
        'consume_item': get_consume_item_name,
        'scroll': get_scroll_name,  # 호환성 유지
        'ability': get_ability_name,
        'monster': get_monster_name,
        'item': get_item_name
    }

    mapper_func = type_mapping.get(item_type.lower())
    if mapper_func:
        return mapper_func(index)
    else:
        return f"{item_type} {index}"


def clear_cache():
    """이름 매핑 캐시를 클리어합니다."""
    get_bookmark_name.cache_clear()
    get_joker_name.cache_clear()
    get_playing_card_name.cache_clear()
    get_card_label_name.cache_clear()
    get_scroll_item_name.cache_clear()
    get_consume_item_name.cache_clear()
    get_card_name.cache_clear()
    get_ability_name.cache_clear()
    get_monster_name.cache_clear()
    get_item_name.cache_clear()
    get_scroll_name.cache_clear()


def reload_names():
    """이름 매핑 데이터를 다시 로드합니다."""
    global _name_mapper
    _name_mapper._loaded = False
    clear_cache()
    _name_mapper._load_names()


# 한국어 라벨 매핑
KOREAN_LABELS = {
    "능력들": "어빌리티 목록",
    "능력": "어빌리티",
    "값들": "values", 
    "조건들": "conditions",
    "카드들": "카드 목록",
    "데이터": "data",
    "정보": "info",
    "상태": "status",
    "결과": "result",
    "타입": "type",
    "인덱스": "index",
    "개수": "count",
    "레벨": "level",
    "체력": "HP",
    "공격력": "attack",
    "방어력": "defense",
    "속도": "speed"
}


def get_korean_label(key: str) -> str:
    """영어 키를 한국어 라벨로 변환합니다."""
    return KOREAN_LABELS.get(key, key)


def get_item_name_by_type(item_type: str, index: int) -> str:
    """아이템 타입에 따라 적절한 한국어 이름을 반환합니다."""
    # 타입 이름 정규화
    item_type_upper = item_type.upper()

    if item_type_upper in ["JOKER", "조커"]:
        return get_joker_name(index)
    elif item_type_upper in ["CONSUME_ITEM", "소모아이템", "소모 아이템"]:
        return get_consume_item_name(index)
    elif item_type_upper in ["SCROLL_ITEM", "스크롤아이템", "스크롤 아이템"]:
        return get_scroll_item_name(index)
    elif item_type_upper in ["PLAYING_CARD", "카드"]:
        return get_playing_card_name(index)
    elif item_type_upper in ["CARD_LABEL", "카드라벨", "카드 라벨"]:
        return get_card_label_name(index)
    elif item_type_upper in ["BOOKMARK", "책갈피"]:
        return get_bookmark_name(index)
    else:
        return f"{item_type} {index}"


def get_shop_item_type_korean(item_type: str) -> str:
    """상점 아이템 타입을 한국어로 변환합니다."""
    type_mapping = {
        "JOKER": "조커",
        "CONSUME_ITEM": "소모 아이템",
        "SCROLL_ITEM": "스크롤 아이템",
        "조커": "조커",
        "소모아이템": "소모 아이템",
        "스크롤아이템": "스크롤 아이템"
    }
    return type_mapping.get(item_type.upper(), item_type)
