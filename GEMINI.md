# 📖 책갈피 레벨&돌파 통계 구현 및 문제 해결

## 🎯 프로젝트 개요
- **목표**: 책갈피 레벨&돌파 통계 탭 구현
- **데이터 소스**: CurrentGoodsData 로그의 payload_goods_data에서 bookmark 정보 추출
- **주요 기능**: 개별 사용자 bookmark 테이블 및 전체 p_lv 통계 생성

## 🚨 발견된 주요 문제

### 1. 잘못된 구현 방식
**문제**: bookmark_analyzer.py가 완전히 잘못된 방식으로 구현됨
- ❌ 직접 Protobuf 파싱 (복잡하고 오류 발생)
- ❌ 수동 이벤트 타입 필터링
- ❌ 수동 Payload 추출

**올바른 방식**: 기존 검증된 코드 패턴 사용
- ✅ `log_parser.process_log_file()` 사용
- ✅ DataFrame 기반 처리
- ✅ 간단한 컬럼 접근

### 2. 터미널 로그 분석 결과
```
모든 파일에서 MAF_ANALYTICS_LOG_EVENT_TYPE_USER_CURRENT_GOODS_DATA 이벤트가 0개 발견
→ 수집된 로그 0개 → 사용자 데이터 0개 → 통계 없음
```

## 🔧 해결 과정

### 1단계: 기존 코드 분석
- **log_parser.py**: `process_log_file()` 함수로 DataFrame 생성
- **purchase_analyzer.py**: `log_parser.process_log_file()` 호출해서 DataFrame 사용
- **모든 이벤트 타입과 payload 데이터가 DataFrame 컬럼으로 존재**

### 2단계: 올바른 구현으로 완전 재작성

#### 핵심 변경사항:
```python
# 기존 (잘못된 방식)
def parse_log_file_for_bookmark_analysis(file_path: str):
    # 직접 Protobuf 파싱
    with open(file_path, 'rb') as f:
        compressed_data = f.read()
    dctx = zstd.ZstdDecompressor()
    decompressed_data = dctx.decompress(compressed_data)
    maf_action_log_queue = MafActionLogQueue_PROTO()
    # ... 복잡한 수동 처리

# 수정 (올바른 방식)
def parse_log_file_for_bookmark_analysis(file_path: str):
    from log_parser import process_log_file
    df = process_log_file(file_path)
    current_goods_df = df[df['event_type'] == 'MAF_ANALYTICS_LOG_EVENT_TYPE_USER_CURRENT_GOODS_DATA']
    # ... 간단한 DataFrame 처리
```

### 3단계: 시간 처리 개선
purchase_analyzer.py와 동일한 방식 적용:
```python
# 문자열 시간 처리
if isinstance(time_value, str):
    if 'T' in time_value:
        time_value = datetime.fromisoformat(time_value.replace('Z', '+00:00'))
    else:
        time_value = datetime.strptime(time_value, '%Y-%m-%d %H:%M:%S')
# timestamp 처리
elif isinstance(time_value, (int, float)):
    time_value = datetime.fromtimestamp(time_value / 1000)
```

### 4단계: 안전한 데이터 추출
pandas nan 값 처리:
```python
def clean_value(value):
    if pd.isna(value) or str(value).strip() == 'nan':
        return None
    return value

json_data = clean_value(row.get('payload_goods_data'))
```

## ✅ 최종 구현 결과

### 검증된 구현 요소들:

#### 1. 올바른 로그 파싱
- `log_parser.process_log_file()` 사용 (검증된 방식)
- DataFrame 기반 처리
- 컬럼명 일치: `payload_goods_data` ✅

#### 2. 정확한 시간 처리
- purchase_analyzer.py와 동일한 로직
- 문자열/timestamp 모두 지원
- ISO 형식 및 일반 형식 파싱

#### 3. 안전한 데이터 추출
- pandas nan 값 처리 (`clean_value` 함수)
- 예외 처리 및 로깅
- 빈 값 검증

#### 4. 완전한 사용자 ID 처리
- 파일명에서 추출: `filename.split('_')[0]`
- 로그에 추가: `log['user_id'] = user_id`
- 검증 및 필터링

#### 5. 효율적인 캐싱
- 파일별 캐싱 (점진적)
- 전체 결과 캐싱
- 24시간 유효 기간

#### 6. 견고한 오류 처리
- 각 단계별 예외 처리
- 상세한 로깅 (INFO 레벨)
- 실패 시 빈 결과 반환

## 🚀 기대 결과
```
파일 user123.pb.zst: 전체 1500개 로그 중 3개의 CurrentGoodsData 이벤트 발견
유효한 CurrentGoodsData 로그 추가: jsonData 길이 2048
파일 user123.pb.zst: 3개의 유효한 CurrentGoodsData 로그 추출
수집된 CurrentGoodsData 로그 총 45개 처리 시작
사용자 user123: 30개 bookmark 데이터 추출 성공
처리 완료: 15명 성공, 0개 실패
총 450개 bookmark 데이터 수집, 30개 고유 bookmark ID
통계 데이터 생성 완료: 120개 항목
```

## 📋 주요 학습 사항

### 1. 기존 코드 패턴 활용의 중요성
- 새로운 기능 구현 시 기존 검증된 코드 패턴을 반드시 참고
- 직접 구현보다는 기존 함수 활용이 안전하고 효율적

### 2. 정밀한 문제 진단
- 터미널 로그 분석을 통한 정확한 문제 파악
- 단계별 디버깅으로 근본 원인 발견

### 3. 일관된 구현 방식
- 동일한 프로젝트 내에서는 일관된 패턴 사용
- 시간 처리, 데이터 추출, 오류 처리 방식 통일

## 🎯 결론
**완전히 새로운 올바른 구현으로 재작성하여 문제 해결 완료**
- 검증된 log_parser 사용으로 안정성 확보
- purchase_analyzer와 동일한 패턴으로 일관성 유지
- 효율적인 캐싱 시스템으로 성능 최적화
