﻿syntax = "proto3";
package google.protobuf;
//import "google/protobuf/any.proto";

import "ActionLogEnumsFull.proto";

message MafActionLogQueue_PROTO {
    int32 oder = 1;
    repeated MafActionLog_PROTO logs = 2;
}

message MafActionLog_PROTO {
  MAF_ANALYTICS_LOG_EVENT_TYPE EventType = 1;
  string Source = 2;
  map<string, MafActionLogPayloadDTO_PROTO> Payload = 3;
}

message MafActionLogPayloadDTO_PROTO
{
  string key = 1;
  MAF_ACTION_LOG_PAYLOAD_TYPE payloadType = 2;

  MafActionLogScalarValue_PROTO scalarValue = 3;
  MafActionLogReferenceValue_PROTO referenceValue = 4;
}

message MafActionLogScalarValue_PROTO{
  int32 intValue = 1;
  uint64 ulongValue = 2;
  bool boolValue = 3;
  string stringValue = 4;
}

message MafActionLogReferenceValue_PROTO{
  repeated int32 intList = 1;
}
