#!/usr/bin/env python3
"""
개별 유저 동선 분석 모듈
"""

import os
import re
import pandas as pd
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional
from log_parser import process_log_file
from s3_utils import list_s3_log_files, download_multiple_s3_files
from config import LOCAL_DOWNLOAD_DIR

# 로깅 설정
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def is_time_related_file(filename: str, login_time: str, time_window_minutes: int = 30) -> bool:
    """
    파일명의 시간 정보를 기준으로 LOGIN 시간과 관련된 파일인지 판단합니다.

    Args:
        filename: 파일명 (예: "16H_42M_40S.pb.zst")
        login_time: LOGIN 시간 (예: "2025-07-04 16:42:40")
        time_window_minutes: 시간 윈도우 (분 단위, 기본 30분)

    Returns:
        관련 파일 여부
    """
    try:
        # 파일명에서 시간 정보 추출 (예: "16H_42M_40S.pb.zst" -> 16:42:40)
        time_pattern = r'(\d+)H_(\d+)M_(\d+)S'
        match = re.search(time_pattern, filename)

        if not match:
            return False

        file_hour = int(match.group(1))
        file_minute = int(match.group(2))
        file_second = int(match.group(3))

        # LOGIN 시간 파싱
        if isinstance(login_time, str):
            # 다양한 시간 형식 처리
            try:
                if 'T' in login_time:
                    login_dt = datetime.fromisoformat(login_time.replace('Z', '+00:00'))
                else:
                    login_dt = datetime.strptime(login_time, '%Y-%m-%d %H:%M:%S')
            except ValueError:
                # 파싱 실패 시 현재 시간 사용
                logger.warning(f"LOGIN 시간 파싱 실패: {login_time}")
                return False
        elif hasattr(login_time, 'hour'):  # datetime 객체
            login_dt = login_time
        else:
            logger.warning(f"알 수 없는 LOGIN 시간 형식: {login_time} (타입: {type(login_time)})")
            return False

        # 파일 시간을 datetime으로 변환 (같은 날짜 가정)
        file_dt = login_dt.replace(hour=file_hour, minute=file_minute, second=file_second, microsecond=0)

        # 시간 차이 계산
        time_diff = abs((file_dt - login_dt).total_seconds())

        # 시간 윈도우 내에 있는지 확인
        return time_diff <= (time_window_minutes * 60)

    except Exception as e:
        logger.warning(f"시간 관련 파일 판단 실패: {filename} - {e}")
        return False

def search_user_logs(user_id: str, s3_files: List[str] = None) -> Dict:
    """
    특정 사용자 ID의 모든 로그를 검색합니다.
    
    Args:
        user_id: 검색할 사용자 ID
        s3_files: 검색할 S3 파일 목록 (None이면 전체 파일 검색)
        
    Returns:
        사용자 로그 분석 결과
    """
    logger.info(f"사용자 '{user_id}' 로그 검색 시작")
    
    # S3 파일 목록 가져오기
    if s3_files is None:
        s3_files = list_s3_log_files()
        if not s3_files:
            return {'error': 'S3에서 로그 파일을 찾을 수 없습니다.'}

    # 1단계: 해당 사용자 ID가 포함된 파일명만 필터링 (모든 파일)
    user_files = [f for f in s3_files if user_id in f]
    logger.info(f"사용자 {user_id}의 모든 파일: {len(user_files)}개")

    if not user_files:
        return {'error': f'사용자 {user_id}의 로그 파일을 찾을 수 없습니다.'}

    # 2단계: 모든 사용자 파일 다운로드
    logger.info(f"사용자 파일 다운로드 시작: {len(user_files)}개")
    downloaded_files = download_multiple_s3_files(user_files, LOCAL_DOWNLOAD_DIR)
    if not downloaded_files:
        return {'error': '파일 다운로드에 실패했습니다.'}

    logger.info(f"다운로드 완료: {len(downloaded_files)}개 파일")

    # 3단계: 모든 파일 처리하여 로그 수집
    user_logs = []
    found_files = 0
    processed_files = 0

    for file_path in downloaded_files:
        try:
            df = process_log_file(file_path)
            if df.empty:
                continue

            processed_files += 1

            # 파일명에 사용자 ID가 포함되어 있으므로 모든 로그가 해당 사용자의 것
            df['source_file'] = os.path.basename(file_path)
            user_logs.append(df)
            found_files += 1

            logger.info(f"사용자 파일 처리: {file_path} ({len(df)}개 레코드)")

            # 진행 상황 로깅
            if processed_files % 10 == 0:
                logger.info(f"파일 처리 진행: {processed_files}/{len(downloaded_files)} 완료")

        except Exception as e:
            logger.warning(f"파일 처리 실패: {file_path} - {e}")
            continue
    
    if not user_logs:
        return {
            'error': f"사용자 '{user_id}'의 로그를 찾을 수 없습니다.",
            'processed_files': processed_files,
            'found_files': 0
        }
    
    # 모든 사용자 로그 통합
    combined_logs = pd.concat(user_logs, ignore_index=True)

    # 시간 컬럼 확인 및 datetime 생성
    time_columns = [col for col in combined_logs.columns if 'time' in col.lower()]
    if time_columns:
        time_col = time_columns[0]
        combined_logs['datetime'] = pd.to_datetime(combined_logs[time_col], errors='coerce')
        # 시간 순으로 정렬
        combined_logs = combined_logs.sort_values('datetime').reset_index(drop=True)
        logger.info(f"시간 기준 정렬 완료: {time_col} 컬럼 사용")

    # UILoading 이벤트 필터링 (MAF_ANALYTICS_LOG_EVENT_TYPE_LOG_UI_OPEN 중 payload_ui_name이 UILoading인 항목 제거)
    if not combined_logs.empty:
        before_ui_filter = len(combined_logs)
        ui_loading_mask = (
            (combined_logs['event_type'] == 'MAF_ANALYTICS_LOG_EVENT_TYPE_LOG_UI_OPEN') &
            (combined_logs.get('payload_ui_name', '') == 'UILoading')
        )
        combined_logs = combined_logs[~ui_loading_mask]
        after_ui_filter = len(combined_logs)
        if before_ui_filter != after_ui_filter:
            logger.info(f"UILoading 이벤트 필터링: {before_ui_filter}개 -> {after_ui_filter}개 ({before_ui_filter - after_ui_filter}개 UILoading 제거)")

    # 중복 제거 (같은 시간, 같은 이벤트 타입의 중복 레코드 제거)
    if not combined_logs.empty and 'datetime' in combined_logs.columns:
        before_dedup = len(combined_logs)
        combined_logs = combined_logs.drop_duplicates(subset=['datetime', 'event_type'], keep='first')
        after_dedup = len(combined_logs)
        if before_dedup != after_dedup:
            logger.info(f"중복 제거: {before_dedup}개 -> {after_dedup}개 ({before_dedup - after_dedup}개 중복 제거)")

    logger.info(f"사용자 로그 수집 완료: {len(combined_logs)}개 레코드, {found_files}개 파일에서 발견")

    # 통합된 로그의 이벤트 타입 확인
    if not combined_logs.empty:
        event_types = combined_logs['event_type'].value_counts()
        logger.info(f"통합된 로그의 이벤트 타입: {event_types.to_dict()}")
    
    # 날짜별 분석
    daily_analysis = analyze_user_daily_logs(combined_logs)
    
    return {
        'user_id': user_id,
        'total_records': len(combined_logs),
        'found_files': found_files,
        'processed_files': processed_files,
        'raw_logs': combined_logs,
        'daily_analysis': daily_analysis,
        'summary': {
            'total_days': len(daily_analysis),
            'total_logins': sum([day_data['login_sessions'] for day_data in daily_analysis.values()]),
            'date_range': {
                'start': combined_logs['time'].min() if 'time' in combined_logs.columns else None,
                'end': combined_logs['time'].max() if 'time' in combined_logs.columns else None
            }
        }
    }

def filter_user_logs(df: pd.DataFrame, user_id: str) -> pd.DataFrame:
    """
    DataFrame에서 특정 사용자의 로그만 필터링합니다.

    Args:
        df: 로그 DataFrame
        user_id: 필터링할 사용자 ID

    Returns:
        필터링된 사용자 로그 DataFrame
    """
    if df.empty:
        return pd.DataFrame()

    # payload_user_id 컬럼에서 해당 사용자 찾기
    user_id_columns = [col for col in df.columns if 'user_id' in col.lower()]

    user_mask = pd.Series([False] * len(df))

    for col in user_id_columns:
        if col in df.columns:
            # 정확한 문자열 매칭
            user_mask |= (df[col].astype(str) == str(user_id))

    # source 컬럼에서도 확인
    if 'source' in df.columns:
        user_mask |= (df['source'].astype(str) == str(user_id))

    filtered_df = df[user_mask].copy()

    # 필터링 결과 로깅
    if not filtered_df.empty:
        logger.info(f"사용자 {user_id} 로그 필터링: {len(df)}개 -> {len(filtered_df)}개")

    return filtered_df

def analyze_user_daily_logs(df: pd.DataFrame) -> Dict:
    """
    사용자 로그를 날짜별로 분석합니다.
    
    Args:
        df: 사용자 로그 DataFrame
        
    Returns:
        날짜별 분석 결과
    """
    if df.empty:
        return {}
    
    # 시간 컬럼 처리
    time_columns = [col for col in df.columns if 'time' in col.lower()]
    if not time_columns:
        return {}
    
    # 첫 번째 시간 컬럼 사용
    time_col = time_columns[0]
    
    try:
        # 시간을 datetime으로 변환
        df['datetime'] = pd.to_datetime(df[time_col], errors='coerce')
        df['date'] = df['datetime'].dt.date
        
        # 날짜별 그룹화
        daily_analysis = {}
        
        for date, day_logs in df.groupby('date'):
            if pd.isna(date):
                continue
                
            # 로그인 세션 분석
            login_sessions = analyze_login_sessions(day_logs)
            
            daily_analysis[str(date)] = {
                'date': date,
                'total_logs': len(day_logs),
                'login_sessions': len(login_sessions),
                'sessions': login_sessions,
                'event_types': day_logs['event_type'].value_counts().to_dict(),
                'time_range': {
                    'start': day_logs['datetime'].min(),
                    'end': day_logs['datetime'].max()
                }
            }
        
        return daily_analysis
        
    except Exception as e:
        logger.error(f"날짜별 분석 실패: {e}")
        return {}

def analyze_login_sessions(day_logs: pd.DataFrame) -> List[Dict]:
    """
    하루 로그에서 로그인 세션을 분석합니다.
    
    Args:
        day_logs: 특정 날짜의 로그 DataFrame
        
    Returns:
        로그인 세션 목록
    """
    if day_logs.empty:
        return []
    
    # 로그인 이벤트 찾기
    login_events = day_logs[day_logs['event_type'] == 'MAF_ANALYTICS_LOG_EVENT_TYPE_LOG_LOGIN'].copy()

    # 디버깅: 해당 날짜의 모든 이벤트 타입 확인
    all_event_types = day_logs['event_type'].value_counts()
    logger.info(f"해당 날짜의 모든 이벤트 타입: {all_event_types.to_dict()}")
    logger.info(f"LOGIN 이벤트 수: {len(login_events)}")

    if login_events.empty:
        # 로그인 이벤트가 없으면 전체를 하나의 세션으로 간주
        return [{
            'session_id': 1,
            'login_time': day_logs['datetime'].min(),
            'session_logs': day_logs.to_dict('records'),
            'total_actions': len(day_logs),
            'duration_minutes': (day_logs['datetime'].max() - day_logs['datetime'].min()).total_seconds() / 60
        }]
    
    # 로그인 시간 기준으로 정렬
    login_events = login_events.sort_values('datetime')
    
    sessions = []
    
    for i, (_, login_event) in enumerate(login_events.iterrows()):
        session_id = i + 1
        login_time = login_event['datetime']
        
        # 정확한 세션 구분: LOGIN 시간부터 다음 LOGIN 시간 전까지
        if i + 1 < len(login_events):
            next_login_time = login_events.iloc[i + 1]['datetime']
            session_logs = day_logs[
                (day_logs['datetime'] >= login_time) &
                (day_logs['datetime'] < next_login_time)
            ]
        else:
            # 마지막 세션은 그 날의 끝까지
            session_logs = day_logs[day_logs['datetime'] >= login_time]

        logger.info(f"세션 {session_id} 시간 범위: {login_time} ~ {next_login_time if i + 1 < len(login_events) else '하루 끝'}, 포함된 로그: {len(session_logs)}개")
        
        if not session_logs.empty:
            duration = (session_logs['datetime'].max() - session_logs['datetime'].min()).total_seconds() / 60
            event_summary = session_logs['event_type'].value_counts().to_dict()

            logger.info(f"세션 {session_id} 구성: {len(session_logs)}개 액션, 이벤트 타입: {event_summary}")

            sessions.append({
                'session_id': session_id,
                'login_time': login_time,
                'session_logs': session_logs.to_dict('records'),
                'total_actions': len(session_logs),
                'duration_minutes': duration,
                'event_summary': event_summary
            })
    
    return sessions
