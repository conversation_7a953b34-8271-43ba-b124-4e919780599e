[명령어 모음]


[Proto File To Python Script]
& "C:\SVN\AceBattleLogTool\ACE_Ranking_Log_Proto_Version\protoc-25.6-win64\bin\protoc.exe" --proto_path=. --python_out=. BattleStruct_PROTO.proto
& "C:\SVN\AceBattleLogTool\ACE_Ranking_Log_Proto_Version\protoc-25.6-win64\bin\protoc.exe" --proto_path=. --python_out=. BattleEnumsFull.proto


[Streamlit Run]
streamlit run streamlit_app.py


[새로운 기능 - Mind Map 뷰]
- streamlit-markmap을 사용한 인터랙티브 마인드맵 뷰 추가
- 모든 로그 항목을 계층적으로 시각화
- 라운드 → 행동 → 이벤트 구조로 표현
- 게임 정보, 책갈피, 조커, 몬스터 상태 등 모든 데이터 포함
- 상세 정보 표시/숨김 옵션
- 성능 최적화를 위한 이벤트 수 제한 기능

[설치된 패키지]
- streamlit-markmap==1.0.1
- streamlit_mermaid
- streamlit-tree-independent-components

[주요 파일 변경사항]
- streamlit_app.py: Mind Map 뷰 모드 추가
- log_parser.py: parse_log_extended() 함수 추가 (모든 항목 파싱)
- 예외 처리 및 성능 최적화 적용