# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: BattleStruct_PROTO.proto
# Protobuf Python Version: 4.25.6
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


import BattleEnumsFull_pb2 as BattleEnumsFull__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x18\x42\x61ttleStruct_PROTO.proto\x12\x0fgoogle.protobuf\x1a\x15\x42\x61ttleEnumsFull.proto\"\xbe\x02\n\x0f\x42\x61ttleLog_PROTO\x12=\n\tEventType\x18\x01 \x01(\x0e\x32*.google.protobuf.EBT_BATTLE_LOG_EVENT_TYPE\x12\x0e\n\x06Source\x18\x02 \x01(\t\x12>\n\x07Payload\x18\x03 \x03(\x0b\x32-.google.protobuf.BattleLog_PROTO.PayloadEntry\x12@\n\x0cPayloadOrder\x18\x04 \x03(\x0b\x32*.google.protobuf.BattleLogPayloadDTO_PROTO\x1aZ\n\x0cPayloadEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\x39\n\x05value\x18\x02 \x01(\x0b\x32*.google.protobuf.BattleLogPayloadDTO_PROTO:\x02\x38\x01\"T\n\x14\x42\x61ttleLogQueue_PROTO\x12\x0c\n\x04oder\x18\x01 \x01(\x05\x12.\n\x04logs\x18\x02 \x03(\x0b\x32 .google.protobuf.BattleLog_PROTO\"`\n\x14\x42\x61ttleLogRound_PROTO\x12\x13\n\x0bround_index\x18\x01 \x01(\x05\x12\x33\n\x04logs\x18\x02 \x03(\x0b\x32%.google.protobuf.BattleLogQueue_PROTO\"R\n\x19TotalBattleLogQueue_PROTO\x12\x35\n\x06rounds\x18\x01 \x03(\x0b\x32%.google.protobuf.BattleLogRound_PROTO\"\xf5\x01\n\x19\x42\x61ttleLogPayloadDTO_PROTO\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\x41\n\x0bpayloadType\x18\x02 \x01(\x0e\x32,.google.protobuf.EBT_BATTLE_LOG_PAYLOAD_TYPE\x12@\n\x0bscalarValue\x18\x03 \x01(\x0b\x32+.google.protobuf.BattleLogScalarValue_PROTO\x12\x46\n\x0ereferenceValue\x18\x04 \x01(\x0b\x32..google.protobuf.BattleLogReferenceValue_PROTO\"\xd6\x03\n\x1a\x42\x61ttleLogScalarValue_PROTO\x12\x10\n\x08intValue\x18\x01 \x01(\x05\x12\x12\n\nulongValue\x18\x02 \x01(\x04\x12\x11\n\tboolValue\x18\x03 \x01(\x08\x12\x13\n\x0bstringValue\x18\x04 \x01(\t\x12\x34\n\x0coverKillType\x18\x05 \x01(\x0e\x32\x1e.google.protobuf.ETB_OVER_KILL\x12\x36\n\x0b\x61\x62ilityType\x18\x06 \x01(\x0e\x32!.google.protobuf.ETB_ABILITY_TYPE\x12=\n\x0epokerHandsType\x18\x07 \x01(\x0e\x32%.google.protobuf.ETB_Poker_Hands_Type\x12\x32\n\tsoundType\x18\x08 \x01(\x0e\x32\x1f.google.protobuf.EBT_SOUND_TYPE\x12K\n\x11missionRewardType\x18\t \x01(\x0e\x32\x30.google.protobuf.ETB_IN_GAME_MISSION_REWARD_TYPE\x12\x13\n\x0b\x63\x61rdIndices\x18\n \x03(\x05\x12\x12\n\nfloatValue\x18\x0b \x01(\t\x12\x13\n\x0b\x64oubleValue\x18\x0c \x01(\t\"\xc3\x06\n\x1d\x42\x61ttleLogReferenceValue_PROTO\x12\x30\n\x04\x63\x61rd\x18\x01 \x01(\x0b\x32\".google.protobuf.PlayingCard_PROTO\x12\x34\n\x08\x63\x61rdList\x18\x02 \x03(\x0b\x32\".google.protobuf.PlayingCard_PROTO\x12=\n\x0bproductList\x18\x03 \x03(\x0b\x32(.google.protobuf.InGameShopProduct_PROTO\x12\x0f\n\x07intList\x18\x04 \x03(\x05\x12\x31\n\x05joker\x18\x05 \x01(\x0b\x32\".google.protobuf.InGameJoker_PROTO\x12Y\n\x10stringDictionary\x18\x06 \x01(\x0b\x32?.google.protobuf.BattleLogReferenceValue_PROTO_StringDictionary\x12]\n\x14stringDictionaryList\x18\x07 \x03(\x0b\x32?.google.protobuf.BattleLogReferenceValue_PROTO_StringDictionary\x12\x39\n\x07product\x18\x08 \x01(\x0b\x32(.google.protobuf.InGameShopProduct_PROTO\x12=\n\x0e\x65valuateResult\x18\t \x01(\x0b\x32%.google.protobuf.EvaluateResult_PROTO\x12\x46\n\x0e\x65valuateConfig\x18\n \x01(\x0b\x32..google.protobuf.EvaluateAttentionConfig_PROTO\x12;\n\x08gameInfo\x18\x0b \x01(\x0b\x32).google.protobuf.InGameInfoSnapshot_PROTO\x12?\n\x0f\x65valuateContext\x18\x0c \x01(\x0b\x32&.google.protobuf.EvaluateContext_PROTO\x12=\n\x0b\x63onsumeItem\x18\r \x01(\x0b\x32(.google.protobuf.InGameConsumeItem_PROTO\"\xda\x01\n.BattleLogReferenceValue_PROTO_StringDictionary\x12o\n\x10stringDictionary\x18\x01 \x03(\x0b\x32U.google.protobuf.BattleLogReferenceValue_PROTO_StringDictionary.StringDictionaryEntry\x1a\x37\n\x15StringDictionaryEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\"\xe6\x08\n\x18InGameInfoSnapshot_PROTO\x12\x36\n\x0b\x62\x61ttleState\x18\x01 \x01(\x0e\x32!.google.protobuf.ETB_BATTLE_STATE\x12=\n\x12missionBeforeState\x18\x02 \x01(\x0e\x32!.google.protobuf.ETB_BATTLE_STATE\x12\x14\n\x0chandCountMax\x18\x03 \x01(\x05\x12\x15\n\rjokerCountMax\x18\x04 \x01(\x05\x12\x14\n\x0citemCountMax\x18\x05 \x01(\x05\x12\x16\n\x0e\x61ttackCountMax\x18\x06 \x01(\x05\x12\x15\n\rthrowCountMax\x18\x07 \x01(\x05\x12\x16\n\x0e\x61ttackTryCount\x18\x08 \x01(\x05\x12\x15\n\rthrowTryCount\x18\t \x01(\x05\x12\x15\n\rfreeItemCount\x18\n \x01(\x05\x12\x11\n\tdollarUse\x18\x0b \x01(\x05\x12\x0c\n\x04\x63oin\x18\x0c \x01(\t\x12\x18\n\x10monsterCurrentHP\x18\r \x01(\t\x12\x14\n\x0cmonsterMaxHP\x18\x0e \x01(\t\x12\x44\n\x12\x63urrentMissionInfo\x18\x0f \x01(\x0b\x32(.google.protobuf.InGameMissionInfo_PROTO\x12\x18\n\x10\x63urrentMonsterID\x18\x10 \x01(\x05\x12:\n\x0eoriginFullDeck\x18\x11 \x03(\x0b\x32\".google.protobuf.PlayingCard_PROTO\x12;\n\x0fplayingFullDeck\x18\x12 \x03(\x0b\x32\".google.protobuf.PlayingCard_PROTO\x12\x37\n\x0bplayingDeck\x18\x13 \x03(\x0b\x32\".google.protobuf.PlayingCard_PROTO\x12\x41\n\x15\x64\x65stroyedPlayingCards\x18\x14 \x03(\x0b\x32\".google.protobuf.PlayingCard_PROTO\x12@\n\x12monsterAbilityList\x18\x15 \x03(\x0b\x32$.google.protobuf.InGameAbility_PROTO\x12\x45\n\x13playingConsumeItems\x18\x16 \x03(\x0b\x32(.google.protobuf.InGameConsumeItem_PROTO\x12\x39\n\rplayingJokers\x18\x17 \x03(\x0b\x32\".google.protobuf.InGameJoker_PROTO\x12?\n\x10playingBookMarks\x18\x18 \x03(\x0b\x32%.google.protobuf.InGameBookMark_PROTO\x12?\n\x11pokerHandDataList\x18\x19 \x03(\x0b\x32$.google.protobuf.PokerHandData_PROTO\x12\x0e\n\x06userID\x18\x1a \x01(\t\x12\x11\n\tuserLevel\x18\x1b \x01(\x05\x12\x0c\n\x04seed\x18\x1c \x01(\x04\"\xbe\x03\n\x18InGameInfoSaveData_PROTO\x12:\n\x0eoriginFullDeck\x18\x01 \x03(\x0b\x32\".google.protobuf.PlayingCard_PROTO\x12\x41\n\x15\x64\x65stroyedPlayingCards\x18\x02 \x03(\x0b\x32\".google.protobuf.PlayingCard_PROTO\x12\x45\n\x13playingConsumeItems\x18\x03 \x03(\x0b\x32(.google.protobuf.InGameConsumeItem_PROTO\x12\x39\n\rplayingJokers\x18\x04 \x03(\x0b\x32\".google.protobuf.InGameJoker_PROTO\x12?\n\x10playingBookMarks\x18\x05 \x03(\x0b\x32%.google.protobuf.InGameBookMark_PROTO\x12?\n\x11pokerHandDataList\x18\x06 \x03(\x0b\x32$.google.protobuf.PokerHandData_PROTO\x12\x0c\n\x04\x63oin\x18\x07 \x01(\t\x12\x11\n\tshopLevel\x18\x08 \x01(\t\"J\n\x15PlayingCardList_PROTO\x12\x31\n\x05\x63\x61rds\x18\x01 \x03(\x0b\x32\".google.protobuf.PlayingCard_PROTO\"\x7f\n\x11PlayingCard_PROTO\x12\x15\n\rcardDataIndex\x18\x01 \x01(\x05\x12\x35\n\x05label\x18\x02 \x01(\x0b\x32&.google.protobuf.InGameCardLabel_PROTO\x12\x0e\n\x06isBack\x18\x03 \x01(\x08\x12\x0c\n\x04\x63hip\x18\x04 \x01(\x04\"\xf3\x06\n\x14\x45valuateResult_PROTO\x12\r\n\x05\x63hips\x18\x01 \x01(\x04\x12\x0e\n\x06xChips\x18\x02 \x01(\x04\x12\x0c\n\x04mult\x18\x03 \x01(\x02\x12\r\n\x05xMult\x18\x04 \x01(\x02\x12\x10\n\x08pDollars\x18\x05 \x01(\x05\x12\x10\n\x08isRepeat\x18\x06 \x01(\x08\x12\x11\n\tisDestroy\x18\x07 \x01(\x08\x12;\n\rcalculateArea\x18\x08 \x01(\x0e\x32$.google.protobuf.ETB_CALCULATOR_AREA\x12>\n\rcalculateTurn\x18\t \x01(\x0e\x32\'.google.protobuf.ETB_IN_GAME_STATE_TURN\x12\x17\n\x0fisEffectChanged\x18\n \x01(\x08\x12\x37\n\x0b\x61\x62ilitiable\x18\x0b \x01(\x0b\x32\".google.protobuf.Abilitiable_PROTO\x12\x30\n\x04\x63\x61rd\x18\x0c \x01(\x0b\x32\".google.protobuf.PlayingCard_PROTO\x12H\n\x13\x63\x61lculateType_chips\x18\r \x01(\x0e\x32+.google.protobuf.ETB_ABILITY_CALCULATE_TYPE\x12I\n\x14\x63\x61lculateType_xChips\x18\x0e \x01(\x0e\x32+.google.protobuf.ETB_ABILITY_CALCULATE_TYPE\x12G\n\x12\x63\x61lculateType_mult\x18\x0f \x01(\x0e\x32+.google.protobuf.ETB_ABILITY_CALCULATE_TYPE\x12H\n\x13\x63\x61lculateType_xMult\x18\x10 \x01(\x0e\x32+.google.protobuf.ETB_ABILITY_CALCULATE_TYPE\x12J\n\x15\x63\x61lculateType_dollars\x18\x11 \x01(\x0e\x32+.google.protobuf.ETB_ABILITY_CALCULATE_TYPE\x12;\n\x0c\x65xtraResults\x18\x12 \x03(\x0b\x32%.google.protobuf.EvaluateResult_PROTO\x12\x36\n\x0b\x61\x62ilityType\x18\x13 \x01(\x0e\x32!.google.protobuf.ETB_ABILITY_TYPE\"7\n\x15\x45valuateContext_PROTO\x12\x0c\n\x04\x63hip\x18\x01 \x01(\x04\x12\x10\n\x08multiple\x18\x02 \x01(\t\"\xb4\x04\n\x1d\x45valuateAttentionConfig_PROTO\x12\x30\n\x04\x63\x61rd\x18\x01 \x01(\x0b\x32\".google.protobuf.PlayingCard_PROTO\x12\x37\n\x0b\x61\x62ilitiable\x18\x02 \x01(\x0b\x32\".google.protobuf.Abilitiable_PROTO\x12.\n\x05sound\x18\x03 \x01(\x0e\x32\x1f.google.protobuf.EBT_SOUND_TYPE\x12\x38\n\x04type\x18\x04 \x01(\x0e\x32*.google.protobuf.EBT_ATTENTION_UPDATE_TYPE\x12\r\n\x05value\x18\x05 \x01(\t\x12\x11\n\tisDestroy\x18\x06 \x01(\x08\x12H\n\x13\x63\x61lculateType_chips\x18\x07 \x01(\x0e\x32+.google.protobuf.ETB_ABILITY_CALCULATE_TYPE\x12G\n\x12\x63\x61lculateType_mult\x18\x08 \x01(\x0e\x32+.google.protobuf.ETB_ABILITY_CALCULATE_TYPE\x12J\n\x15\x63\x61lculateType_dollars\x18\t \x01(\x0e\x32+.google.protobuf.ETB_ABILITY_CALCULATE_TYPE\x12=\n\x0e\x65valuateResult\x18\n \x01(\x0b\x32%.google.protobuf.EvaluateResult_PROTO\"\x9c\x01\n\x17InGameMissionInfo_PROTO\x12\x11\n\tMissionID\x18\x01 \x01(\x05\x12?\n\nrewardData\x18\x02 \x01(\x0b\x32+.google.protobuf.InGameMisstionReward_PROTO\x12\r\n\x05\x63ount\x18\x03 \x01(\x05\x12\x0f\n\x07isClear\x18\x04 \x01(\x08\x12\r\n\x05round\x18\x05 \x01(\x05\"\x8b\x01\n\x1aInGameMisstionReward_PROTO\x12>\n\x04type\x18\x01 \x01(\x0e\x32\x30.google.protobuf.ETB_IN_GAME_MISSION_REWARD_TYPE\x12\x10\n\x08item_idx\x18\x02 \x01(\x05\x12\r\n\x05\x63ount\x18\x03 \x01(\x05\x12\x0c\n\x04rate\x18\x04 \x01(\x05\"\xa1\x01\n\x11\x41\x62ilitiable_PROTO\x12\x37\n\tabilities\x18\x01 \x03(\x0b\x32$.google.protobuf.InGameAbility_PROTO\x12\x1c\n\x14isAbilityCopyCompact\x18\x02 \x01(\x08\x12\x35\n\x05label\x18\x03 \x01(\x0b\x32&.google.protobuf.InGameCardLabel_PROTO\"\x88\x01\n\x11InGameJoker_PROTO\x12\x37\n\tabilities\x18\x01 \x03(\x0b\x32$.google.protobuf.InGameAbility_PROTO\x12\x16\n\x0ejokerDataIndex\x18\x02 \x01(\x05\x12\x0e\n\x06isBack\x18\x03 \x01(\x08\x12\x12\n\nuserDataLv\x18\x04 \x01(\x05\"h\n\x15InGameCardLabel_PROTO\x12\x37\n\tabilities\x18\x01 \x03(\x0b\x32$.google.protobuf.InGameAbility_PROTO\x12\x16\n\x0elabelDataIndex\x18\x02 \x01(\x05\"p\n\x17InGameConsumeItem_PROTO\x12\x37\n\tabilities\x18\x01 \x03(\x0b\x32$.google.protobuf.InGameAbility_PROTO\x12\x1c\n\x14\x63onsumeItemDataIndex\x18\x02 \x01(\x05\"n\n\x16InGameScrollItem_PROTO\x12\x37\n\tabilities\x18\x01 \x03(\x0b\x32$.google.protobuf.InGameAbility_PROTO\x12\x1b\n\x13scrollItemDataIndex\x18\x02 \x01(\x05\"~\n#InGameUserStatusRandomAbility_PROTO\x12\x37\n\tabilities\x18\x01 \x03(\x0b\x32$.google.protobuf.InGameAbility_PROTO\x12\x1e\n\x16statusAbilityDataIndex\x18\x02 \x01(\x05\"i\n\"InGameUserStatusLevelAbility_PROTO\x12\x37\n\tabilities\x18\x01 \x03(\x0b\x32$.google.protobuf.InGameAbility_PROTO\x12\n\n\x02lv\x18\x02 \x01(\x05\"\x93\x01\n\x17InGameShopProduct_PROTO\x12\x30\n\x08itemType\x18\x01 \x01(\x0e\x32\x1e.google.protobuf.ETB_CARD_TYPE\x12\x33\n\x07product\x18\x02 \x01(\x0b\x32\".google.protobuf.Abilitiable_PROTO\x12\x11\n\titemIndex\x18\x03 \x01(\x05\"\xe1\x01\n\x14InGameBookMark_PROTO\x12\x19\n\x11\x62ookmarkDataIndex\x18\x01 \x01(\x05\x12\x12\n\nuserDataLv\x18\x02 \x01(\x05\x12\x12\n\n_enhanceLv\x18\x03 \x01(\x05\x12?\n\x0b_listSkills\x18\x04 \x03(\x0b\x32*.google.protobuf.InGameBookMarkSkill_PROTO\x12\x45\n\x11_listSkillsPlayed\x18\x05 \x03(\x0b\x32*.google.protobuf.InGameBookMarkSkill_PROTO\"~\n\x19InGameBookMarkSkill_PROTO\x12\x37\n\tabilities\x18\x01 \x03(\x0b\x32$.google.protobuf.InGameAbility_PROTO\x12\x19\n\x11\x62ookmarkDataIndex\x18\x02 \x01(\x05\x12\r\n\x05level\x18\x03 \x01(\x05\"\xf4\x02\n\x13InGameAbility_PROTO\x12\x18\n\x10\x61\x62ilityDataIndex\x18\x01 \x01(\x05\x12\x42\n\rcalculateType\x18\x02 \x01(\x0e\x32+.google.protobuf.ETB_ABILITY_CALCULATE_TYPE\x12;\n\rcalculateArea\x18\x03 \x01(\x0e\x32$.google.protobuf.ETB_CALCULATOR_AREA\x12\x39\n\x08turnType\x18\x04 \x01(\x0e\x32\'.google.protobuf.ETB_IN_GAME_STATE_TURN\x12\r\n\x05round\x18\x05 \x01(\x05\x12\x39\n\x06values\x18\x06 \x03(\x0b\x32).google.protobuf.InGameAbilityValue_PROTO\x12=\n\nconditions\x18\x07 \x03(\x0b\x32).google.protobuf.InGameAbilityValue_PROTO\"D\n\x18InGameAbilityValue_PROTO\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\x0c\n\x04type\x18\x02 \x01(\t\x12\r\n\x05value\x18\x03 \x01(\t\"\x8e\x02\n\x13PokerHandData_PROTO\x12\x11\n\tdataIndex\x18\x01 \x01(\x05\x12\x33\n\x04type\x18\x02 \x01(\x0e\x32%.google.protobuf.ETB_Poker_Hands_Type\x12\n\n\x02lv\x18\x03 \x01(\x05\x12\x0c\n\x04\x63hip\x18\x04 \x01(\x04\x12\x10\n\x08multiple\x18\x05 \x01(\x02\x12\x11\n\tplayCount\x18\x06 \x01(\x05\x12\x12\n\nthrowCount\x18\x07 \x01(\x05\x12\x13\n\x0bisRoundPlay\x18\x08 \x01(\x08\x12\x17\n\x0fplayCount_Round\x18\t \x01(\x05\x12\x18\n\x10throwCount_Round\x18\n \x01(\x05\x12\x14\n\x0cisRoundThrow\x18\x0b \x01(\x08\"G\n\x15\x42\x61ttleLogQueueWrapper\x12.\n\x04logs\x18\x01 \x03(\x0b\x32 .google.protobuf.BattleLog_PROTOb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'BattleStruct_PROTO_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  DESCRIPTOR._options = None
  _globals['_BATTLELOG_PROTO_PAYLOADENTRY']._options = None
  _globals['_BATTLELOG_PROTO_PAYLOADENTRY']._serialized_options = b'8\001'
  _globals['_BATTLELOGREFERENCEVALUE_PROTO_STRINGDICTIONARY_STRINGDICTIONARYENTRY']._options = None
  _globals['_BATTLELOGREFERENCEVALUE_PROTO_STRINGDICTIONARY_STRINGDICTIONARYENTRY']._serialized_options = b'8\001'
  _globals['_BATTLELOG_PROTO']._serialized_start=69
  _globals['_BATTLELOG_PROTO']._serialized_end=387
  _globals['_BATTLELOG_PROTO_PAYLOADENTRY']._serialized_start=297
  _globals['_BATTLELOG_PROTO_PAYLOADENTRY']._serialized_end=387
  _globals['_BATTLELOGQUEUE_PROTO']._serialized_start=389
  _globals['_BATTLELOGQUEUE_PROTO']._serialized_end=473
  _globals['_BATTLELOGROUND_PROTO']._serialized_start=475
  _globals['_BATTLELOGROUND_PROTO']._serialized_end=571
  _globals['_TOTALBATTLELOGQUEUE_PROTO']._serialized_start=573
  _globals['_TOTALBATTLELOGQUEUE_PROTO']._serialized_end=655
  _globals['_BATTLELOGPAYLOADDTO_PROTO']._serialized_start=658
  _globals['_BATTLELOGPAYLOADDTO_PROTO']._serialized_end=903
  _globals['_BATTLELOGSCALARVALUE_PROTO']._serialized_start=906
  _globals['_BATTLELOGSCALARVALUE_PROTO']._serialized_end=1376
  _globals['_BATTLELOGREFERENCEVALUE_PROTO']._serialized_start=1379
  _globals['_BATTLELOGREFERENCEVALUE_PROTO']._serialized_end=2214
  _globals['_BATTLELOGREFERENCEVALUE_PROTO_STRINGDICTIONARY']._serialized_start=2217
  _globals['_BATTLELOGREFERENCEVALUE_PROTO_STRINGDICTIONARY']._serialized_end=2435
  _globals['_BATTLELOGREFERENCEVALUE_PROTO_STRINGDICTIONARY_STRINGDICTIONARYENTRY']._serialized_start=2380
  _globals['_BATTLELOGREFERENCEVALUE_PROTO_STRINGDICTIONARY_STRINGDICTIONARYENTRY']._serialized_end=2435
  _globals['_INGAMEINFOSNAPSHOT_PROTO']._serialized_start=2438
  _globals['_INGAMEINFOSNAPSHOT_PROTO']._serialized_end=3564
  _globals['_INGAMEINFOSAVEDATA_PROTO']._serialized_start=3567
  _globals['_INGAMEINFOSAVEDATA_PROTO']._serialized_end=4013
  _globals['_PLAYINGCARDLIST_PROTO']._serialized_start=4015
  _globals['_PLAYINGCARDLIST_PROTO']._serialized_end=4089
  _globals['_PLAYINGCARD_PROTO']._serialized_start=4091
  _globals['_PLAYINGCARD_PROTO']._serialized_end=4218
  _globals['_EVALUATERESULT_PROTO']._serialized_start=4221
  _globals['_EVALUATERESULT_PROTO']._serialized_end=5104
  _globals['_EVALUATECONTEXT_PROTO']._serialized_start=5106
  _globals['_EVALUATECONTEXT_PROTO']._serialized_end=5161
  _globals['_EVALUATEATTENTIONCONFIG_PROTO']._serialized_start=5164
  _globals['_EVALUATEATTENTIONCONFIG_PROTO']._serialized_end=5728
  _globals['_INGAMEMISSIONINFO_PROTO']._serialized_start=5731
  _globals['_INGAMEMISSIONINFO_PROTO']._serialized_end=5887
  _globals['_INGAMEMISSTIONREWARD_PROTO']._serialized_start=5890
  _globals['_INGAMEMISSTIONREWARD_PROTO']._serialized_end=6029
  _globals['_ABILITIABLE_PROTO']._serialized_start=6032
  _globals['_ABILITIABLE_PROTO']._serialized_end=6193
  _globals['_INGAMEJOKER_PROTO']._serialized_start=6196
  _globals['_INGAMEJOKER_PROTO']._serialized_end=6332
  _globals['_INGAMECARDLABEL_PROTO']._serialized_start=6334
  _globals['_INGAMECARDLABEL_PROTO']._serialized_end=6438
  _globals['_INGAMECONSUMEITEM_PROTO']._serialized_start=6440
  _globals['_INGAMECONSUMEITEM_PROTO']._serialized_end=6552
  _globals['_INGAMESCROLLITEM_PROTO']._serialized_start=6554
  _globals['_INGAMESCROLLITEM_PROTO']._serialized_end=6664
  _globals['_INGAMEUSERSTATUSRANDOMABILITY_PROTO']._serialized_start=6666
  _globals['_INGAMEUSERSTATUSRANDOMABILITY_PROTO']._serialized_end=6792
  _globals['_INGAMEUSERSTATUSLEVELABILITY_PROTO']._serialized_start=6794
  _globals['_INGAMEUSERSTATUSLEVELABILITY_PROTO']._serialized_end=6899
  _globals['_INGAMESHOPPRODUCT_PROTO']._serialized_start=6902
  _globals['_INGAMESHOPPRODUCT_PROTO']._serialized_end=7049
  _globals['_INGAMEBOOKMARK_PROTO']._serialized_start=7052
  _globals['_INGAMEBOOKMARK_PROTO']._serialized_end=7277
  _globals['_INGAMEBOOKMARKSKILL_PROTO']._serialized_start=7279
  _globals['_INGAMEBOOKMARKSKILL_PROTO']._serialized_end=7405
  _globals['_INGAMEABILITY_PROTO']._serialized_start=7408
  _globals['_INGAMEABILITY_PROTO']._serialized_end=7780
  _globals['_INGAMEABILITYVALUE_PROTO']._serialized_start=7782
  _globals['_INGAMEABILITYVALUE_PROTO']._serialized_end=7850
  _globals['_POKERHANDDATA_PROTO']._serialized_start=7853
  _globals['_POKERHANDDATA_PROTO']._serialized_end=8123
  _globals['_BATTLELOGQUEUEWRAPPER']._serialized_start=8125
  _globals['_BATTLELOGQUEUEWRAPPER']._serialized_end=8196
# @@protoc_insertion_point(module_scope)
