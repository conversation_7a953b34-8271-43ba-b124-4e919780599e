import os
import boto3
import zstandard as zstd
from functools import lru_cache
from typing import List, Optional, Dict, Any
import logging
from config import (
    AWS_ACCESS_KEY_ID,
    AWS_SECRET_ACCESS_KEY,
    AWS_BUCKET_NAME,
    AWS_REGION_NAME,
    S3_PREFIX,
    LOCAL_DOWNLOAD_DIR,
    BUFFER_SIZE,
    ERROR_MESSAGES,
    SUCCESS_MESSAGES
)

# 로깅 설정
logger = logging.getLogger(__name__)

@lru_cache(maxsize=1)
def s3_client():
    """S3 클라이언트를 생성하고 캐싱합니다."""
    try:
        return boto3.client(
            "s3",
            aws_access_key_id=AWS_ACCESS_KEY_ID,
            aws_secret_access_key=AWS_SECRET_ACCESS_KEY,
            region_name=AWS_REGION_NAME
        )
    except Exception as e:
        logger.error(f"S3 클라이언트 생성 실패: {e}")
        raise


def _fetch_log_files_from_s3(prefix: str) -> List[str]:
    """S3에서 직접 로그 파일 목록을 가져옵니다 (캐시 없음)."""
    try:
        s3 = s3_client()
        paginator = s3.get_paginator('list_objects_v2')

        files = []
        for page in paginator.paginate(Bucket=AWS_BUCKET_NAME, Prefix=prefix):
            if 'Contents' in page:
                files.extend([obj["Key"] for obj in page["Contents"] if obj["Key"].endswith(".zst")])

        logger.info(f"S3에서 {len(files)}개 파일 목록을 가져왔습니다 (prefix: {prefix})")
        return files

    except Exception as e:
        logger.error(f"S3 파일 목록 가져오기 실패: {e}")
        return []

@lru_cache(maxsize=10)
def list_log_files(prefix: str = None) -> List[str]:
    """S3에서 로그 파일 목록을 가져오고 캐싱합니다."""
    # prefix가 None이면 기본값 사용
    if prefix is None:
        prefix = S3_PREFIX

    return _fetch_log_files_from_s3(prefix)

def list_log_files_no_cache(prefix: str = None) -> List[str]:
    """캐시를 사용하지 않고 S3에서 직접 로그 파일 목록을 가져옵니다."""
    # prefix가 None이면 기본값 사용
    if prefix is None:
        prefix = S3_PREFIX

    logger.info(f"캐시 없이 S3에서 파일 목록을 새로 가져옵니다 (prefix: {prefix})")
    return _fetch_log_files_from_s3(prefix)

def download_and_decompress(key: str) -> Optional[str]:
    """S3에서 파일을 다운로드하고 압축을 해제합니다."""
    try:
        # S3 키의 전체 경로 구조를 유지하여 로컬 경로 생성
        # 예: Log/BattleLog/2025_6_30/Rank/file.pb.zst -> downloads/Log/BattleLog/2025_6_30/Rank/file.pb.zst
        local_file_path = os.path.join(LOCAL_DOWNLOAD_DIR, key)
        local_dir = os.path.dirname(local_file_path)

        # 필요한 디렉토리 구조 생성
        os.makedirs(local_dir, exist_ok=True)

        zst_path = local_file_path
        pb_path = os.path.splitext(zst_path)[0] + ".pb"

        # 이미 압축 해제된 파일이 있으면 재사용
        if os.path.exists(pb_path):
            logger.debug(f"캐시된 파일 사용: {pb_path}")
            return pb_path

        s3 = s3_client()

        # 다운로드가 필요한 경우에만 수행
        if not os.path.exists(zst_path):
            logger.info(f"S3에서 파일 다운로드 중: {key}")
            s3.download_file(Bucket=AWS_BUCKET_NAME, Key=key, Filename=zst_path)

        # 최적화된 압축 해제 (설정된 버퍼 크기 사용)
        logger.info(f"파일 압축 해제 중: {zst_path}")
        with open(zst_path, "rb") as compressed, open(pb_path, "wb") as decompressed:
            dctx = zstd.ZstdDecompressor()
            dctx.copy_stream(compressed, decompressed, read_size=BUFFER_SIZE, write_size=BUFFER_SIZE)

        # 압축 파일 정리 (디스크 공간 절약)
        try:
            os.remove(zst_path)
            logger.debug(f"압축 파일 삭제: {zst_path}")
        except Exception as e:
            logger.warning(f"압축 파일 삭제 실패: {e}")

        logger.info(f"파일 처리 완료: {pb_path}")
        return pb_path

    except Exception as e:
        logger.error(f"파일 다운로드/압축해제 실패 ({key}): {e}")
        return None


def clear_cache():
    """캐시를 클리어합니다."""
    s3_client.cache_clear()
    list_log_files.cache_clear()
    logger.info("S3 유틸리티 캐시가 클리어되었습니다")


def get_file_info(key: str) -> Optional[Dict[str, Any]]:
    """S3 파일의 메타데이터를 가져옵니다."""
    try:
        s3 = s3_client()
        response = s3.head_object(Bucket=AWS_BUCKET_NAME, Key=key)

        return {
            "size": response.get("ContentLength", 0),
            "last_modified": response.get("LastModified"),
            "etag": response.get("ETag", "").strip('"'),
            "content_type": response.get("ContentType", "")
        }
    except Exception as e:
        logger.error(f"파일 정보 가져오기 실패 ({key}): {e}")
        return None