#!/usr/bin/env python3
"""
재화 통계 분석 모듈
MAF_ANALYTICS_LOG_EVENT_TYPE_USER_CURRENCY_DATA 이벤트를 기반으로 재화 통계를 분석합니다.
"""

import os
import json
import pandas as pd
import logging
from typing import Dict, List, Optional, Tuple
from log_parser import process_log_file
from s3_utils import download_multiple_s3_files
from config import LOCAL_DOWNLOAD_DIR

# 로깅 설정
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def extract_currency_data_from_parquet(parquet_file_path: str) -> pd.DataFrame:
    """
    base_user_goods.parquet 파일에서 재화 데이터를 추출합니다.

    Args:
        parquet_file_path: parquet 파일 경로

    Returns:
        재화 데이터가 포함된 DataFrame
    """
    try:
        # parquet 파일 읽기
        df = pd.read_parquet(parquet_file_path)
        logger.info(f"Parquet 파일 로드 완료: {len(df)}행")

        if df.empty or 'all_fields_map' not in df.columns:
            logger.warning("유효한 all_fields_map 컬럼을 찾을 수 없습니다.")
            return pd.DataFrame()

        result_records = []
        excluded_count = 0

        for _, row in df.iterrows():
            try:
                all_fields_map = row['all_fields_map']

                # all_fields_map이 리스트 형태인지 확인
                if not isinstance(all_fields_map, list):
                    excluded_count += 1
                    continue

                # 필드들을 딕셔너리로 변환
                fields_dict = {}
                for field_tuple in all_fields_map:
                    if isinstance(field_tuple, tuple) and len(field_tuple) == 2:
                        field_name, field_value = field_tuple
                        fields_dict[field_name] = field_value

                # gamer_id 추출
                gamer_id_data = fields_dict.get('gamer_id')
                if not gamer_id_data:
                    excluded_count += 1
                    continue

                # gamer_id JSON 파싱
                if isinstance(gamer_id_data, str):
                    try:
                        gamer_id_json = json.loads(gamer_id_data)
                        user_id = gamer_id_json.get('s')
                    except json.JSONDecodeError:
                        excluded_count += 1
                        continue
                else:
                    excluded_count += 1
                    continue

                if not user_id:
                    excluded_count += 1
                    continue

                # updatedat 시간 추출
                updatedat_data = fields_dict.get('updatedat')
                time_value = None
                if updatedat_data and isinstance(updatedat_data, str):
                    try:
                        updatedat_json = json.loads(updatedat_data)
                        time_value = updatedat_json.get('s')
                    except json.JSONDecodeError:
                        pass

                # item_info에서 재화 데이터 추출
                item_info_data = fields_dict.get('item_info')
                if not item_info_data:
                    excluded_count += 1
                    continue

                # item_info JSON 파싱
                if isinstance(item_info_data, str):
                    try:
                        item_info_json = json.loads(item_info_data)
                        items_list = item_info_json.get('l', [])
                    except json.JSONDecodeError:
                        excluded_count += 1
                        continue
                else:
                    excluded_count += 1
                    continue

                # 각 아이템 처리
                for item in items_list:
                    if not isinstance(item, dict):
                        continue

                    item_data = item.get('m', {})
                    if not isinstance(item_data, dict):
                        continue

                    # 아이템 ID 추출
                    item_id_data = item_data.get('id', {})
                    if isinstance(item_id_data, dict) and 'n' in item_id_data:
                        try:
                            item_id = int(item_id_data['n'])
                        except (ValueError, TypeError):
                            continue
                    else:
                        continue

                    # 관심 있는 아이템만 처리
                    if item_id not in [101, 102, 10004, 10005]:
                        continue

                    # 수량 정보 추출
                    c_data = item_data.get('c', {})
                    h_a_data = item_data.get('h_a', {})
                    h_u_data = item_data.get('h_u', {})

                    try:
                        c = int(c_data.get('n', 0)) if isinstance(c_data, dict) else 0
                        h_a = int(h_a_data.get('n', 0)) if isinstance(h_a_data, dict) else 0
                        h_u = int(h_u_data.get('n', 0)) if isinstance(h_u_data, dict) else 0
                    except (ValueError, TypeError):
                        continue

                    result_records.append({
                        'user_id': user_id,
                        'time': time_value,
                        'item_id': item_id,
                        'current_amount': c,
                        'acquired_amount': h_a,
                        'used_amount': h_u
                    })

            except Exception as e:
                logger.error(f"Parquet 데이터 처리 중 오류: {e}")
                excluded_count += 1
                continue

        if excluded_count > 0:
            logger.info(f"제외된 레코드: {excluded_count}개")

        if not result_records:
            logger.warning("유효한 재화 데이터를 찾을 수 없습니다.")
            return pd.DataFrame()

        result_df = pd.DataFrame(result_records)
        logger.info(f"Parquet에서 추출된 재화 데이터: {len(result_df)}개 레코드")

        return result_df

    except Exception as e:
        logger.error(f"Parquet 파일 처리 중 오류: {e}")
        return pd.DataFrame()

def extract_currency_data_from_logs(df: pd.DataFrame) -> pd.DataFrame:
    """
    로그 DataFrame에서 MAF_ANALYTICS_LOG_EVENT_TYPE_USER_CURRENCY_DATA 이벤트만 추출합니다.
    LOGIN 이벤트에서 user_id를 가져와서 USER_CURRENCY_DATA 이벤트에 매핑합니다.

    Args:
        df: 로그 DataFrame

    Returns:
        재화 데이터가 포함된 DataFrame
    """
    if df.empty:
        logger.warning("입력 DataFrame이 비어있습니다.")
        return pd.DataFrame()

    # 사용 가능한 이벤트 타입 확인
    available_events = df['event_type'].unique() if 'event_type' in df.columns else []
    logger.debug(f"사용 가능한 이벤트 타입: {list(available_events)}")

    # LOGIN 이벤트에서 user_id 추출
    login_events = df[df['event_type'] == 'MAF_ANALYTICS_LOG_EVENT_TYPE_LOG_LOGIN'].copy()
    file_user_id = None

    if not login_events.empty:
        login_user_id = login_events.iloc[0].get('payload_user_id')
        if pd.notna(login_user_id) and str(login_user_id).strip():
            file_user_id = str(login_user_id).strip()
            logger.debug(f"파일에서 user_id 추출: {file_user_id}")

    if not file_user_id:
        logger.warning("LOGIN 이벤트에서 유효한 user_id를 찾을 수 없습니다.")
        return pd.DataFrame()

    # USER_CURRENCY_DATA 이벤트만 필터링
    currency_events = df[df['event_type'] == 'MAF_ANALYTICS_LOG_EVENT_TYPE_USER_CURRENCY_DATA'].copy()

    if currency_events.empty:
        logger.warning("MAF_ANALYTICS_LOG_EVENT_TYPE_USER_CURRENCY_DATA 이벤트를 찾을 수 없습니다.")
        logger.info(f"전체 이벤트 수: {len(df)}, 사용 가능한 이벤트 타입: {list(available_events)}")
        return pd.DataFrame()

    logger.info(f"재화 데이터 이벤트 {len(currency_events)}개 발견, user_id: {file_user_id}")
    
    # 필요한 필드 추출
    result_records = []
    excluded_count = 0
    
    for _, row in currency_events.iterrows():
        try:
            # LOGIN에서 추출한 user_id 사용
            user_id = file_user_id
            time_value = row.get('payload_time')
            goods_data = row.get('payload_goods_data')

            if not time_value or not goods_data:
                excluded_count += 1
                continue
            
            # goods_data가 문자열인 경우 JSON 파싱
            if isinstance(goods_data, str):
                try:
                    goods_data = json.loads(goods_data)
                except json.JSONDecodeError:
                    excluded_count += 1
                    continue

            # goods_data가 딕셔너리인지 확인
            if not isinstance(goods_data, dict):
                excluded_count += 1
                continue

            # items 배열에서 아이템 정보 추출
            items_list = goods_data.get('items', [])
            if not isinstance(items_list, list):
                excluded_count += 1
                continue

            # 각 아이템 처리
            for item in items_list:
                if not isinstance(item, dict):
                    continue

                item_id = item.get('id')
                if item_id not in [101, 102, 10004, 10005]:  # 101: 유료 다이아, 102: 무료 다이아, 10004: 일반 소환권, 10005: 픽업 소환권
                    continue

                c = item.get('c', 0)  # 보유 개수
                h_a = item.get('h_a', 0)  # 획득 개수
                h_u = item.get('h_u', 0)  # 사용 개수

                result_records.append({
                    'user_id': user_id,
                    'time': time_value,
                    'item_id': item_id,
                    'current_amount': c,
                    'acquired_amount': h_a,
                    'used_amount': h_u
                })
                
        except Exception as e:
            logger.error(f"재화 데이터 처리 중 오류: {e}")
            excluded_count += 1
            continue
    
    if excluded_count > 0:
        logger.info(f"제외된 레코드: {excluded_count}개")
    
    if not result_records:
        logger.warning("유효한 재화 데이터를 찾을 수 없습니다.")
        return pd.DataFrame()
    
    result_df = pd.DataFrame(result_records)
    logger.info(f"추출된 재화 데이터: {len(result_df)}개 레코드")
    
    return result_df

def get_latest_currency_data_per_user(currency_df: pd.DataFrame) -> pd.DataFrame:
    """
    사용자별로 마지막 날짜의 마지막 재화 정보만 추출합니다.

    Args:
        currency_df: 전체 재화 데이터 DataFrame

    Returns:
        사용자별 최신 재화 데이터 DataFrame
    """
    if currency_df.empty:
        return pd.DataFrame()

    # user_id가 유효한 데이터만 필터링 (NaN, None, 빈 문자열 제외)
    valid_user_data = currency_df.dropna(subset=['user_id']).copy()
    valid_user_data = valid_user_data[valid_user_data['user_id'] != ''].copy()

    if valid_user_data.empty:
        logger.warning("유효한 user_id를 가진 재화 데이터가 없습니다.")
        return pd.DataFrame()

    logger.info(f"유효한 사용자 데이터: {len(valid_user_data)}개 (전체: {len(currency_df)}개)")

    # 시간을 datetime으로 변환 (다양한 형식 지원)
    try:
        valid_user_data['time'] = pd.to_datetime(valid_user_data['time'], format='mixed', errors='coerce')
    except Exception as e:
        logger.warning(f"시간 변환 중 오류 발생: {e}")
        # 대안으로 기본 변환 시도
        valid_user_data['time'] = pd.to_datetime(valid_user_data['time'], errors='coerce')

    # 시간 변환에 실패한 데이터 제거
    before_time_filter = len(valid_user_data)
    valid_user_data = valid_user_data.dropna(subset=['time']).copy()
    after_time_filter = len(valid_user_data)

    if before_time_filter != after_time_filter:
        logger.info(f"시간 형식 오류로 제외된 레코드: {before_time_filter - after_time_filter}개")

    # 시간 컬럼이 실제로 datetime 타입인지 확인
    if not pd.api.types.is_datetime64_any_dtype(valid_user_data['time']):
        logger.warning("시간 컬럼이 datetime 타입이 아닙니다. 재변환을 시도합니다.")

        # 재변환 시도 (최대 1회만)
        original_count = len(valid_user_data)
        valid_user_data['time'] = pd.to_datetime(valid_user_data['time'], errors='coerce')
        valid_user_data = valid_user_data.dropna(subset=['time']).copy()

        # 재변환 후에도 datetime 타입이 아니면 강제 종료
        if not pd.api.types.is_datetime64_any_dtype(valid_user_data['time']):
            logger.error("시간 컬럼 변환에 실패했습니다. 분석을 중단합니다.")
            return pd.DataFrame()

        logger.info(f"시간 재변환 완료: {original_count} -> {len(valid_user_data)}개 레코드")

    if valid_user_data.empty:
        logger.warning("시간 변환 후 유효한 데이터가 없습니다.")
        return pd.DataFrame()

    # 성능 최적화: 벡터화된 연산으로 최신 데이터 추출
    logger.info("사용자별 최신 재화 데이터 추출 시작...")
    valid_user_data['date'] = valid_user_data['time'].dt.date

    # 각 사용자의 최신 날짜 찾기
    user_latest_dates = valid_user_data.groupby('user_id')['date'].max().reset_index()
    user_latest_dates.columns = ['user_id', 'latest_date']

    # 최신 날짜 데이터만 필터링
    latest_date_data = valid_user_data.merge(user_latest_dates, on='user_id')
    latest_date_data = latest_date_data[latest_date_data['date'] == latest_date_data['latest_date']]

    # 각 사용자-아이템별 최신 시간 데이터 추출
    latest_records = []

    # 사용자-아이템별로 그룹화하여 최신 시간 데이터 선택
    grouped = latest_date_data.groupby(['user_id', 'item_id'])

    for (_, item_id), group in grouped:
        if item_id in [101, 102, 10004, 10005]:
            # 시간순으로 정렬하여 마지막 데이터 선택
            latest_item_data = group.sort_values('time').iloc[-1]
            latest_records.append(latest_item_data.to_dict())

    logger.info(f"벡터화된 연산으로 최신 데이터 추출 완료: {len(latest_records)}개 레코드")

    if not latest_records:
        return pd.DataFrame()

    latest_df = pd.DataFrame(latest_records)
    logger.info(f"사용자별 최신 재화 데이터 추출 완료: {len(latest_df)}개 레코드")

    return latest_df

def analyze_currency_statistics(currency_df: pd.DataFrame) -> Dict:
    """
    재화 데이터를 분석하여 통계를 생성합니다.
    사용자별 마지막 날짜의 마지막 재화 정보만 사용합니다.

    Args:
        currency_df: 재화 데이터 DataFrame

    Returns:
        분석 결과 딕셔너리
    """
    if currency_df.empty:
        return {
            'summary': {},
            'user_summary': pd.DataFrame(),
            'purchase_users': pd.DataFrame(),
            'negative_records': pd.DataFrame(),
            'large_value_records': pd.DataFrame(),
            'error': '분석할 재화 데이터가 없습니다.'
        }

    # 사용자별 최신 재화 데이터만 추출
    latest_currency_df = get_latest_currency_data_per_user(currency_df)

    if latest_currency_df.empty:
        return {
            'summary': {},
            'user_summary': pd.DataFrame(),
            'purchase_users': pd.DataFrame(),
            'negative_records': pd.DataFrame(),
            'large_value_records': pd.DataFrame(),
            'error': '사용자별 최신 재화 데이터를 찾을 수 없습니다.'
        }

    logger.info(f"최신 데이터 기준 분석: 전체 {len(currency_df)}개 → 최신 {len(latest_currency_df)}개 레코드")

    # 전체 총합 계산 (최신 데이터 기준, 음수 값 제외)
    summary_totals = {}
    for item_id in [101, 102, 10004, 10005]:
        item_data = latest_currency_df[latest_currency_df['item_id'] == item_id]
        if not item_data.empty:
            # 음수 값을 0으로 처리하여 총합에서 제외
            current_amounts = item_data['current_amount'].clip(lower=0)
            acquired_amounts = item_data['acquired_amount'].clip(lower=0)
            used_amounts = item_data['used_amount'].clip(lower=0)

            summary_totals[item_id] = {
                'total_current': current_amounts.sum(),
                'total_acquired': acquired_amounts.sum(),
                'total_used': used_amounts.sum()
            }
        else:
            summary_totals[item_id] = {
                'total_current': 0,
                'total_acquired': 0,
                'total_used': 0
            }

    # 음수 값 및 대용량 값 감지 (최신 데이터 기준)
    negative_records = latest_currency_df[latest_currency_df['current_amount'] < 0].copy()
    large_value_records = latest_currency_df[latest_currency_df['current_amount'] >= 1_000_000].copy()

    # 사용자별 집계 (최신 데이터 기준)
    user_summary_list = []
    for user_id in latest_currency_df['user_id'].unique():
        user_data = latest_currency_df[latest_currency_df['user_id'] == user_id]

        # 각 아이템별 데이터 가져오기
        user_summary = {'user_id': user_id}

        for item_id in [101, 102, 10004, 10005]:
            item_data = user_data[user_data['item_id'] == item_id]
            if not item_data.empty:
                # 이미 최신 데이터이므로 첫 번째 레코드 사용
                latest_data = item_data.iloc[0]
                # 음수 값을 0으로 처리 (개별 사용자 데이터에서는 원본 값 유지하되, 총합 계산시에만 제외)
                user_summary[f'item_{item_id}_current'] = latest_data['current_amount']
                user_summary[f'item_{item_id}_acquired'] = latest_data['acquired_amount']
                user_summary[f'item_{item_id}_used'] = latest_data['used_amount']
                user_summary[f'item_{item_id}_last_time'] = latest_data['time']
            else:
                user_summary[f'item_{item_id}_current'] = 0
                user_summary[f'item_{item_id}_acquired'] = 0
                user_summary[f'item_{item_id}_used'] = 0
                user_summary[f'item_{item_id}_last_time'] = None

        user_summary_list.append(user_summary)

    user_summary_df = pd.DataFrame(user_summary_list)

    # 결제 유저 필터링 (유료 다이아가 있는 유저)
    purchase_users_df = user_summary_df[
        (user_summary_df['item_101_current'] > 0) |
        (user_summary_df['item_101_acquired'] > 0) |
        (user_summary_df['item_101_used'] > 0)
    ].copy()

    return {
        'summary': summary_totals,
        'user_summary': user_summary_df,
        'purchase_users': purchase_users_df,
        'negative_records': negative_records,
        'large_value_records': large_value_records,
        'total_users': len(user_summary_df),
        'purchase_users_count': len(purchase_users_df),
        'negative_count': len(negative_records),
        'large_value_count': len(large_value_records),
        'total_records_before': len(currency_df),
        'total_records_after': len(latest_currency_df)
    }

def analyze_currency_from_s3_and_parquet(s3_files: List[str], download_dir: str, parquet_file_path: Optional[str] = None, max_files: Optional[int] = None) -> Dict:
    """
    S3 파일들과 base_user_goods.parquet 파일에서 재화 통계를 통합 분석합니다.

    Args:
        s3_files: S3 파일 키 리스트
        download_dir: 다운로드 디렉토리
        parquet_file_path: base_user_goods.parquet 파일 경로 (선택사항)
        max_files: 최대 처리 파일 수 (테스트용)

    Returns:
        통합 분석 결과 딕셔너리
    """
    logger.info(f"통합 재화 통계 분석 시작: S3 {len(s3_files)}개 파일" + (f", Parquet: {parquet_file_path}" if parquet_file_path else ""))

    all_currency_data = []

    # 1. Parquet 파일 처리 (있는 경우)
    parquet_records = 0
    if parquet_file_path and os.path.exists(parquet_file_path):
        try:
            parquet_currency_data = extract_currency_data_from_parquet(parquet_file_path)
            if not parquet_currency_data.empty:
                all_currency_data.append(parquet_currency_data)
                parquet_records = len(parquet_currency_data)
                logger.info(f"Parquet 데이터 추가: {parquet_records}개 레코드")
        except Exception as e:
            logger.error(f"Parquet 파일 처리 중 오류: {e}")

    # 2. S3 파일 처리
    s3_records = 0
    processed_files = 0
    failed_files = 0

    if s3_files:
        # 파일 수 제한 (max_files가 None이면 모든 파일 처리)
        if max_files and max_files < len(s3_files):
            s3_files = s3_files[:max_files]
            logger.info(f"S3 파일 수를 {max_files}개로 제한")

        # S3 파일 다운로드
        try:
            downloaded_files = download_multiple_s3_files(s3_files, download_dir)
            if downloaded_files:
                logger.info(f"S3 다운로드 완료: {len(downloaded_files)}개 파일")

                # 로그 파일 처리 및 재화 데이터 추출
                for file_path in downloaded_files:
                    try:
                        # 로그 파일 파싱
                        df = process_log_file(file_path)
                        if df.empty:
                            failed_files += 1
                            continue

                        # 재화 데이터 추출
                        currency_data = extract_currency_data_from_logs(df)
                        if not currency_data.empty:
                            all_currency_data.append(currency_data)
                            s3_records += len(currency_data)

                        processed_files += 1

                    except Exception as e:
                        logger.error(f"S3 파일 처리 실패 ({file_path}): {e}")
                        failed_files += 1
                        continue
            else:
                logger.warning("다운로드된 S3 파일이 없습니다.")

        except Exception as e:
            logger.error(f"S3 파일 다운로드 중 오류: {e}")

    logger.info(f"S3 파일 처리 완료: 성공 {processed_files}개, 실패 {failed_files}개, 레코드 {s3_records}개")

    # 3. 데이터 통합
    if not all_currency_data:
        return {
            'error': '유효한 재화 데이터를 찾을 수 없습니다.',
            'processed_files': processed_files,
            'failed_files': failed_files,
            'parquet_records': parquet_records,
            's3_records': s3_records
        }

    combined_currency_df = pd.concat(all_currency_data, ignore_index=True)
    total_records = len(combined_currency_df)

    logger.info(f"통합된 재화 데이터: 총 {total_records}개 레코드 (Parquet: {parquet_records}, S3: {s3_records})")

    # 4. 통계 분석
    analysis_result = analyze_currency_statistics(combined_currency_df)
    analysis_result.update({
        'processed_files': processed_files,
        'failed_files': failed_files,
        'total_records': total_records,
        'parquet_records': parquet_records,
        's3_records': s3_records,
        'data_sources': {
            'parquet_used': parquet_records > 0,
            's3_used': s3_records > 0,
            'parquet_file': parquet_file_path if parquet_records > 0 else None
        }
    })

    return analysis_result

def analyze_currency_from_s3_files(s3_files: List[str], download_dir: str, max_files: Optional[int] = None) -> Dict:
    """
    S3 파일들에서 재화 통계를 분석합니다.
    
    Args:
        s3_files: S3 파일 키 리스트
        download_dir: 다운로드 디렉토리
        max_files: 최대 처리 파일 수 (테스트용)
        
    Returns:
        분석 결과 딕셔너리
    """
    logger.info(f"재화 통계 분석 시작: {len(s3_files)}개 파일")

    # 파일 수 제한 (max_files가 None이면 모든 파일 처리)
    if max_files and max_files < len(s3_files):
        s3_files = s3_files[:max_files]
        logger.info(f"파일 수를 {max_files}개로 제한")
    else:
        logger.info("모든 파일을 분석합니다")
    
    # S3 파일 다운로드
    try:
        downloaded_files = download_multiple_s3_files(s3_files, download_dir)
        if not downloaded_files:
            return {'error': "다운로드된 파일이 없습니다."}

        logger.info(f"다운로드 완료: {len(downloaded_files)}개 파일")

    except Exception as e:
        logger.error(f"파일 다운로드 중 오류: {e}")
        return {'error': f"파일 다운로드 중 오류: {e}"}
    
    # 로그 파일 처리 및 재화 데이터 추출
    all_currency_data = []
    processed_files = 0
    failed_files = 0
    
    for file_path in downloaded_files:
        try:
            # 로그 파일 파싱
            df = process_log_file(file_path)
            if df.empty:
                failed_files += 1
                continue
            
            # 재화 데이터 추출
            currency_data = extract_currency_data_from_logs(df)
            if not currency_data.empty:
                all_currency_data.append(currency_data)
            
            processed_files += 1
            
        except Exception as e:
            logger.error(f"파일 처리 실패 ({file_path}): {e}")
            failed_files += 1
            continue
    
    logger.info(f"파일 처리 완료: 성공 {processed_files}개, 실패 {failed_files}개")
    
    # 데이터 통합
    if not all_currency_data:
        return {
            'error': '유효한 재화 데이터를 찾을 수 없습니다.',
            'processed_files': processed_files,
            'failed_files': failed_files
        }
    
    combined_currency_df = pd.concat(all_currency_data, ignore_index=True)
    logger.info(f"통합된 재화 데이터: {len(combined_currency_df)}개 레코드")
    
    # 통계 분석
    analysis_result = analyze_currency_statistics(combined_currency_df)
    analysis_result.update({
        'processed_files': processed_files,
        'failed_files': failed_files,
        'total_records': len(combined_currency_df)
    })
    
    return analysis_result

def analyze_user_currency_history(s3_files: List[str], download_dir: str, target_user_id: str) -> Dict:
    """
    특정 사용자의 날짜별 재화 변화를 분석합니다.

    Args:
        s3_files: S3 파일 키 리스트
        download_dir: 다운로드 디렉토리
        target_user_id: 분석할 사용자 ID

    Returns:
        사용자별 재화 분석 결과 딕셔너리
    """
    logger.info(f"사용자별 재화 분석 시작: {target_user_id}")

    # S3 파일 다운로드
    try:
        downloaded_files = download_multiple_s3_files(s3_files, download_dir)
        if not downloaded_files:
            return {'error': "다운로드된 파일이 없습니다."}

        logger.info(f"다운로드 완료: {len(downloaded_files)}개 파일")

    except Exception as e:
        logger.error(f"파일 다운로드 중 오류: {e}")
        return {'error': f"파일 다운로드 중 오류: {e}"}

    # 사용자별 재화 데이터 수집
    user_currency_data = []
    processed_files = 0
    failed_files = 0

    for file_path in downloaded_files:
        try:
            # 로그 파일 파싱
            df = process_log_file(file_path)
            if df.empty:
                failed_files += 1
                continue

            # 재화 데이터 추출
            currency_data = extract_currency_data_from_logs(df)
            if currency_data.empty:
                failed_files += 1
                continue

            # 해당 사용자의 데이터만 필터링
            user_data = currency_data[currency_data['user_id'] == target_user_id]
            if not user_data.empty:
                user_currency_data.append(user_data)

            processed_files += 1

        except Exception as e:
            logger.error(f"파일 처리 실패 ({file_path}): {e}")
            failed_files += 1
            continue

    logger.info(f"파일 처리 완료: 성공 {processed_files}개, 실패 {failed_files}개")

    # 데이터 통합
    if not user_currency_data:
        return {
            'error': f'사용자 {target_user_id}의 재화 데이터를 찾을 수 없습니다.',
            'processed_files': processed_files,
            'failed_files': failed_files
        }

    combined_user_df = pd.concat(user_currency_data, ignore_index=True)
    logger.info(f"사용자 {target_user_id}의 재화 데이터: {len(combined_user_df)}개 레코드")

    # 날짜별 마지막 재화 로그 분석
    daily_analysis = analyze_daily_currency_changes(combined_user_df, target_user_id)

    return {
        'user_id': target_user_id,
        'processed_files': processed_files,
        'failed_files': failed_files,
        'total_records': len(combined_user_df),
        'daily_analysis': daily_analysis
    }

def analyze_daily_currency_changes(user_df: pd.DataFrame, user_id: str) -> Dict:
    """
    사용자의 날짜별 재화 변화를 분석합니다.

    Args:
        user_df: 사용자의 재화 데이터 DataFrame
        user_id: 사용자 ID

    Returns:
        날짜별 분석 결과
    """
    if user_df.empty:
        return {'error': '분석할 데이터가 없습니다.'}

    # 시간을 datetime으로 변환하고 날짜 추출 (다양한 형식 지원)
    try:
        user_df['time'] = pd.to_datetime(user_df['time'], format='mixed', errors='coerce')
    except Exception as e:
        logger.warning(f"시간 변환 중 오류 발생: {e}")
        # 대안으로 기본 변환 시도
        user_df['time'] = pd.to_datetime(user_df['time'], errors='coerce')

    # 시간 변환에 실패한 데이터 제거
    before_time_filter = len(user_df)
    user_df = user_df.dropna(subset=['time']).copy()
    after_time_filter = len(user_df)

    if before_time_filter != after_time_filter:
        logger.info(f"시간 형식 오류로 제외된 레코드: {before_time_filter - after_time_filter}개")

    # 시간 컬럼이 실제로 datetime 타입인지 확인
    if not pd.api.types.is_datetime64_any_dtype(user_df['time']):
        logger.warning("시간 컬럼이 datetime 타입이 아닙니다. 재변환을 시도합니다.")

        # 재변환 시도 (최대 1회만)
        original_count = len(user_df)
        user_df['time'] = pd.to_datetime(user_df['time'], errors='coerce')
        user_df = user_df.dropna(subset=['time']).copy()

        # 재변환 후에도 datetime 타입이 아니면 강제 종료
        if not pd.api.types.is_datetime64_any_dtype(user_df['time']):
            logger.error("시간 컬럼 변환에 실패했습니다. 분석을 중단합니다.")
            return {'error': '시간 데이터 변환 실패'}

        logger.info(f"시간 재변환 완료: {original_count} -> {len(user_df)}개 레코드")

    if user_df.empty:
        return {'error': '유효한 시간 데이터가 없습니다.'}

    user_df['date'] = user_df['time'].dt.date

    # 날짜별 마지막 재화 로그 추출
    daily_last_logs = []

    for date in sorted(user_df['date'].unique()):
        date_data = user_df[user_df['date'] == date]

        # 각 아이템별 마지막 시간의 데이터
        date_summary = {'date': date, 'logs': []}

        for item_id in [101, 102, 10004, 10005]:
            item_data = date_data[date_data['item_id'] == item_id]
            if not item_data.empty:
                # 시간순으로 정렬하여 마지막 데이터 선택
                last_log = item_data.sort_values('time').iloc[-1]

                item_name = ''
                if item_id == 101:
                    item_name = '유로 다이아'
                elif item_id == 102:
                    item_name = '무료 다이아'
                elif item_id == 10004:
                    item_name = '일반 소환권'
                elif item_id == 10005:
                    item_name = '픽업 소환권'

                date_summary['logs'].append({
                    'item_id': item_id,
                    'item_name': item_name,
                    'time': last_log['time'],
                    'current_amount': last_log['current_amount'],
                    'acquired_amount': last_log['acquired_amount'],
                    'used_amount': last_log['used_amount']
                })

        if date_summary['logs']:
            daily_last_logs.append(date_summary)

    # 날짜 간 변화량 계산
    daily_changes = []

    for i in range(1, len(daily_last_logs)):
        prev_day = daily_last_logs[i-1]
        curr_day = daily_last_logs[i]

        change_summary = {
            'from_date': prev_day['date'],
            'to_date': curr_day['date'],
            'changes': []
        }

        # 각 아이템별 변화량 계산
        for item_id in [101, 102, 10004, 10005]:
            prev_item = next((log for log in prev_day['logs'] if log['item_id'] == item_id), None)
            curr_item = next((log for log in curr_day['logs'] if log['item_id'] == item_id), None)

            if prev_item and curr_item:
                item_name = ''
                if item_id == 101:
                    item_name = '유로 다이아'
                elif item_id == 102:
                    item_name = '무료 다이아'
                elif item_id == 10004:
                    item_name = '일반 소환권'
                elif item_id == 10005:
                    item_name = '픽업 소환권'

                change_summary['changes'].append({
                    'item_id': item_id,
                    'item_name': item_name,
                    'current_change': curr_item['current_amount'] - prev_item['current_amount'],
                    'acquired_change': curr_item['acquired_amount'] - prev_item['acquired_amount'],
                    'used_change': curr_item['used_amount'] - prev_item['used_amount'],
                    'prev_current': prev_item['current_amount'],
                    'curr_current': curr_item['current_amount'],
                    'prev_acquired': prev_item['acquired_amount'],
                    'curr_acquired': curr_item['acquired_amount'],
                    'prev_used': prev_item['used_amount'],
                    'curr_used': curr_item['used_amount']
                })

        if change_summary['changes']:
            daily_changes.append(change_summary)

    return {
        'user_id': user_id,
        'total_days': len(daily_last_logs),
        'daily_last_logs': daily_last_logs,
        'daily_changes': daily_changes
    }

def get_top_currency_holders(user_summary_df: pd.DataFrame, item_id: int, top_n: int = 5) -> pd.DataFrame:
    """
    특정 재화의 보유량 기준으로 상위 N명의 유저 정보를 반환합니다.

    Args:
        user_summary_df: 사용자별 재화 요약 DataFrame
        item_id: 아이템 ID (10004: 일반 소환권, 10005: 픽업 소환권)
        top_n: 상위 몇 명까지 반환할지 (기본값: 5)

    Returns:
        상위 N명의 유저 정보 DataFrame
    """
    if user_summary_df.empty:
        return pd.DataFrame()

    # 해당 아이템의 보유량 컬럼명
    current_col = f'item_{item_id}_current'
    acquired_col = f'item_{item_id}_acquired'
    used_col = f'item_{item_id}_used'

    # 필요한 컬럼이 있는지 확인
    required_cols = [current_col, acquired_col, used_col]
    if not all(col in user_summary_df.columns for col in required_cols):
        logger.warning(f"아이템 {item_id}에 대한 필요한 컬럼을 찾을 수 없습니다.")
        return pd.DataFrame()

    # 보유량 기준으로 정렬하여 상위 N명 선택
    top_users = user_summary_df.nlargest(top_n, current_col)

    # 결과 DataFrame 생성
    result_data = []
    for idx, (_, row) in enumerate(top_users.iterrows(), 1):
        result_data.append({
            '순위': idx,
            '사용자ID': row['user_id'],
            '보유량': int(row[current_col]),
            '획득량': int(row[acquired_col]),
            '사용량': int(row[used_col])
        })

    return pd.DataFrame(result_data)
