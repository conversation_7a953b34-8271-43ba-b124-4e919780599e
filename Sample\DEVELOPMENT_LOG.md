# 개발 로그 및 학습 데이터 정리

## 📋 프로젝트 개요
- **프로젝트명**: ACE Battle Log Analysis Tool
- **목적**: 모바일 카드 게임 로그 분석 도구
- **주요 기술**: Streamlit, Protocol Buffers, Python
- **로그 형식**: `.pb.zst` (압축된 Protocol Buffer)

## 🏗️ 시스템 아키텍처

### 핵심 파일 구조
```
├── streamlit_app.py          # 메인 UI 애플리케이션
├── log_parser.py            # 로그 파싱 및 데이터 추출
├── s3_utils.py              # S3 파일 다운로드 및 압축 해제
├── config.py                # 설정 및 상수
├── korean_names.py          # 한국어 이름 매핑
├── ui_components.py         # UI 컴포넌트
├── BattleStruct_PROTO.proto # 프로토콜 버퍼 구조 정의
└── BattleEnumsFull.proto    # 열거형 정의
```

### 데이터 흐름
1. **S3 파일 목록 조회** → `s3_utils.py`
2. **파일 다운로드 및 압축 해제** → `s3_utils.py`
3. **프로토 버퍼 파싱** → `log_parser.py`
4. **데이터 분석 및 통계** → `log_parser.py`
5. **UI 표시** → `streamlit_app.py`

## 🔧 주요 기능 구현

### 1. 상점 통계 시스템
**목적**: 아이템 구매/판매 통계 분석

#### 핵심 함수
- `parse_log_shop_statistics()`: 상점 데이터 추출
- `get_shop_statistics_summary()`: 통계 집계
- `show_shop_stats()`: UI 표시

#### 데이터 구조
```python
shop_event = {
    'round_index': int,           # 라운드 번호
    'item_type': str,            # 아이템 타입 (JOKER, CONSUME_ITEM, SCROLL_ITEM)
    'item_index': int,           # 아이템 인덱스
    'transaction_type': str,     # 거래 타입 (구매/판매)
    'log_type': str,            # 로그 타입
    'file_name': str            # 파일명
}
```

#### 이벤트 타입 매핑
```python
# 구매 이벤트
BUY_JOKER = 5
BUY_CONSUME_ITEM = 7  
BUY_SCROLL_ITEM = 9

# 판매 이벤트
SELL_JOKER = 6
SELL_CONSUME_ITEM = 8
```

#### 중요 발견사항
1. **JOKER 인덱스 추출**: `referenceValue.joker.jokerDataIndex` 사용
2. **중복 제거**: `round_index + event_type + key + item_index + item_type` 조합으로 고유키 생성
3. **Index 0 필터링**: 의미없는 데이터 제외
4. **라운드 정보**: Rank 모드에서만 표시, `라운드 (횟수번)` 형식

### 2. 아이템&스크롤 사용 통계
**목적**: 소모 아이템 및 스크롤 아이템 사용 통계

#### 핵심 함수
- `parse_log_fast_item_scroll_usage()`: 아이템/스크롤 데이터 추출
- `show_item_scroll_usage_stats()`: UI 표시

#### 데이터 소스
1. **상점 구매 데이터**: `productList`, `product` 필드
2. **게임 정보**: `gameInfo.playingConsumeItems`

#### 아이템 타입 구분
```python
CONSUME_ITEM = 2  # 소모 아이템
SCROLL_ITEM = 3   # 스크롤 아이템
```

## 📊 프로토콜 버퍼 구조 분석

### BattleLogReferenceValue_PROTO
```protobuf
message BattleLogReferenceValue_PROTO {
  repeated InGameShopProduct_PROTO productList = 3;
  InGameShopProduct_PROTO product = 8;
  InGameInfoSnapshot_PROTO gameInfo = 11;
  InGameConsumeItem_PROTO consumeItem = 13;
}
```

### InGameInfoSnapshot_PROTO
```protobuf
message InGameInfoSnapshot_PROTO {
  repeated InGameConsumeItem_PROTO playingConsumeItems = 22;
  repeated InGameJoker_PROTO playingJokers = 23;
  repeated InGameBookMark_PROTO playingBookMarks = 24;
}
```

### InGameShopProduct_PROTO
```protobuf
message InGameShopProduct_PROTO {
  ETB_CARD_TYPE itemType = 1;
  int32 itemIndex = 2;
}
```

## 🐛 해결된 주요 이슈

### 1. 프로토 필드 접근 문제
**문제**: `HasField()` 사용 시 오류
**해결**: `hasattr()` 사용으로 변경
```python
# 잘못된 방법
if ref_value.HasField("gameInfo"):

# 올바른 방법  
if hasattr(ref_value, 'gameInfo'):
```

### 2. JOKER 인덱스 추출 문제
**문제**: `joker_index` 필드에서 잘못된 값 추출
**해결**: `referenceValue.joker.jokerDataIndex` 사용
```python
if (hasattr(payload_dto, 'referenceValue') and 
    payload_dto.referenceValue and
    hasattr(payload_dto.referenceValue, 'joker') and 
    payload_dto.referenceValue.joker and
    hasattr(payload_dto.referenceValue.joker, 'jokerDataIndex')):
    item_index = payload_dto.referenceValue.joker.jokerDataIndex
```

### 3. 중복 데이터 문제
**문제**: 같은 이벤트가 여러 번 기록됨
**해결**: 고유키 기반 중복 제거
```python
unique_key = f"{round_idx}_{log.EventType}_{key}_{item_index}_{item_type_enum}"
if unique_key not in processed_sales:
    processed_sales.add(unique_key)
```

### 4. 라운드 정보 표시 문제
**문제**: All/Chapter 모드에서도 라운드 정보 표시
**해결**: `log_type == "Rank"` 조건 추가
```python
if log_type == "Rank":
    # 라운드 정보 표시
    round_info[item_index] = f"{most_frequent_round} ({round_count}번)"
```

## 🎯 성능 최적화

### 1. 병렬 처리
```python
max_workers = min(8, os.cpu_count() or 4)
with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
    # 병렬 파일 처리
```

### 2. 캐싱
```python
@st.cache_data(ttl=CACHE_TTL_LONG)
def get_shop_statistics_cached(file_paths_tuple):
    return get_shop_statistics_summary(list(file_paths_tuple))
```

### 3. 메모리 최적화
- 불필요한 디버그 로그 제거
- 시간 측정 변수 제거
- 효율적인 데이터 구조 사용

## 📝 코딩 패턴 및 베스트 프랙티스

### 1. 에러 처리
```python
try:
    # 메인 로직
except Exception as e:
    logger.error(f"오류 발생: {e}")
    return pd.DataFrame()  # 빈 DataFrame 반환
```

### 2. 로깅
```python
logger.info(f"✅ 상점 데이터 발견: {file_name} ({len(shop_df)}개 이벤트)")
```

### 3. 데이터 검증
```python
if item_index > 0:  # Index 0 제외
    if unique_key not in processed_items:  # 중복 제거
        # 데이터 처리
```

## 🔍 디버깅 히스토리

### 제거된 디버그 코드들
- 상세 디버그 메시지 (`🔍`, `🎯`, `🎉`, `❌` 이모지 포함)
- 시간 측정 변수 (`download_time`, `read_time`, `parse_time`)
- 테스트 파일들 (15개 이상)
- 불필요한 예외 변수

### 유지된 핵심 로그
- 정보성 로그 (`logger.info`)
- 에러 로그 (`logger.error`)
- 중요한 상태 정보

## 📈 통계 및 메트릭

### 상점 통계 결과 예시
```
총 상점 이벤트: 762개
판매 이벤트: 6개
- 인덱스 1012 (CONSUME_ITEM): 2회 판매, 주요 라운드 4 (1번)
- 인덱스 1044 (JOKER): 1회 판매, 주요 라운드 8 (1번)

구매 이벤트: 180개  
- 인덱스 1010 (CONSUME_ITEM): 10회 구매, 주요 라운드 19 (6번)
```

### 아이템&스크롤 통계 결과 예시
```
소모 아이템 종류: 15개
스크롤 아이템 종류: 20개
```

## 🚀 향후 개발 방향

### 완료된 기능
- ✅ 상점 통계 (구매/판매, 라운드 정보)
- ✅ 아이템&스크롤 사용 통계
- ✅ 책갈피&조커 사용 통계
- ✅ Mind Map 뷰어
- ✅ 로그 뷰어

### 개발 가능한 추가 기능
- 카드 사용 통계
- 능력치 변화 추적
- 게임 진행 패턴 분석
- 성능 지표 모니터링

## 💡 핵심 학습 포인트

1. **프로토콜 버퍼 구조 이해가 핵심**
2. **중복 제거 로직의 중요성**
3. **사용자 경험을 위한 UI 최적화**
4. **성능과 정확성의 균형**
5. **체계적인 디버깅과 로깅**

## 🔧 코드 예제 및 패턴

### 상점 데이터 추출 핵심 로직
```python
def parse_log_shop_statistics(file_path: str) -> pd.DataFrame:
    # 1. 파일 다운로드 및 압축 해제
    from s3_utils import download_and_decompress
    local_file_path = download_and_decompress(file_path)

    # 2. 프로토 버퍼 파싱
    with open(local_file_path, "rb") as f:
        data = f.read()
    queue = proto.TotalBattleLogQueue_PROTO()
    queue.ParseFromString(data)

    # 3. 이벤트 처리
    shop_events = []
    processed_sales = set()  # 중복 제거용

    for round_block in queue.rounds:
        for logqueue in round_block.logs:
            for log in logqueue.logs:
                # 상점 관련 이벤트만 처리
                if log.EventType in [5, 6, 7, 8, 9]:
                    # 데이터 추출 및 저장
```

### UI 컴포넌트 패턴
```python
def show_shop_stats(filtered_files, log_type="All"):
    st.subheader("🛒 상점 통계")

    # 1. 데이터 로딩 (진행률 표시)
    progress_bar = st.progress(0)
    shop_summary = get_shop_statistics_summary(filtered_files)
    progress_bar.empty()

    # 2. 기본 메트릭 표시
    col1, col2, col3, col4 = st.columns(4)
    with col1:
        st.metric("전체 파일 수", shop_summary['total_files'])

    # 3. 탭으로 구분된 상세 통계
    tab1, tab2, tab3 = st.tabs(["📊 아이템 타입별", "🛒 구매 통계", "💰 판매 통계"])
```

## 📚 참고 자료

### 프로토콜 버퍼 관련
- [Protocol Buffers 공식 문서](https://developers.google.com/protocol-buffers)
- 프로젝트 내 `.proto` 파일들이 스키마 정의

### Streamlit 관련
- [Streamlit 공식 문서](https://docs.streamlit.io)
- 특히 `st.cache_data`, `st.progress`, `st.tabs` 활용

### 성능 최적화
- `concurrent.futures.ThreadPoolExecutor` 활용
- 메모리 효율적인 데이터 처리
- 캐싱 전략

## 🎯 트러블슈팅 가이드

### 자주 발생하는 문제들

1. **프로토 필드 접근 오류**
   ```python
   # ❌ 잘못된 방법
   if ref_value.HasField("gameInfo"):

   # ✅ 올바른 방법
   if hasattr(ref_value, 'gameInfo') and ref_value.gameInfo:
   ```

2. **메모리 부족 문제**
   - 대용량 파일 처리 시 청크 단위로 처리
   - 불필요한 데이터 즉시 해제
   - 캐싱 적절히 활용

3. **UI 응답성 문제**
   - 병렬 처리로 속도 향상
   - 진행률 표시로 사용자 경험 개선
   - 적절한 데이터 페이징

### 디버깅 팁
1. **로그 레벨 조정**: `logger.setLevel(logging.DEBUG)`
2. **데이터 샘플링**: 작은 파일로 먼저 테스트
3. **단계별 검증**: 각 처리 단계마다 결과 확인

---
*이 문서는 다음 개발자가 빠르게 프로젝트를 이해하고 기여할 수 있도록 작성되었습니다.*
*최종 업데이트: 2025-07-01*
