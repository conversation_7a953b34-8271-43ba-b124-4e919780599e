import zstandard
import pandas as pd
from google.protobuf.message import DecodeError

# Protobuf import
from ActionLog_PROTO_pb2 import MafActionLogQueue_PROTO, MafActionLog_PROTO, MafActionLogPayloadDTO_PROTO, MafActionLogScalarValue_PROTO, MafActionLogReferenceValue_PROTO
from ActionLogEnumsFull_pb2 import MAF_ANALYTICS_LOG_EVENT_TYPE, MAF_ACTION_LOG_PAYLOAD_TYPE

def parse_maf_action_log_queue(log_data: bytes) -> MafActionLogQueue_PROTO:
    """MafActionLogQueue_PROTO 데이터를 파싱합니다."""
    maf_action_log_queue = MafActionLogQueue_PROTO()
    try:
        maf_action_log_queue.ParseFromString(log_data)
        return maf_action_log_queue
    except DecodeError as e:
        print(f"Error decoding MafActionLogQueue_PROTO: {e}")
        return None

def decompress_zstd_proto(file_path: str) -> MafActionLogQueue_PROTO:
    """ZStandard로 압축된 Protobuf 파일을 읽고 파싱합니다."""
    dctx = zstandard.ZstdDecompressor()
    decompressed_data = b''
    try:
        with open(file_path, 'rb') as f:
            decompressed_data = dctx.decompress(f.read())
    except Exception as e:
        print(f"Error decompressing file {file_path}: {e}")
        return None

    return parse_maf_action_log_queue(decompressed_data)

def process_log_file(file_path: str) -> pd.DataFrame:
    """단일 로그 파일을 처리하여 DataFrame으로 반환합니다."""
    maf_action_log_queue = decompress_zstd_proto(file_path)
    if not maf_action_log_queue:
        return pd.DataFrame()

    records = []
    for maf_action_log in maf_action_log_queue.logs:
        event_type_name = "UNKNOWN_EVENT_TYPE"
        try:
            event_type_name = MAF_ANALYTICS_LOG_EVENT_TYPE.Name(maf_action_log.EventType)
        except ValueError:
            event_type_name = f"UNKNOWN_EVENT_TYPE_{maf_action_log.EventType}"

        record = {
            "event_type": event_type_name,
            "source": maf_action_log.Source,
        }
        for key, payload_dto in maf_action_log.Payload.items():
            value = None
            try:
                if payload_dto.payloadType == MAF_ACTION_LOG_PAYLOAD_TYPE.MAF_ACTION_LOG_PAYLOAD_TYPE_Int:
                    value = payload_dto.scalarValue.intValue
                elif payload_dto.payloadType == MAF_ACTION_LOG_PAYLOAD_TYPE.MAF_ACTION_LOG_PAYLOAD_TYPE_Bool:
                    value = payload_dto.scalarValue.boolValue
                elif payload_dto.payloadType == MAF_ACTION_LOG_PAYLOAD_TYPE.MAF_ACTION_LOG_PAYLOAD_TYPE_String:
                    value = payload_dto.scalarValue.stringValue
                elif payload_dto.payloadType == MAF_ACTION_LOG_PAYLOAD_TYPE.MAF_ACTION_LOG_PAYLOAD_TYPE_ULong:
                    value = payload_dto.scalarValue.ulongValue
                elif payload_dto.payloadType == MAF_ACTION_LOG_PAYLOAD_TYPE.MAF_ACTION_LOG_PAYLOAD_TYPE_IntList:
                    value = list(payload_dto.referenceValue.intList)
                elif payload_dto.payloadType == MAF_ACTION_LOG_PAYLOAD_TYPE.MAF_ACTION_LOG_PAYLOAD_TYPE_Float:
                    # Float 타입 지원 (현재 proto에 floatValue 필드가 없으므로 stringValue로 처리)
                    value = payload_dto.scalarValue.stringValue
                    try:
                        value = float(value) if value else 0.0
                    except (ValueError, TypeError):
                        value = 0.0
                elif payload_dto.payloadType == MAF_ACTION_LOG_PAYLOAD_TYPE.MAF_ACTION_LOG_PAYLOAD_TYPE_Double:
                    # Double 타입 지원 (현재 proto에 doubleValue 필드가 없으므로 stringValue로 처리)
                    value = payload_dto.scalarValue.stringValue
                    try:
                        value = float(value) if value else 0.0
                    except (ValueError, TypeError):
                        value = 0.0
                elif payload_dto.payloadType == MAF_ACTION_LOG_PAYLOAD_TYPE.MAF_ACTION_LOG_PAYLOAD_TYPE_StringDictionary:
                    # StringDictionary 타입 지원 (JSON 문자열로 처리)
                    value = payload_dto.scalarValue.stringValue
                    try:
                        import json
                        value = json.loads(value) if value else {}
                    except (json.JSONDecodeError, TypeError):
                        # JSON 파싱 실패 시 원본 문자열 유지
                        pass
                elif payload_dto.payloadType == MAF_ACTION_LOG_PAYLOAD_TYPE.MAF_ACTION_LOG_PAYLOAD_TYPE_StringDictionaryList:
                    # StringDictionaryList 타입 지원 (JSON 배열 문자열로 처리)
                    value = payload_dto.scalarValue.stringValue
                    try:
                        import json
                        value = json.loads(value) if value else []
                    except (json.JSONDecodeError, TypeError):
                        # JSON 파싱 실패 시 원본 문자열 유지
                        pass
                else:
                    # 알 수 없는 타입의 경우 문자열 값으로 시도
                    value = payload_dto.scalarValue.stringValue if payload_dto.scalarValue.stringValue else None

            except AttributeError as e:
                # 필드가 존재하지 않는 경우 로깅하고 None으로 설정
                print(f"Warning: Field not found for payload type {payload_dto.payloadType} in key '{key}': {e}")
                value = None
            except Exception as e:
                # 기타 예외 처리
                print(f"Error processing payload key '{key}' with type {payload_dto.payloadType}: {e}")
                value = None

            record[f"payload_{key}"] = value
        records.append(record)
    return pd.DataFrame(records)
