import boto3
from botocore.exceptions import NoCredentialsError, ClientError
import os
import logging
from config import S3_BUCKET_NAME, S3_REGION_NAME, LOCAL_DOWNLOAD_DIR, S3_LOG_PREFIX, S3_LOG_PREFIXES, AWS_ACCESS_KEY_ID, AWS_SECRET_ACCESS_KEY

# 로깅 설정
logger = logging.getLogger(__name__)
logging.basicConfig(level=logging.INFO)

def get_s3_prefix_for_environment(environment: str) -> str:
    """환경에 따른 S3 prefix를 반환합니다."""
    return S3_LOG_PREFIXES.get(environment, S3_LOG_PREFIX)

def get_s3_client():
    """S3 클라이언트를 반환합니다."""
    try:
        # 자격 증명 확인
        access_key = AWS_ACCESS_KEY_ID or os.environ.get("AWS_ACCESS_KEY_ID")
        secret_key = AWS_SECRET_ACCESS_KEY or os.environ.get("AWS_SECRET_ACCESS_KEY")

        logger.info(f"S3 클라이언트 생성 시도 - Region: {S3_REGION_NAME}, Bucket: {S3_BUCKET_NAME}")
        logger.info(f"Access Key 존재: {'Yes' if access_key else 'No'}")
        logger.info(f"Secret Key 존재: {'Yes' if secret_key else 'No'}")

        s3_client = boto3.client(
            's3',
            region_name=S3_REGION_NAME,
            aws_access_key_id=access_key,
            aws_secret_access_key=secret_key
        )

        # 연결 테스트
        try:
            s3_client.head_bucket(Bucket=S3_BUCKET_NAME)
            logger.info(f"S3 버킷 '{S3_BUCKET_NAME}' 연결 성공")
        except ClientError as e:
            error_code = e.response['Error']['Code']
            if error_code == '404':
                logger.error(f"S3 버킷 '{S3_BUCKET_NAME}'을 찾을 수 없습니다")
            elif error_code == '403':
                logger.error(f"S3 버킷 '{S3_BUCKET_NAME}'에 대한 접근 권한이 없습니다")
            else:
                logger.error(f"S3 버킷 연결 오류: {e}")
            return None

        return s3_client
    except NoCredentialsError:
        logger.error("AWS 자격 증명을 찾을 수 없습니다. AWS 자격 증명을 설정해주세요.")
        return None
    except Exception as e:
        logger.error(f"S3 클라이언트 생성 오류: {e}")
        return None

def list_s3_log_files(prefix: str = None, s3_client=None) -> list:
    """S3 버킷에서 로그 파일 목록을 가져옵니다."""
    if s3_client is None:
        s3_client = get_s3_client()
        if not s3_client:
            logger.error("S3 클라이언트를 생성할 수 없어 파일 목록을 가져올 수 없습니다")
            return []

    # prefix가 None이면 기본값 사용
    if prefix is None:
        prefix = S3_LOG_PREFIX

    logger.info(f"S3에서 파일 목록 검색 시작 - Prefix: '{prefix}'")

    log_files = []
    total_objects = 0
    try:
        paginator = s3_client.get_paginator('list_objects_v2')
        pages = paginator.paginate(Bucket=S3_BUCKET_NAME, Prefix=prefix)

        for page in pages:
            if "Contents" in page:
                total_objects += len(page['Contents'])
                for obj in page['Contents']:
                    logger.debug(f"발견된 객체: {obj['Key']}")
                    # .zst 또는 .pb.zst 파일 (압축된 로그 파일)을 찾습니다
                    if obj['Key'].endswith(".zst") or obj['Key'].endswith(".pb.zst"):
                        log_files.append(obj['Key'])
                        logger.debug(f"로그 파일 추가: {obj['Key']}")
            else:
                logger.info(f"페이지에 Contents가 없습니다")

        logger.info(f"검색 완료 - 총 객체: {total_objects}개, .zst 파일: {len(log_files)}개")
        if log_files:
            logger.info(f"발견된 로그 파일들: {log_files[:5]}{'...' if len(log_files) > 5 else ''}")
        else:
            logger.warning(f"'{prefix}' 경로에서 .zst 파일을 찾을 수 없습니다")

    except ClientError as e:
        error_code = e.response['Error']['Code']
        logger.error(f"S3 객체 목록 조회 오류 (코드: {error_code}): {e}")
    except Exception as e:
        logger.error(f"예상치 못한 오류 발생: {e}")

    return log_files

def download_s3_file(s3_key: str, local_path: str, s3_client=None) -> bool:
    """S3에서 파일을 다운로드합니다. (캐싱 지원)"""
    if s3_client is None:
        s3_client = get_s3_client()
        if not s3_client:
            logger.error("S3 클라이언트를 생성할 수 없어 파일을 다운로드할 수 없습니다")
            return False

    # 이미 파일이 존재하고 크기가 0보다 크면 다운로드 스킵
    if os.path.exists(local_path) and os.path.getsize(local_path) > 0:
        logger.debug(f"캐시된 파일 사용: {local_path}")
        return True

    logger.info(f"파일 다운로드 시작: {s3_key} -> {local_path}")

    # 로컬 디렉토리 생성
    local_dir = os.path.dirname(local_path)
    os.makedirs(local_dir, exist_ok=True)
    logger.debug(f"로컬 디렉토리 생성: {local_dir}")

    try:
        s3_client.download_file(S3_BUCKET_NAME, s3_key, local_path)
        logger.info(f"파일 다운로드 완료: {local_path}")
        return True
    except ClientError as e:
        error_code = e.response['Error']['Code']
        logger.error(f"파일 다운로드 오류 (코드: {error_code}) - {s3_key}: {e}")
        return False
    except Exception as e:
        logger.error(f"파일 다운로드 중 예상치 못한 오류 - {s3_key}: {e}")
        return False

def download_multiple_s3_files(s3_keys: list, download_dir: str) -> list:
    """여러 S3 파일을 효율적으로 다운로드합니다. (캐싱 강화)"""
    logger.info(f"다중 파일 다운로드 시작: {len(s3_keys)}개 파일")

    # downloads 디렉토리 확실히 생성
    os.makedirs(download_dir, exist_ok=True)
    logger.info(f"다운로드 디렉토리 확인/생성: {download_dir}")

    # S3 클라이언트를 한 번만 생성
    s3_client = get_s3_client()
    if not s3_client:
        logger.error("S3 클라이언트를 생성할 수 없습니다")
        return []

    downloaded_files = []
    failed_count = 0
    cached_count = 0

    for i, s3_key in enumerate(s3_keys, 1):
        try:
            local_path = os.path.join(download_dir, s3_key)

            # 이미 다운로드된 파일이 있으면 재사용 (캐시 히트)
            if os.path.exists(local_path) and os.path.getsize(local_path) > 0:
                logger.debug(f"캐시된 파일 사용 ({i}/{len(s3_keys)}): {local_path}")
                downloaded_files.append(local_path)
                cached_count += 1
                continue

            # S3에서 파일 다운로드 (같은 클라이언트 재사용)
            if download_s3_file(s3_key, local_path, s3_client):
                downloaded_files.append(local_path)
                logger.debug(f"새로 다운로드 완료 ({i}/{len(s3_keys)}): {s3_key}")
            else:
                logger.warning(f"다운로드 실패 ({i}/{len(s3_keys)}): {s3_key}")
                failed_count += 1

        except Exception as e:
            logger.error(f"다운로드 오류 ({i}/{len(s3_keys)}) {s3_key}: {e}")
            failed_count += 1

    logger.info(f"다중 파일 다운로드 완료: {len(downloaded_files)}개 성공 (캐시: {cached_count}개, 새로운: {len(downloaded_files)-cached_count}개), {failed_count}개 실패")
    return downloaded_files

def get_cache_info(download_dir: str) -> dict:
    """다운로드 캐시 디렉토리의 정보를 반환합니다."""
    if not os.path.exists(download_dir):
        return {
            'exists': False,
            'total_files': 0,
            'total_size_mb': 0,
            'subdirs': []
        }

    total_files = 0
    total_size = 0
    subdirs = []

    try:
        for root, dirs, files in os.walk(download_dir):
            total_files += len(files)
            for file in files:
                file_path = os.path.join(root, file)
                try:
                    total_size += os.path.getsize(file_path)
                except OSError:
                    pass  # 파일 접근 오류 무시

            # 하위 디렉토리 정보 수집
            if root != download_dir:
                rel_path = os.path.relpath(root, download_dir)
                subdirs.append(rel_path)

    except Exception as e:
        logger.error(f"캐시 정보 수집 중 오류: {e}")

    return {
        'exists': True,
        'total_files': total_files,
        'total_size_mb': round(total_size / (1024 * 1024), 2),
        'subdirs': subdirs[:10]  # 최대 10개 하위 디렉토리만 표시
    }

def clear_cache_directory(download_dir: str) -> bool:
    """다운로드 캐시 디렉토리를 정리합니다."""
    try:
        if os.path.exists(download_dir):
            import shutil
            shutil.rmtree(download_dir)
            logger.info(f"캐시 디렉토리 정리 완료: {download_dir}")
            return True
        else:
            logger.info(f"캐시 디렉토리가 존재하지 않음: {download_dir}")
            return True
    except Exception as e:
        logger.error(f"캐시 디렉토리 정리 실패: {e}")
        return False
