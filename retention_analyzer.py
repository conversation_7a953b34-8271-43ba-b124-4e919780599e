#!/usr/bin/env python3
"""
리텐션 분석 모듈
로그 데이터에서 time과 user_register_date를 기반으로 리텐션 지표를 계산합니다.
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
import logging
from log_parser import process_log_file

logger = logging.getLogger(__name__)

def extract_login_events_for_retention(df: pd.DataFrame) -> pd.DataFrame:
    """
    리텐션 분석을 위한 로그인 이벤트 데이터를 추출합니다.

    조건:
    1. event_type이 MAF_ANALYTICS_LOG_EVENT_TYPE_LOG_LOGIN인 로그만 검사
    2. payload_user_id, payload_time, payload_device_id, payload_user_register_date 필수
    3. 동일한 user_id가 같은 날에 중복 집계되지 않도록 처리

    Args:
        df: process_log_file에서 반환된 DataFrame

    Returns:
        리텐션 분석용 정리된 DataFrame
    """
    if df.empty:
        return pd.DataFrame()

    # 1. MAF_ANALYTICS_LOG_EVENT_TYPE_LOG_LOGIN 이벤트만 필터링
    login_events = df[df['event_type'] == 'MAF_ANALYTICS_LOG_EVENT_TYPE_LOG_LOGIN'].copy()

    if login_events.empty:
        logger.info("MAF_ANALYTICS_LOG_EVENT_TYPE_LOG_LOGIN 이벤트가 없습니다.")
        return pd.DataFrame()

    logger.info(f"로그인 이벤트 발견: {len(login_events)}개")

    # 2. 필수 payload 컬럼들을 찾습니다
    required_columns = {
        'payload_user_id': [col for col in login_events.columns if 'payload_user_id' in col.lower()],
        'payload_time': [col for col in login_events.columns if 'payload_time' in col.lower()],
        'payload_device_id': [col for col in login_events.columns if 'payload_device_id' in col.lower()],
        'payload_user_register_date': [col for col in login_events.columns if 'payload_user_register_date' in col.lower()]
    }

    logger.info(f"발견된 필수 컬럼들:")
    for key, cols in required_columns.items():
        logger.info(f"  {key}: {cols}")

    # 3. 데이터 추출 및 검증
    result_data = []
    excluded_counts = {
        'no_user_id': 0,
        'no_time': 0,
        'no_device_id': 0,
        'no_register_date': 0,
        'invalid_data': 0
    }

    for idx, row in login_events.iterrows():
        # payload_user_id 추출
        user_id_value = None
        for col in required_columns['payload_user_id']:
            if pd.notna(row[col]) and row[col] != '':
                user_id_value = row[col]
                break

        if not user_id_value:
            excluded_counts['no_user_id'] += 1
            continue

        # payload_time 추출
        time_value = None
        for col in required_columns['payload_time']:
            if pd.notna(row[col]) and row[col] != '':
                time_value = row[col]
                break

        if not time_value:
            excluded_counts['no_time'] += 1
            continue

        # payload_device_id 추출
        device_id_value = None
        for col in required_columns['payload_device_id']:
            if pd.notna(row[col]) and row[col] != '':
                device_id_value = row[col]
                break

        if not device_id_value:
            excluded_counts['no_device_id'] += 1
            continue

        # payload_user_register_date 추출 (필수)
        register_date_value = None
        for col in required_columns['payload_user_register_date']:
            if pd.notna(row[col]) and row[col] != '':
                register_date_value = row[col]
                break

        # user_register_date가 없는 레코드는 집계에서 제외
        if not register_date_value:
            excluded_counts['no_register_date'] += 1
            continue

        # 모든 조건을 만족하는 레코드 추가
        result_data.append({
            'event_type': row['event_type'],
            'user_id': user_id_value,
            'device_id': device_id_value,
            'time': time_value,
            'user_register_date': register_date_value,
            'original_index': idx
        })

    # 제외된 레코드 통계 로깅
    logger.info(f"제외된 레코드 통계:")
    logger.info(f"  payload_user_id 없음: {excluded_counts['no_user_id']}개")
    logger.info(f"  payload_time 없음: {excluded_counts['no_time']}개")
    logger.info(f"  payload_device_id 없음: {excluded_counts['no_device_id']}개")
    logger.info(f"  payload_user_register_date 없음 (임시로 2025-07-01 사용): {excluded_counts['no_register_date']}개")
    logger.info(f"유효한 로그인 이벤트: {len(result_data)}개")

    if not result_data:
        return pd.DataFrame()

    result_df = pd.DataFrame(result_data)

    # 4. 동일한 user_id가 같은 날에 중복 집계되지 않도록 처리
    # 먼저 시간을 날짜로 변환
    result_df['access_datetime'] = pd.to_datetime(result_df['time'], errors='coerce')
    result_df['access_date'] = result_df['access_datetime'].dt.date

    # 같은 날 같은 user_id의 중복 제거 (첫 번째 로그인만 유지)
    before_dedup = len(result_df)
    result_df = result_df.drop_duplicates(subset=['user_id', 'access_date'], keep='first')
    after_dedup = len(result_df)

    logger.info(f"일별 중복 제거: {before_dedup} → {after_dedup} ({before_dedup - after_dedup}개 중복 제거)")

    return result_df

def convert_timestamps_to_dates(df: pd.DataFrame) -> pd.DataFrame:
    """
    타임스탬프를 날짜로 변환합니다.

    Args:
        df: 로그인 이벤트 DataFrame (이미 access_date가 있을 수 있음)

    Returns:
        날짜가 변환된 DataFrame
    """
    if df.empty:
        return df

    df = df.copy()

    # access_date가 이미 있으면 건너뛰기
    if 'access_date' not in df.columns and 'time' in df.columns:
        try:
            # 문자열 형태의 datetime을 pandas datetime으로 변환
            df['access_datetime'] = pd.to_datetime(df['time'], errors='coerce')
            df['access_date'] = df['access_datetime'].dt.date

            logger.info(f"접속 시간 변환 완료: {df['access_date'].notna().sum()}개 유효한 날짜")

        except Exception as e:
            logger.warning(f"time 컬럼 변환 실패: {e}")
            df['access_date'] = None

    # user_register_date 컬럼 처리
    if 'user_register_date' in df.columns:
        try:
            # user_register_date를 pandas datetime으로 변환
            df['register_datetime'] = pd.to_datetime(df['user_register_date'], errors='coerce')
            df['register_date'] = df['register_datetime'].dt.date

            logger.info(f"등록일 변환 완료: {df['register_date'].notna().sum()}개 유효한 등록일")

        except Exception as e:
            logger.warning(f"user_register_date 컬럼 변환 실패: {e}")
            df['register_date'] = None

    return df

def calculate_user_retention(df: pd.DataFrame, end_date=None) -> Dict:
    """
    사용자별 리텐션을 계산합니다.
    user_id와 device_id를 모두 집계합니다.

    Args:
        df: 날짜가 변환된 로그인 이벤트 DataFrame

    Returns:
        리텐션 분석 결과 딕셔너리
    """
    if df.empty or 'access_date' not in df.columns or 'register_date' not in df.columns:
        return {
            'daily_retention': pd.DataFrame(),
            'cohort_analysis': pd.DataFrame(),
            'summary': {}
        }

    # 유효한 데이터만 필터링
    valid_data = df.dropna(subset=['access_date', 'register_date', 'user_id', 'device_id'])

    if valid_data.empty:
        return {
            'daily_retention': pd.DataFrame(),
            'cohort_analysis': pd.DataFrame(),
            'summary': {'total_users': 0, 'total_devices': 0, 'valid_records': 0}
        }

    # 사용자별 정보 집계
    user_info = valid_data.groupby('user_id').agg({
        'register_date': 'first',
        'access_date': ['first', 'last', 'nunique'],
        'device_id': 'nunique'  # 사용자별 디바이스 수
    }).reset_index()

    user_info.columns = ['user_id', 'register_date', 'first_access', 'last_access', 'total_access_days', 'device_count']

    # 디바이스별 정보 집계
    device_info = valid_data.groupby('device_id').agg({
        'register_date': 'first',
        'access_date': ['first', 'last', 'nunique'],
        'user_id': 'nunique'  # 디바이스별 사용자 수
    }).reset_index()

    device_info.columns = ['device_id', 'register_date', 'first_access', 'last_access', 'total_access_days', 'user_count']

    # 등록일 기준 리텐션 계산 (사용자 기준)
    user_retention_data = []

    for _, user in user_info.iterrows():
        user_id = user['user_id']
        register_date = user['register_date']

        # 해당 사용자의 모든 접속 날짜
        user_accesses = valid_data[valid_data['user_id'] == user_id]['access_date'].unique()

        # 등록일로부터 각 날짜까지의 차이 계산
        for access_date in user_accesses:
            days_since_register = (access_date - register_date).days

            user_retention_data.append({
                'user_id': user_id,
                'register_date': register_date,
                'access_date': access_date,
                'days_since_register': days_since_register
            })

    user_retention_df = pd.DataFrame(user_retention_data)

    # 등록일 기준 리텐션 계산 (디바이스 기준)
    device_retention_data = []

    for _, device in device_info.iterrows():
        device_id = device['device_id']
        register_date = device['register_date']

        # 해당 디바이스의 모든 접속 날짜
        device_accesses = valid_data[valid_data['device_id'] == device_id]['access_date'].unique()

        # 등록일로부터 각 날짜까지의 차이 계산
        for access_date in device_accesses:
            days_since_register = (access_date - register_date).days

            device_retention_data.append({
                'device_id': device_id,
                'register_date': register_date,
                'access_date': access_date,
                'days_since_register': days_since_register
            })

    device_retention_df = pd.DataFrame(device_retention_data)

    # 일별 리텐션 계산 (사용자 기준)
    daily_user_retention = []
    for day in range(0, 31):  # 0일부터 30일까지
        users_at_day = user_retention_df[user_retention_df['days_since_register'] == day]['user_id'].nunique()
        total_users = len(user_info)
        retention_rate = (users_at_day / total_users * 100) if total_users > 0 else 0

        daily_user_retention.append({
            'day': day,
            'users': users_at_day,
            'retention_rate': retention_rate
        })

    daily_user_retention_df = pd.DataFrame(daily_user_retention)

    # 일별 리텐션 계산 (디바이스 기준)
    daily_device_retention = []
    for day in range(0, 31):  # 0일부터 30일까지
        devices_at_day = device_retention_df[device_retention_df['days_since_register'] == day]['device_id'].nunique()
        total_devices = len(device_info)
        retention_rate = (devices_at_day / total_devices * 100) if total_devices > 0 else 0

        daily_device_retention.append({
            'day': day,
            'devices': devices_at_day,
            'retention_rate': retention_rate
        })

    daily_device_retention_df = pd.DataFrame(daily_device_retention)

    # 코호트 분석 (사용자 기준)
    user_cohort_data = user_retention_df.groupby(['register_date', 'days_since_register']).agg({
        'user_id': 'nunique'
    }).reset_index()

    user_cohort_pivot = user_cohort_data.pivot(index='register_date', columns='days_since_register', values='user_id').fillna(0)

    # 코호트 분석 (디바이스 기준)
    device_cohort_data = device_retention_df.groupby(['register_date', 'days_since_register']).agg({
        'device_id': 'nunique'
    }).reset_index()

    device_cohort_pivot = device_cohort_data.pivot(index='register_date', columns='days_since_register', values='device_id').fillna(0)

    # 요약 정보
    summary = {
        'total_users': len(user_info),
        'total_devices': len(device_info),
        'unique_user_device_pairs': len(valid_data.groupby(['user_id', 'device_id'])),
        'valid_records': len(valid_data),
        'date_range': {
            'start': valid_data['access_date'].min(),
            'end': valid_data['access_date'].max()
        },
        'register_date_range': {
            'start': valid_data['register_date'].min(),
            'end': valid_data['register_date'].max()
        }
    }

    # 새로운 리텐션 기준으로 코호트 테이블 생성 (UserID 기준)
    new_cohort_table_user = create_cohort_retention_table_new(valid_data, end_date=end_date, id_type='user_id')

    # DeviceID 기준 코호트 테이블 생성
    new_cohort_table_device = create_cohort_retention_table_new(valid_data, end_date=end_date, id_type='device_id')

    return {
        'daily_retention': daily_user_retention_df,  # 기본은 사용자 기준
        'daily_user_retention': daily_user_retention_df,
        'daily_device_retention': daily_device_retention_df,
        'cohort_analysis': user_cohort_pivot,  # 기존 코호트 분석 (호환성)
        'new_cohort_table': new_cohort_table_user,  # 기존 호환성을 위해 UserID 기준 유지
        'new_cohort_table_user': new_cohort_table_user,  # UserID 기준 코호트 테이블
        'new_cohort_table_device': new_cohort_table_device,  # DeviceID 기준 코호트 테이블
        'user_cohort_analysis': user_cohort_pivot,
        'device_cohort_analysis': device_cohort_pivot,
        'user_info': user_info,
        'device_info': device_info,
        'summary': summary
    }

def analyze_retention_from_log_file(file_path: str) -> Dict:
    """
    로그 파일에서 리텐션을 분석합니다.
    
    Args:
        file_path: 로그 파일 경로
        
    Returns:
        리텐션 분석 결과
    """
    logger.info(f"리텐션 분석 시작: {file_path}")
    
    # 1. 로그 파일 파싱
    df = process_log_file(file_path)
    if df.empty:
        logger.warning("로그 파일이 비어있습니다.")
        return {'error': '로그 파일이 비어있습니다.'}
    
    logger.info(f"로그 파일 파싱 완료: {len(df)}개 레코드")
    
    # 2. 로그인 이벤트 추출 (새로운 집계 기준 적용)
    login_df = extract_login_events_for_retention(df)
    if login_df.empty:
        logger.warning("MAF_ANALYTICS_LOG_EVENT_TYPE_LOG_LOGIN 이벤트 또는 필수 정보를 찾을 수 없습니다.")
        return {'error': 'MAF_ANALYTICS_LOG_EVENT_TYPE_LOG_LOGIN 이벤트 또는 필수 정보를 찾을 수 없습니다.'}
    
    logger.info(f"로그인 이벤트 추출 완료: {len(login_df)}개 레코드")

    # 3. 타임스탬프를 날짜로 변환 (이미 일부 변환되었을 수 있음)
    date_df = convert_timestamps_to_dates(login_df)
    
    # 4. 리텐션 계산
    retention_result = calculate_user_retention(date_df)
    
    logger.info(f"리텐션 분석 완료: {retention_result['summary']}")
    
    return retention_result

def analyze_retention_from_multiple_files(file_paths: List[str], end_date=None) -> Dict:
    """
    여러 로그 파일에서 리텐션을 분석합니다.

    Args:
        file_paths: 로그 파일 경로 리스트

    Returns:
        통합된 리텐션 분석 결과
    """
    logger.info(f"다중 파일 리텐션 분석 시작: {len(file_paths)}개 파일")

    all_time_data = []
    processed_files = 0
    failed_files = 0

    for file_path in file_paths:
        try:
            logger.info(f"파일 처리 중: {file_path}")

            # 1. 로그 파일 파싱
            df = process_log_file(file_path)
            if df.empty:
                logger.warning(f"빈 파일 건너뜀: {file_path}")
                failed_files += 1
                continue

            # 2. 로그인 이벤트 추출 (새로운 집계 기준 적용)
            login_df = extract_login_events_for_retention(df)
            if login_df.empty:
                logger.warning(f"로그인 이벤트 또는 필수 정보 없는 파일 건너뜀: {file_path}")
                failed_files += 1
                continue

            # 3. 타임스탬프를 날짜로 변환 (이미 일부 변환되었을 수 있음)
            date_df = convert_timestamps_to_dates(login_df)

            # 파일명 정보 추가
            date_df['source_file'] = file_path

            all_time_data.append(date_df)
            processed_files += 1

            logger.info(f"파일 처리 완료: {file_path} ({len(date_df)}개 레코드)")

        except Exception as e:
            logger.error(f"파일 처리 실패 {file_path}: {e}")
            failed_files += 1
            continue

    if not all_time_data:
        return {
            'error': f'처리 가능한 파일이 없습니다. 총 {len(file_paths)}개 파일 중 {failed_files}개 실패',
            'processed_files': 0,
            'failed_files': failed_files
        }

    # 모든 데이터 통합
    logger.info("데이터 통합 중...")
    combined_df = pd.concat(all_time_data, ignore_index=True)

    # 중복 제거 (같은 사용자의 같은 시간 접속)
    before_dedup = len(combined_df)
    # 새로운 집계 기준에 맞게 user_id와 time으로 중복 제거
    if 'user_id' in combined_df.columns and 'time' in combined_df.columns:
        combined_df = combined_df.drop_duplicates(subset=['user_id', 'time'], keep='first')
    elif 'user_id' in combined_df.columns and 'access_date' in combined_df.columns:
        # 이미 날짜별로 중복 제거가 되었을 수 있음
        combined_df = combined_df.drop_duplicates(subset=['user_id', 'access_date'], keep='first')
    else:
        logger.warning("중복 제거를 위한 적절한 컬럼을 찾을 수 없습니다.")

    after_dedup = len(combined_df)

    logger.info(f"중복 제거: {before_dedup} → {after_dedup} ({before_dedup - after_dedup}개 중복 제거)")

    # 리텐션 계산 (날짜 범위 전달)
    retention_result = calculate_user_retention(combined_df, end_date=end_date)

    # 추가 정보
    retention_result['file_processing'] = {
        'total_files': len(file_paths),
        'processed_files': processed_files,
        'failed_files': failed_files,
        'total_records_before_dedup': before_dedup,
        'total_records_after_dedup': after_dedup,
        'duplicates_removed': before_dedup - after_dedup
    }

    logger.info(f"다중 파일 리텐션 분석 완료: {processed_files}개 파일 처리, {retention_result['summary']['total_users']}명 사용자")

    return retention_result

def analyze_retention_from_s3_files(s3_file_keys: List[str], download_dir: str, end_date=None) -> Dict:
    """
    S3 파일들을 다운로드하고 리텐션을 분석합니다.

    Args:
        s3_file_keys: S3 파일 키 리스트
        download_dir: 로컬 다운로드 디렉토리

    Returns:
        리텐션 분석 결과
    """
    from s3_utils import download_multiple_s3_files

    logger.info(f"S3 파일 다운로드 및 리텐션 분석 시작: {len(s3_file_keys)}개 파일")

    # 최적화된 다중 파일 다운로드 (S3 클라이언트 재사용)
    downloaded_files = download_multiple_s3_files(s3_file_keys, download_dir)

    if not downloaded_files:
        return {
            'error': f'다운로드된 파일이 없습니다. 총 {len(s3_file_keys)}개 파일 다운로드 실패',
            'download_failed': len(s3_file_keys)
        }

    download_failed = len(s3_file_keys) - len(downloaded_files)
    logger.info(f"다운로드 완료: {len(downloaded_files)}개 파일 성공, {download_failed}개 실패")

    # 다운로드된 파일들로 리텐션 분석
    result = analyze_retention_from_multiple_files(downloaded_files, end_date=end_date)

    # 다운로드 정보 추가
    if 'file_processing' not in result:
        result['file_processing'] = {}

    result['file_processing']['download_info'] = {
        'total_s3_files': len(s3_file_keys),
        'downloaded_files': len(downloaded_files),
        'download_failed': download_failed
    }

    return result

def create_cohort_retention_table_new(valid_data_df: pd.DataFrame, end_date=None, id_type='user_id') -> pd.DataFrame:
    """
    새로운 리텐션 기준으로 코호트 리텐션 테이블을 생성합니다.

    리텐션 기준:
    - 각 가입일별로 사용자/디바이스 그룹화
    - 가입일 기준으로 +0, +1, +2, +3, +4, +5, +6, +7, +15, +30일 후 접속률 계산
    - 사용자/디바이스는 자신의 가입일 또는 그 이후 날짜의 리텐션에만 포함

    Args:
        valid_data_df: 일별 접속 기록 DataFrame (user_id, register_date, access_date, device_id)
        end_date: 분석 종료 날짜
        id_type: 'user_id' 또는 'device_id' - 집계 기준 ID 타입

    Returns:
        코호트 리텐션 테이블 DataFrame
    """
    if valid_data_df.empty:
        logger.warning("유효한 데이터 DataFrame이 비어있습니다.")
        return pd.DataFrame()

    # ID 타입에 따른 필수 컬럼 설정
    if id_type == 'device_id':
        required_columns = ['device_id', 'register_date', 'access_date']
        id_column = 'device_id'
        table_title = 'DeviceID'
    else:
        required_columns = ['user_id', 'register_date', 'access_date']
        id_column = 'user_id'
        table_title = 'UserID'

    missing_columns = [col for col in required_columns if col not in valid_data_df.columns]

    if missing_columns:
        logger.error(f"필수 컬럼이 없습니다: {missing_columns}")
        return pd.DataFrame()

    # 유효한 데이터만 필터링
    valid_data = valid_data_df.dropna(subset=required_columns).copy()

    if valid_data.empty:
        logger.warning("유효한 접속 데이터가 없습니다.")
        return pd.DataFrame()

    logger.info(f"{table_title} 데이터 필터링: 원본 {len(valid_data_df)}개 → 유효 {len(valid_data)}개")

    logger.info(f"리텐션 분석 대상 레코드: {len(valid_data)}개")

    # 날짜 타입 확인 및 변환
    if not pd.api.types.is_datetime64_any_dtype(valid_data['register_date']):
        valid_data['register_date'] = pd.to_datetime(valid_data['register_date'])
    if not pd.api.types.is_datetime64_any_dtype(valid_data['access_date']):
        valid_data['access_date'] = pd.to_datetime(valid_data['access_date'])

    # 날짜만 추출 (시간 제거)
    valid_data['register_date'] = valid_data['register_date'].dt.date
    valid_data['access_date'] = valid_data['access_date'].dt.date

    # ID별 가입일과 접속일 목록 생성
    if id_type == 'device_id':
        # DeviceID의 경우: 각 접속 기록의 사용자 등록일을 그대로 사용
        # (device_id, register_date) 조합별로 처리하여 정확한 리텐션 계산
        id_register_dates = {}
        id_access_dates = {}

        for _, row in valid_data.iterrows():
            device_id = row['device_id']
            register_date = row['register_date']
            access_date = row['access_date']

            # 각 접속 기록을 (device_id, register_date) 조합으로 처리
            composite_key = f"{device_id}_{register_date}"

            if composite_key not in id_register_dates:
                id_register_dates[composite_key] = register_date
                id_access_dates[composite_key] = set()

            id_access_dates[composite_key].add(access_date)

        logger.info(f"DeviceID 조합 생성: {len(id_register_dates)}개 (device_id, register_date) 조합")
    else:
        # UserID의 경우: 기존 방식 유지
        id_register_dates = valid_data.groupby(id_column)['register_date'].first().to_dict()
        id_access_dates = valid_data.groupby(id_column)['access_date'].apply(set).to_dict()



    # 선택된 날짜 기준 이전 30일까지의 가입일만 필터링
    from datetime import date, timedelta
    if end_date is None:
        end_date = date.today()
    elif isinstance(end_date, str):
        end_date = pd.to_datetime(end_date).date()

    start_date = end_date - timedelta(days=30)

    all_register_dates = sorted(valid_data['register_date'].unique())
    register_dates = [d for d in all_register_dates if start_date <= d <= end_date]

    logger.info(f"전체 가입일 범위: {len(all_register_dates)}개 날짜")
    logger.info(f"선택된 범위 ({start_date} ~ {end_date}) 가입일: {len(register_dates)}개 날짜")

    # 리텐션 계산할 일수 목록
    retention_days = [0, 1, 2, 3, 4, 5, 6, 7, 15, 30]

    cohort_data = []

    # 날짜 범위 내의 모든 날짜를 생성 (사용자가 없어도 표시)
    from datetime import timedelta
    all_dates_in_range = []
    current_date = start_date
    while current_date <= end_date:
        all_dates_in_range.append(current_date)
        current_date += timedelta(days=1)

    for register_date in all_dates_in_range:
        if id_type == 'device_id':
            # DeviceID의 경우: 해당 날짜에 등록한 사용자가 사용한 디바이스들
            cohort_ids = [id_value for id_value, reg_date in id_register_dates.items()
                         if reg_date == register_date]

            # composite_key에서 실제 device_id 추출하여 중복 제거
            actual_device_ids = list(set([comp_key.split('_')[0] for comp_key in cohort_ids]))
            total_count = len(actual_device_ids)
        else:
            # UserID의 경우: 기존 방식
            cohort_ids = [id_value for id_value, reg_date in id_register_dates.items()
                         if reg_date == register_date]
            total_count = len(cohort_ids)

        row_data = {
            '등록일': register_date.strftime('%Y-%m-%d'),
            f'총 {table_title}': total_count
        }

        # 각 일차별 리텐션 계산
        for day in retention_days:
            # 가입일로부터 day일 후의 날짜 계산
            target_date = register_date + timedelta(days=day)

            if total_count == 0:
                # 사용자/디바이스가 없으면 0으로 표시
                retention_rate = 0
            elif day == 0:
                # +0일(등록일 당일)은 항상 100% (등록 자체가 접속을 의미)
                retention_rate = 1.0
            else:
                if id_type == 'device_id':
                    # DeviceID의 경우: composite_key 기준으로 접속 확인 후 실제 device_id로 중복 제거
                    active_device_ids = set()
                    for comp_key in cohort_ids:
                        if target_date in id_access_dates.get(comp_key, set()):
                            actual_device_id = comp_key.split('_')[0]
                            active_device_ids.add(actual_device_id)

                    active_count = len(active_device_ids)
                else:
                    # UserID의 경우: 기존 방식
                    active_count = 0
                    for id_value in cohort_ids:
                        if target_date in id_access_dates.get(id_value, set()):
                            active_count += 1

                retention_rate = (active_count / total_count) if total_count > 0 else 0

            row_data[f'+{day}일'] = retention_rate

        cohort_data.append(row_data)

    if not cohort_data:
        logger.warning("코호트 데이터가 생성되지 않았습니다.")
        return pd.DataFrame()

    cohort_df = pd.DataFrame(cohort_data)

    # 등록일 기준으로 정렬 (최신 날짜가 위로)
    cohort_df = cohort_df.sort_values('등록일', ascending=False)

    # 최대 30줄까지만 표시
    if len(cohort_df) > 30:
        cohort_df = cohort_df.head(30)
        logger.info(f"코호트 테이블을 30줄로 제한했습니다.")

    logger.info(f"새로운 {table_title} 기준 코호트 테이블 생성 완료: {len(cohort_df)}개 코호트")
    return cohort_df


def create_cohort_retention_table(user_info_df: pd.DataFrame) -> pd.DataFrame:
    """
    기존 호환성을 위한 래퍼 함수 - 사용자 정보에서 코호트 테이블 생성
    """
    logger.warning("기존 create_cohort_retention_table 함수가 호출되었습니다. 새로운 함수를 사용하세요.")
    return pd.DataFrame()  # 빈 DataFrame 반환

def create_cohort_retention_heatmap_data(user_info_df: pd.DataFrame) -> pd.DataFrame:
    """
    사용자 정보 DataFrame으로부터 코호트 리텐션 히트맵용 데이터를 생성합니다.

    Args:
        user_info_df: 사용자 정보 DataFrame

    Returns:
        히트맵용 DataFrame (숫자 값)
    """
    if user_info_df.empty:
        return pd.DataFrame()

    required_columns = ['user_id', 'register_date', 'first_access', 'last_access']
    missing_columns = [col for col in required_columns if col not in user_info_df.columns]

    if missing_columns:
        logger.error(f"히트맵 생성을 위한 필수 컬럼이 없습니다: {missing_columns}")
        return pd.DataFrame()

    # 유효한 데이터만 필터링
    valid_data = user_info_df.dropna(subset=['register_date', 'first_access'])

    if valid_data.empty:
        return pd.DataFrame()

    # 코호트 테이블 생성 (숫자 값만)
    cohort_table = create_cohort_retention_table(user_info_df)

    if cohort_table.empty:
        return pd.DataFrame()

    # 숫자 컬럼만 선택 (등록일, 총 사용자 제외)
    numeric_columns = [col for col in cohort_table.columns if col.endswith('일')]

    if not numeric_columns:
        return pd.DataFrame()

    # 등록일을 인덱스로 설정하고 숫자 데이터만 반환
    heatmap_data = cohort_table.set_index('등록일')[numeric_columns]

    return heatmap_data


