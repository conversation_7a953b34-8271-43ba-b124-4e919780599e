﻿syntax = "proto3";
package google.protobuf;
//import "google/protobuf/any.proto";

import "BattleEnumsFull.proto";

message BattleLog_PROTO {
  EBT_BATTLE_LOG_EVENT_TYPE EventType = 1;
  string Source = 2;
  map<string, BattleLogPayloadDTO_PROTO> Payload = 3;
  repeated BattleLogPayloadDTO_PROTO PayloadOrder = 4;
}

message BattleLogQueue_PROTO {
    int32 oder = 1;
    repeated BattleLog_PROTO logs = 2;
}

message BattleLogRound_PROTO {
    int32 round_index = 1;
    repeated BattleLogQueue_PROTO logs = 2;
}

message TotalBattleLogQueue_PROTO {
    repeated BattleLogRound_PROTO rounds = 1;
}

message BattleLogPayloadDTO_PROTO
{
  string key = 1;
  EBT_BATTLE_LOG_PAYLOAD_TYPE payloadType = 2;

  BattleLogScalarValue_PROTO scalarValue = 3;
  BattleLogReferenceValue_PROTO referenceValue = 4;
}

message BattleLogScalarValue_PROTO{
  int32 intValue = 1;
  uint64 ulongValue = 2;
  bool boolValue = 3;
  string stringValue = 4;
  ETB_OVER_KILL overKillType = 5;
  ETB_ABILITY_TYPE abilityType = 6;
  ETB_Poker_Hands_Type pokerHandsType = 7;
  EBT_SOUND_TYPE soundType = 8;
  ETB_IN_GAME_MISSION_REWARD_TYPE missionRewardType = 9;
  repeated int32 cardIndices = 10;
  string floatValue = 11;
  string doubleValue = 12;
}

message BattleLogReferenceValue_PROTO{
  PlayingCard_PROTO card = 1;
  repeated PlayingCard_PROTO cardList = 2;
  repeated InGameShopProduct_PROTO productList = 3;
  repeated int32 intList = 4;
  InGameJoker_PROTO joker = 5;
  BattleLogReferenceValue_PROTO_StringDictionary stringDictionary = 6;
  repeated BattleLogReferenceValue_PROTO_StringDictionary stringDictionaryList = 7;
  InGameShopProduct_PROTO product = 8;
  EvaluateResult_PROTO evaluateResult = 9;
  EvaluateAttentionConfig_PROTO evaluateConfig = 10;
  InGameInfoSnapshot_PROTO gameInfo = 11;
  EvaluateContext_PROTO evaluateContext = 12;
  InGameConsumeItem_PROTO consumeItem = 13;
}

message BattleLogReferenceValue_PROTO_StringDictionary{
  map<string,string> stringDictionary = 1;
}

message InGameInfoSnapshot_PROTO {
  ETB_BATTLE_STATE battleState = 1;
  ETB_BATTLE_STATE missionBeforeState = 2;
  int32 handCountMax = 3;
  int32 jokerCountMax = 4;
  int32 itemCountMax = 5;
  int32 attackCountMax = 6;
  int32 throwCountMax = 7;
  int32 attackTryCount = 8;
  int32 throwTryCount = 9;
  int32 freeItemCount = 10;
  int32 dollarUse = 11;
  string coin = 12;
  string monsterCurrentHP = 13;
  string monsterMaxHP = 14;
  InGameMissionInfo_PROTO currentMissionInfo = 15;
  int32 currentMonsterID = 16;
  repeated PlayingCard_PROTO originFullDeck = 17;
  repeated PlayingCard_PROTO playingFullDeck = 18;
  repeated PlayingCard_PROTO playingDeck = 19;
  repeated PlayingCard_PROTO destroyedPlayingCards = 20;
  repeated InGameAbility_PROTO monsterAbilityList = 21;
  repeated InGameConsumeItem_PROTO playingConsumeItems = 22;
  repeated InGameJoker_PROTO playingJokers = 23;
  repeated InGameBookMark_PROTO playingBookMarks = 24;
  repeated PokerHandData_PROTO pokerHandDataList = 25;
  string userID = 26;
  int32 userLevel = 27;
  uint64 seed = 28;
}

message InGameInfoSaveData_PROTO {
  repeated PlayingCard_PROTO originFullDeck = 1;
  repeated PlayingCard_PROTO destroyedPlayingCards = 2;
  repeated InGameConsumeItem_PROTO playingConsumeItems = 3;
  repeated InGameJoker_PROTO playingJokers = 4;
  repeated InGameBookMark_PROTO playingBookMarks = 5;
  repeated PokerHandData_PROTO pokerHandDataList = 6;
  string coin = 7;
  string shopLevel = 8;
}

message PlayingCardList_PROTO {
  repeated PlayingCard_PROTO cards = 1;
}

message PlayingCard_PROTO {
  int32 cardDataIndex = 1;
  InGameCardLabel_PROTO label = 2;
  bool isBack = 3;
  uint64 chip = 4;
}

message EvaluateResult_PROTO {
  uint64 chips = 1;
  uint64 xChips = 2;
  float mult = 3;
  float xMult = 4;
  int32 pDollars = 5;
  bool isRepeat = 6;
  bool isDestroy = 7;
  ETB_CALCULATOR_AREA calculateArea = 8;
  ETB_IN_GAME_STATE_TURN calculateTurn = 9;
  bool isEffectChanged = 10;
  Abilitiable_PROTO abilitiable = 11;
  PlayingCard_PROTO card = 12;
  ETB_ABILITY_CALCULATE_TYPE calculateType_chips = 13;
  ETB_ABILITY_CALCULATE_TYPE calculateType_xChips = 14;
  ETB_ABILITY_CALCULATE_TYPE calculateType_mult = 15;
  ETB_ABILITY_CALCULATE_TYPE calculateType_xMult = 16;
  ETB_ABILITY_CALCULATE_TYPE calculateType_dollars = 17;
  repeated EvaluateResult_PROTO extraResults = 18;
  ETB_ABILITY_TYPE abilityType = 19;
}

message EvaluateContext_PROTO {
  uint64 chip = 1;
  string multiple = 2;
}

message EvaluateAttentionConfig_PROTO {
  PlayingCard_PROTO card = 1;
  Abilitiable_PROTO abilitiable = 2;
  EBT_SOUND_TYPE sound = 3;
  EBT_ATTENTION_UPDATE_TYPE type = 4;
  string value = 5;
  bool isDestroy = 6;
  ETB_ABILITY_CALCULATE_TYPE calculateType_chips = 7;
  ETB_ABILITY_CALCULATE_TYPE calculateType_mult = 8;
  ETB_ABILITY_CALCULATE_TYPE calculateType_dollars = 9;
  EvaluateResult_PROTO evaluateResult = 10;
}

message InGameMissionInfo_PROTO{
  int32 MissionID = 1;
  InGameMisstionReward_PROTO rewardData = 2;
  int32 count = 3;
  bool isClear = 4;
  int32 round = 5;
}

message InGameMisstionReward_PROTO{
  ETB_IN_GAME_MISSION_REWARD_TYPE type = 1;
  int32 item_idx = 2;
  int32 count = 3;
  int32 rate = 4;
}

message Abilitiable_PROTO {
  repeated InGameAbility_PROTO abilities = 1;
  bool isAbilityCopyCompact = 2;
  InGameCardLabel_PROTO label = 3;
}

message InGameJoker_PROTO {
  repeated InGameAbility_PROTO abilities = 1;
  int32 jokerDataIndex = 2;
  bool isBack = 3;
  int32 userDataLv = 4;
}

message InGameCardLabel_PROTO {
  repeated InGameAbility_PROTO abilities = 1;
  int32 labelDataIndex = 2;
}

message InGameConsumeItem_PROTO {
  repeated InGameAbility_PROTO abilities = 1;
  int32 consumeItemDataIndex = 2;
}

message InGameScrollItem_PROTO {
  repeated InGameAbility_PROTO abilities = 1;
  int32 scrollItemDataIndex = 2;
}

message InGameUserStatusRandomAbility_PROTO {
  repeated InGameAbility_PROTO abilities = 1;
  int32 statusAbilityDataIndex = 2;
}

message InGameUserStatusLevelAbility_PROTO {
  repeated InGameAbility_PROTO abilities = 1;
  int32 lv = 2;
}

message InGameShopProduct_PROTO {
  ETB_CARD_TYPE itemType = 1;
  Abilitiable_PROTO product = 2;
  int32 itemIndex = 3;
}

message InGameBookMark_PROTO {
  int32 bookmarkDataIndex = 1;
  int32 userDataLv = 2;
  int32 _enhanceLv = 3;
  repeated InGameBookMarkSkill_PROTO _listSkills = 4;
  repeated InGameBookMarkSkill_PROTO _listSkillsPlayed = 5;
}

message InGameBookMarkSkill_PROTO {
  repeated InGameAbility_PROTO abilities = 1;
  int32 bookmarkDataIndex = 2;
  int32 level = 3;
}

message InGameAbility_PROTO {
  int32 abilityDataIndex = 1;
  ETB_ABILITY_CALCULATE_TYPE calculateType = 2;
  ETB_CALCULATOR_AREA calculateArea = 3;
  ETB_IN_GAME_STATE_TURN turnType = 4;
  int32 round = 5;
  repeated InGameAbilityValue_PROTO values = 6;
  repeated InGameAbilityValue_PROTO conditions = 7;
}

message InGameAbilityValue_PROTO {
  string key = 1;
  string type = 2;
  string value = 3;
}

message PokerHandData_PROTO {
  int32 dataIndex = 1;
  ETB_Poker_Hands_Type type = 2;
  int32 lv = 3;
  uint64 chip = 4;
  float multiple = 5;
  int32 playCount = 6;
  int32 throwCount = 7;
  bool isRoundPlay = 8;
  int32 playCount_Round = 9;
  int32 throwCount_Round = 10;
  bool isRoundThrow = 11;
}

message BattleLogQueueWrapper{
	repeated BattleLog_PROTO logs = 1;
}