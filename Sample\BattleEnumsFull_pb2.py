# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: BattleEnumsFull.proto
# Protobuf Python Version: 4.25.6
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x15\x42\x61ttleEnumsFull.proto\x12\x0fgoogle.protobuf*9\n\x0c\x45\x42T_SortType\x12\x15\n\x11\x45\x42T_SORTTYPE_NONE\x10\x00\x12\x08\n\x04HIGH\x10\x01\x12\x08\n\x04TYPE\x10\x02*\xe8\x01\n\x19\x45\x42T_ATTENTION_UPDATE_TYPE\x12\"\n\x1e\x45\x42T_ATTENTION_UPDATE_TYPE_NONE\x10\x00\x12\t\n\x05\x43HIPS\x10\x01\x12\x0b\n\x07X_CHIPS\x10\x02\x12\r\n\tH_X_CHIPS\x10\x03\x12\x08\n\x04MULT\x10\x04\x12\n\n\x06X_MULT\x10\x05\x12\x0c\n\x08H_X_MULT\x10\x06\x12\n\n\x06H_MULT\x10\x07\x12\x0b\n\x07\x44OLLARS\x10\x08\x12\n\n\x06REPEAT\x10\t\x12\t\n\x05JOKER\x10\n\x12\t\n\x05\x45XTRA\x10\x0b\x12!\n\x1d\x45\x42T_ATTENTION_UPDATE_TYPE_MAX\x10\x0c*\x80\x01\n\x19\x45TB_IN_GAME_MISSION_STATE\x12\"\n\x1e\x45TB_IN_GAME_MISSION_STATE_NONE\x10\x00\x12\x14\n\x10MISSION_PROGRESS\x10\x01\x12\x14\n\x10MISSION_COMPLETE\x10\x02\x12\x13\n\x0fREWARD_COMPLETE\x10\x03*\xb9\x02\n\x0e\x45\x42T_SOUND_TYPE\x12\x17\n\x13\x45\x42T_SOUND_TYPE_NONE\x10\x00\x12\x12\n\x0e\x43LICK_POSITIVE\x10\x01\x12\n\n\x06\x41TTACK\x10\x02\x12\x14\n\x10\x41TTACK_OVER_KILL\x10\x03\x12\x0f\n\x0b\x41TTACK_UNIT\x10\x04\x12\x10\n\x0c\x42OSS_ABILITY\x10\x05\x12\n\n\x06\x43\x41RD_1\x10\x06\x12\n\n\x06\x43\x41RD_2\x10\x07\x12\n\n\x06\x43HIP_1\x10\x08\x12\x0e\n\nSHOP_BUY_1\x10\t\x12\x0e\n\nSHOP_BUY_2\x10\n\x12\r\n\tEXPLOSION\x10\x0b\x12\r\n\tGENERIC_1\x10\x0c\x12\n\n\x06MULT_P\x10\r\x12\n\n\x06MULT_M\x10\x0e\x12\x19\n\x15\x45\x42T_SOUND_TYPE_REPEAT\x10\x0f\x12\x0f\n\x0bREPEAT_UNIT\x10\x10\x12\x0f\n\x0bSHOP_REROLL\x10\x11*\xc2\x11\n\x19\x45\x42T_BATTLE_LOG_EVENT_TYPE\x12\"\n\x1e\x45\x42T_BATTLE_LOG_EVENT_TYPE_NONE\x10\x00\x12\x34\n0EBT_BATTLE_LOG_EVENT_TYPE_PLAYING_CARD_DECK_INIT\x10\x01\x12-\n)EBT_BATTLE_LOG_EVENT_TYPE_RESET_PLAY_DECK\x10\x02\x12\x37\n3EBT_BATTLE_LOG_EVENT_TYPE_PLAYING_CARD_DECK_SETTING\x10\x03\x12\'\n#EBT_BATTLE_LOG_EVENT_TYPE_DRAW_CARD\x10\x04\x12\'\n#EBT_BATTLE_LOG_EVENT_TYPE_BUY_JOKER\x10\x05\x12(\n$EBT_BATTLE_LOG_EVENT_TYPE_SELL_JOKER\x10\x06\x12.\n*EBT_BATTLE_LOG_EVENT_TYPE_BUY_CONSUME_ITEM\x10\x07\x12/\n+EBT_BATTLE_LOG_EVENT_TYPE_SELL_CONSUME_ITEM\x10\x08\x12-\n)EBT_BATTLE_LOG_EVENT_TYPE_BUY_SCROLL_ITEM\x10\t\x12.\n*EBT_BATTLE_LOG_EVENT_TYPE_USE_CONSUME_ITEM\x10\n\x12(\n$EBT_BATTLE_LOG_EVENT_TYPE_USE_SCROLL\x10\x0b\x12\'\n#EBT_BATTLE_LOG_EVENT_TYPE_CARD_EVAL\x10\x0c\x12\'\n#EBT_BATTLE_LOG_EVENT_TYPE_PLAY_HAND\x10\r\x12\x31\n-EBT_BATTLE_LOG_EVENT_TYPE_UPDATE_PLAYING_CARD\x10\x0e\x12\x36\n2EBT_BATTLE_LOG_EVENT_TYPE_UPDATE_PLAYING_FULL_DECK\x10\x0f\x12(\n$EBT_BATTLE_LOG_EVENT_TYPE_THROW_CARD\x10\x10\x12\x36\n2EBT_BATTLE_LOG_EVENT_TYPE_PLAY_HAND_BEFORE_SETTING\x10\x11\x12\x38\n4EBT_BATTLE_LOG_EVENT_TYPE_PLAY_HAND_AFTER_CLEAR_DATA\x10\x12\x12\x32\n.EBT_BATTLE_LOG_EVENT_TYPE_CALCULATE_MONSTER_HP\x10\x13\x12*\n&EBT_BATTLE_LOG_EVENT_TYPE_BOSS_ABILITY\x10\x14\x12)\n%EBT_BATTLE_LOG_EVENT_TYPE_ROUND_CLEAR\x10\x15\x12.\n*EBT_BATTLE_LOG_EVENT_TYPE_ROUND_CLEAR_COIN\x10\x16\x12)\n%EBT_BATTLE_LOG_EVENT_TYPE_STAGE_CLEAR\x10\x17\x12*\n&EBT_BATTLE_LOG_EVENT_TYPE_BONUS_ATTACK\x10\x18\x12*\n&EBT_BATTLE_LOG_EVENT_TYPE_MONSTER_INIT\x10\x19\x12\x32\n.EBT_BATTLE_LOG_EVENT_TYPE_MONSTER_INIT_ABILITY\x10\x1a\x12\'\n#EBT_BATTLE_LOG_EVENT_TYPE_OVER_KILL\x10\x1b\x12\'\n#EBT_BATTLE_LOG_EVENT_TYPE_SHOP_OPEN\x10\x1c\x12\x31\n-EBT_BATTLE_LOG_EVENT_TYPE_SHOP_PRODUCT_REROLL\x10\x1d\x12&\n\"EBT_BATTLE_LOG_EVENT_TYPE_SFX_PLAY\x10\x1e\x12(\n$EBT_BATTLE_LOG_EVENT_TYPE_TIME_DELAY\x10\x1f\x12)\n%EBT_BATTLE_LOG_EVENT_TYPE_CARD_UNPACK\x10 \x12\x33\n/EBT_BATTLE_LOG_EVENT_TYPE_ROUND_CLEAR_CHALLENGE\x10!\x12/\n+EBT_BATTLE_LOG_EVENT_TYPE_DESTROY_PLAY_CARD\x10\"\x12\x34\n0EBT_BATTLE_LOG_EVENT_TYPE_RESURRECTION_PLAY_CARD\x10#\x12\x38\n4EBT_BATTLE_LOG_EVENT_TYPE_MISSION_REROLL_STAGE_START\x10$\x12,\n(EBT_BATTLE_LOG_EVENT_TYPE_MISSION_REWARD\x10%\x12\x33\n/EBT_BATTLE_LOG_EVENT_TYPE_MISSION_REWARD_SELECT\x10&\x12,\n(EBT_BATTLE_LOG_EVENT_TYPE_MISSION_REROLL\x10\'\x12\x31\n-EBT_BATTLE_LOG_EVENT_TYPE_MISSION_REWARD_SKIP\x10(\x12+\n\'EBT_BATTLE_LOG_EVENT_TYPE_ADD_NEW_JOKER\x10)\x12\x32\n.EBT_BATTLE_LOG_EVENT_TYPE_ADD_NEW_CONSUME_ITEM\x10*\x12*\n&EBT_BATTLE_LOG_EVENT_TYPE_REMOVE_JOKER\x10+\x12\x31\n-EBT_BATTLE_LOG_EVENT_TYPE_REMOVE_CONSUME_ITEM\x10,\x12)\n%EBT_BATTLE_LOG_EVENT_TYPE_ROUND_START\x10-\x12+\n\'EBT_BATTLE_LOG_EVENT_TYPE_SHOP_LEVEL_UP\x10.*\xc5\x03\n\x1b\x45\x42T_BATTLE_LOG_PAYLOAD_TYPE\x12\x08\n\x04None\x10\x00\x12\x07\n\x03Int\x10\x01\x12\x08\n\x04\x42ool\x10\x02\x12\n\n\x06String\x10\x03\x12\x0f\n\x0b\x41\x62ilityType\x10\x04\x12\x12\n\x0ePokerHandsType\x10\x05\x12\x15\n\x11MissionRewardType\x10\x06\x12\x08\n\x04\x43\x61rd\x10\x07\x12\x0c\n\x08\x43\x61rdList\x10\x08\x12\x0b\n\x07IntList\x10\t\x12\x11\n\rCardIndexList\x10\n\x12\x14\n\x10GameInfoSnapshot\x10\x0b\x12\x12\n\x0e\x45valuateResult\x10\x0c\x12\x12\n\x0e\x45valuateConfig\x10\r\x12\t\n\x05\x46loat\x10\x0e\x12\n\n\x06\x44ouble\x10\x0f\x12\x0c\n\x08GameInfo\x10\x10\x12\x13\n\x0f\x45valuateContext\x10\x11\x12\x10\n\x0cOverKillType\x10\x12\x12\t\n\x05ULong\x10\x13\x12\x14\n\x10StringDictionary\x10\x14\x12\x18\n\x14StringDictionaryList\x10\x15\x12\x0f\n\x0bProductList\x10\x16\x12\x0f\n\x0bInGameJoker\x10\x17\x12\x15\n\x11InGameConsumeItem\x10\x18\x12\x0b\n\x07Product\x10\x19*z\n\nETB_SFX_UI\x12\x13\n\x0f\x45TB_SFX_UI_NONE\x10\x00\x12\x10\n\x0c\x43LICK_NORMAL\x10\x01\x12\x1d\n\x19\x45TB_SFX_UI_CLICK_POSITIVE\x10\x02\x12\x12\n\x0e\x43LICK_NEGATIVE\x10\x03\x12\x12\n\x0e\x43LICK_PURCHASE\x10\x04*n\n\x0e\x45TB_Trump_Type\x12\x17\n\x13\x45TB_TRUMP_TYPE_NONE\x10\x00\x12\t\n\x05SPADE\x10\x01\x12\t\n\x05HEART\x10\x02\x12\x08\n\x04\x43LUB\x10\x03\x12\x0b\n\x07\x44IAMOND\x10\x04\x12\x16\n\x12\x45TB_TRUMP_TYPE_MAX\x10\x05*\x8c\x01\n\rETB_CARD_TYPE\x12\x16\n\x12\x45TB_CARD_TYPE_NONE\x10\x00\x12\x10\n\x0cPLAYING_CARD\x10\x01\x12\x17\n\x13\x45TB_CARD_TYPE_JOKER\x10\x02\x12\x10\n\x0c\x43ONSUME_ITEM\x10\x03\x12\x0f\n\x0bSCROLL_ITEM\x10\x04\x12\x15\n\x11\x45TB_CARD_TYPE_MAX\x10\x05*\x82\x02\n\x14\x45TB_Poker_Hands_Type\x12\x1d\n\x19\x45TB_POKER_HANDS_TYPE_NONE\x10\x00\x12\r\n\tHIGH_CARD\x10\x01\x12\x08\n\x04PAIR\x10\x02\x12\x0c\n\x08TWO_PAIR\x10\x03\x12\n\n\x06TRIPLE\x10\x04\x12\x0c\n\x08STRAIGHT\x10\x05\x12\t\n\x05\x46LUSH\x10\x06\x12\x0e\n\nFULL_HOUSE\x10\x07\x12\r\n\tFOUR_CARD\x10\x08\x12\x12\n\x0eSTRAIGHT_FLUSH\x10\t\x12\r\n\tFIVE_CARD\x10\n\x12\x0f\n\x0b\x46LUSH_HOUSE\x10\x0b\x12\x0e\n\nFLUSH_FIVE\x10\x0c\x12\x1c\n\x18\x45TB_POKER_HANDS_TYPE_MAX\x10\r*c\n\x19\x45TB_STAGE_DIFFICULTY_TYPE\x12\"\n\x1e\x45TB_STAGE_DIFFICULTY_TYPE_NONE\x10\x00\x12\x0c\n\x08TUTORIAL\x10\x01\x12\n\n\x06NORMAL\x10\x02\x12\x08\n\x04HARD\x10\x03*W\n\x10\x45TB_BATTLE_STATE\x12\x19\n\x15\x45TB_BATTLE_STATE_NONE\x10\x00\x12\n\n\x06\x42\x41TTLE\x10\x01\x12\x08\n\x04SHOP\x10\x02\x12\x12\n\x0eMISSION_REWARD\x10\x03*\xd6\x04\n\x16\x45TB_IN_GAME_STATE_TURN\x12\x1f\n\x1b\x45TB_IN_GAME_STATE_TURN_NONE\x10\x00\x12\x0e\n\nGAME_START\x10\x01\x12\x0f\n\x0bROUND_START\x10\x02\x12\x0e\n\nFIRST_DRAW\x10\x03\x12\x17\n\x13PLAYER_OPERATE_TURN\x10\x04\x12\x14\n\x10TURN_START_BEGIN\x10\x05\x12\x14\n\x10TURN_START_AFTER\x10\x06\x12\x16\n\x12JOKER_ENHANCE_TURN\x10\x07\x12\'\n#ETB_IN_GAME_STATE_TURN_PLAYING_CARD\x10\x08\x12\r\n\tHAND_CARD\x10\t\x12\r\n\tTOMB_CARD\x10\n\x12\r\n\tITEM_SLOT\x10\x0b\x12\x12\n\x0eTURN_END_BEGIN\x10\x0c\x12\x12\n\x0eTURN_END_AFTER\x10\r\x12\x0f\n\x0bTURN_ATTACK\x10\x0e\x12\x0e\n\nCARD_THROW\x10\x0f\x12\r\n\tCARD_DRAW\x10\x10\x12\r\n\tROUND_END\x10\x11\x12+\n\'ETB_IN_GAME_STATE_TURN_USE_CONSUME_ITEM\x10\x12\x12%\n!ETB_IN_GAME_STATE_TURN_USE_SCROLL\x10\x13\x12\x17\n\x13\x44\x45STROYED_PLAY_CARD\x10\x14\x12\r\n\tOVER_KILL\x10\x15\x12\x15\n\x11\x43\x41RD_RESURRECTION\x10\x16\x12\x1d\n\x19\x42OOKMARK_LEVEL_BONUS_TURN\x10\x17\x12\x1e\n\x1a\x45TB_IN_GAME_STATE_TURN_MAX\x10\x18*}\n\x1a\x45TB_ABILITY_CALCULATE_TYPE\x12#\n\x1f\x45TB_ABILITY_CALCULATE_TYPE_NONE\x10\x00\x12\x08\n\x04PLUS\x10\x01\x12\x0c\n\x08MULTIPLY\x10\x02\x12\"\n\x1e\x45TB_ABILITY_CALCULATE_TYPE_MAX\x10\x03*\xd7\x01\n\x13\x45TB_CALCULATOR_AREA\x12\x1c\n\x18\x45TB_CALCULATOR_AREA_NONE\x10\x00\x12\x08\n\x04PLAY\x10\x01\x12\x08\n\x04HAND\x10\x02\x12\x1d\n\x19\x45TB_CALCULATOR_AREA_JOKER\x10\x03\x12\x0c\n\x08\x42OOKMARK\x10\x04\x12!\n\x1d\x45TB_CALCULATOR_AREA_ITEM_SLOT\x10\x05\x12!\n\x1d\x45TB_CALCULATOR_AREA_TOMB_CARD\x10\x06\x12\x1b\n\x17\x45TB_CALCULATOR_AREA_MAX\x10\x07*?\n\x19\x45TB_INGAME_SHOP_ITEM_TYPE\x12\"\n\x1e\x45TB_INGAME_SHOP_ITEM_TYPE_NONE\x10\x00*\xdd\x01\n\x10\x45TB_ENHANCE_TYPE\x12\x19\n\x15\x45TB_ENHANCE_TYPE_NONE\x10\x00\x12\t\n\x05WILDE\x10\x01\x12\t\n\x05\x42ONUS\x10\x02\x12\n\n\x06MULTIP\x10\x03\x12\t\n\x05GLASS\x10\x04\x12\t\n\x05STEEL\x10\x05\x12\t\n\x05STONE\x10\x06\x12\x08\n\x04GOLD\x10\x07\x12\t\n\x05LUCKY\x10\x08\x12\x0e\n\nYELLO_SEAL\x10\t\x12\x0c\n\x08RED_SEAL\x10\n\x12\r\n\tBLUE_SEAL\x10\x0b\x12\x0f\n\x0bPURPLE_SEAL\x10\x0c\x12\x18\n\x14\x45TB_ENHANCE_TYPE_MAX\x10\r*y\n\x16\x45TB_ENHANCE_COLOR_TYPE\x12\x1f\n\x1b\x45TB_ENHANCE_COLOR_TYPE_NONE\x10\x00\x12\x07\n\x03RED\x10\x01\x12\x08\n\x04\x42LUE\x10\x02\x12\t\n\x05GREEN\x10\x03\x12\x08\n\x04GRAY\x10\x04\x12\n\n\x06PURPLE\x10\x05\x12\n\n\x06YELLOW\x10\x06*S\n\x10\x45TB_CHAPTER_TYPE\x12\x19\n\x15\x45TB_CHAPTER_TYPE_NONE\x10\x00\x12\x0c\n\x08SCENARIO\x10\x01\x12\x0b\n\x07SPECIAL\x10\x02\x12\t\n\x05\x45VENT\x10\x03*\xbd\x01\n\x0f\x45TB_RARITY_TYPE\x12\x18\n\x14\x45TB_RARITY_TYPE_NONE\x10\x00\x12\n\n\x06TIER_1\x10\x01\x12\n\n\x06TIER_2\x10\x02\x12\n\n\x06TIER_3\x10\x03\x12\n\n\x06TIER_4\x10\x04\x12\n\n\x06TIER_5\x10\x05\x12\n\n\x06TIER_6\x10\x06\x12\n\n\x06TIER_7\x10\x07\x12\n\n\x06TIER_8\x10\x08\x12\n\n\x06TIER_9\x10\t\x12\x0b\n\x07TIER_10\x10\n\x12\x17\n\x13\x45TB_RARITY_TYPE_MAX\x10\x0b*\x8b\x02\n\x15\x45TB_JOKER_RARITY_TYPE\x12\x1e\n\x1a\x45TB_JOKER_RARITY_TYPE_NONE\x10\x00\x12\x10\n\x0cJOKER_TIER_1\x10\x01\x12\x10\n\x0cJOKER_TIER_2\x10\x02\x12\x10\n\x0cJOKER_TIER_3\x10\x03\x12\x10\n\x0cJOKER_TIER_4\x10\x04\x12\x10\n\x0cJOKER_TIER_5\x10\x05\x12\x10\n\x0cJOKER_TIER_6\x10\x06\x12\x10\n\x0cJOKER_TIER_7\x10\x07\x12\x10\n\x0cJOKER_TIER_8\x10\x08\x12\x10\n\x0cJOKER_TIER_9\x10\t\x12\x11\n\rJOKER_TIER_10\x10\n\x12\x1d\n\x19\x45TB_JOKER_RARITY_TYPE_MAX\x10\x0b*\xfe\x01\n\x14\x45TB_ITEM_RARITY_TYPE\x12\x1d\n\x19\x45TB_ITEM_RARITY_TYPE_NONE\x10\x00\x12\x0f\n\x0bITEM_TIER_1\x10\x01\x12\x0f\n\x0bITEM_TIER_2\x10\x02\x12\x0f\n\x0bITEM_TIER_3\x10\x03\x12\x0f\n\x0bITEM_TIER_4\x10\x04\x12\x0f\n\x0bITEM_TIER_5\x10\x05\x12\x0f\n\x0bITEM_TIER_6\x10\x06\x12\x0f\n\x0bITEM_TIER_7\x10\x07\x12\x0f\n\x0bITEM_TIER_8\x10\x08\x12\x0f\n\x0bITEM_TIER_9\x10\t\x12\x10\n\x0cITEM_TIER_10\x10\n\x12\x1c\n\x18\x45TB_ITEM_RARITY_TYPE_MAX\x10\x0b*\x98\x02\n\x16\x45TB_SCROLL_RARITY_TYPE\x12\x1f\n\x1b\x45TB_SCROLL_RARITY_TYPE_NONE\x10\x00\x12\x11\n\rSCROLL_TIER_1\x10\x01\x12\x11\n\rSCROLL_TIER_2\x10\x02\x12\x11\n\rSCROLL_TIER_3\x10\x03\x12\x11\n\rSCROLL_TIER_4\x10\x04\x12\x11\n\rSCROLL_TIER_5\x10\x05\x12\x11\n\rSCROLL_TIER_6\x10\x06\x12\x11\n\rSCROLL_TIER_7\x10\x07\x12\x11\n\rSCROLL_TIER_8\x10\x08\x12\x11\n\rSCROLL_TIER_9\x10\t\x12\x12\n\x0eSCROLL_TIER_10\x10\n\x12\x1e\n\x1a\x45TB_SCROLL_RARITY_TYPE_MAX\x10\x0b*\x93\x01\n\x15\x45TB_OUTGAME_ITEM_TYPE\x12\x1e\n\x1a\x45TB_OUTGAME_ITEM_TYPE_NONE\x10\x00\x12\x08\n\x04ITEM\x10\x01\x12\x0e\n\nREWARD_BOX\x10\x02\x12\x0f\n\x0bJOKER_PIECE\x10\x03\x12\x14\n\x10POKER_HAND_PIECE\x10\x04\x12\r\n\tBOOK_MARK\x10\x05\x12\n\n\x06SYSTEM\x10\x06*S\n\x10\x45TB_MONSTER_TYPE\x12\x19\n\x15\x45TB_MONSTER_TYPE_NONE\x10\x00\x12\x0b\n\x07MONSTER\x10\x01\x12\x08\n\x04\x42OSS\x10\x02\x12\r\n\tBOSS_HARD\x10\x03*g\n\rETB_GAME_MODE\x12\x16\n\x12\x45TB_GAME_MODE_NONE\x10\x00\x12\x18\n\x14\x45TB_GAME_MODE_NORMAL\x10\x01\x12\x08\n\x04RANK\x10\x02\x12\x1a\n\x16\x45TB_GAME_MODE_TUTORIAL\x10\x63*r\n\x14\x45TB_SHOP_PERIOD_TYPE\x12\x1d\n\x19\x45TB_SHOP_PERIOD_TYPE_NONE\x10\x00\x12\x0b\n\x07ONETIME\x10\x01\x12\t\n\x05\x44\x41ILY\x10\x02\x12\n\n\x06WEEKLY\x10\x03\x12\x0b\n\x07MONTHLY\x10\x04\x12\n\n\x06PERIOD\x10\x05*}\n\x15\x45TB_SHOP_MILEAGE_TYPE\x12\x1e\n\x1a\x45TB_SHOP_MILEAGE_TYPE_NONE\x10\x00\x12\x0c\n\x08MILEAGE1\x10\x01\x12\x13\n\x0fPERIOD_MILEAGE1\x10\x02\x12\x0c\n\x08MILEAGE2\x10\x03\x12\x13\n\x0fPERIOD_MILEAGE2\x10\x04*Y\n\x12\x45TB_SHOP_SALE_TYPE\x12\x1b\n\x17\x45TB_SHOP_SALE_TYPE_NONE\x10\x00\x12\x08\n\x04\x43\x41SH\x10\x01\x12\x0c\n\x08\x45XCHANGE\x10\x02\x12\x0e\n\nAD_PRODUCT\x10\x03*\xaf\x01\n\x1a\x45TB_OUTGAME_ITEM_GET_PLACE\x12#\n\x1f\x45TB_OUTGAME_ITEM_GET_PLACE_NONE\x10\x00\x12#\n\x1f\x45TB_OUTGAME_ITEM_GET_PLACE_SHOP\x10\x01\x12\x0b\n\x07RANKING\x10\x02\x12\t\n\x05STORY\x10\x03\x12\t\n\x05GACHA\x10\x04\x12$\n ETB_OUTGAME_ITEM_GET_PLACE_EVENT\x10\x05*\xde\x01\n\x19\x45TB_ABILITY_CATEGORY_TYPE\x12\"\n\x1e\x45TB_ABILITY_CATEGORY_TYPE_NONE\x10\x00\x12\x08\n\x04\x43HIP\x10\x01\x12&\n\"ETB_ABILITY_CATEGORY_TYPE_MULTIPLY\x10\x02\x12\"\n\x1e\x45TB_ABILITY_CATEGORY_TYPE_GOLD\x10\x03\x12\x07\n\x03\x45TC\x10\x04\x12\x0e\n\nREPETITION\x10\x05\x12\x0b\n\x07\x44\x45STROY\x10\x06\x12!\n\x1d\x45TB_ABILITY_CATEGORY_TYPE_MAX\x10\x07*\xc5\x01\n\x17\x45TB_TOGGLE_SETTING_TYPE\x12 \n\x1c\x45TB_TOGGLE_SETTING_TYPE_NONE\x10\x00\x12\x15\n\x11\x42OOKMARK_CUTSCENE\x10\x01\x12\x0c\n\x08VIBERATE\x10\x02\x12\x08\n\x04PUSH\x10\x03\x12\x19\n\x15\x42\x41TTLE_BUTTON_REVERSE\x10\x04\x12\x08\n\x04\x43HAT\x10\x05\x12\x13\n\x0f\x41TK_ACTION_SKIP\x10\x06\x12\x1f\n\x1b\x45TB_TOGGLE_SETTING_TYPE_MAX\x10\x07*\xc1\x01\n\rETB_OVER_KILL\x12\x16\n\x12\x45TB_OVER_KILL_NONE\x10\x00\x12\x0b\n\x07LEVEL_1\x10\x01\x12\x0b\n\x07LEVEL_2\x10\x02\x12\x0b\n\x07LEVEL_3\x10\x03\x12\x0b\n\x07LEVEL_4\x10\x04\x12\x0b\n\x07LEVEL_5\x10\x05\x12\x0b\n\x07LEVEL_6\x10\x06\x12\x0b\n\x07LEVEL_7\x10\x07\x12\x0b\n\x07LEVEL_8\x10\x08\x12\x0b\n\x07LEVEL_9\x10\t\x12\x0c\n\x08LEVEL_10\x10\n\x12\x15\n\x11\x45TB_OVER_KILL_MAX\x10\x0b*^\n\x17\x45TB_MISSION_RECORD_TYPE\x12 \n\x1c\x45TB_MISSION_RECORD_TYPE_NONE\x10\x00\x12\x0c\n\x08INCREASE\x10\x01\x12\t\n\x05TOTAL\x10\x02\x12\x08\n\x04\x42\x45ST\x10\x03*\x91\x02\n\x1f\x45TB_IN_GAME_MISSION_REWARD_TYPE\x12(\n$ETB_IN_GAME_MISSION_REWARD_TYPE_NONE\x10\x00\x12\x08\n\x04\x43OIN\x10\x01\x12\x16\n\x12ITEM_CHOICE_TIER_1\x10\x02\x12\x16\n\x12ITEM_CHOICE_TIER_2\x10\x03\x12\x16\n\x12ITEM_CHOICE_TIER_3\x10\x04\x12\x17\n\x13JOKER_CHOICE_TIER_1\x10\x05\x12\x17\n\x13JOKER_CHOICE_TIER_2\x10\x06\x12\x17\n\x13JOKER_CHOICE_TIER_3\x10\x07\x12\'\n#ETB_IN_GAME_MISSION_REWARD_TYPE_MAX\x10\x08*\x99\x05\n\x18\x45TB_IN_GAME_MISSION_TYPE\x12!\n\x1d\x45TB_IN_GAME_MISSION_TYPE_NONE\x10\x00\x12&\n\"ETB_IN_GAME_MISSION_TYPE_HIGH_CARD\x10\x01\x12!\n\x1d\x45TB_IN_GAME_MISSION_TYPE_PAIR\x10\x02\x12%\n!ETB_IN_GAME_MISSION_TYPE_TWO_PAIR\x10\x03\x12#\n\x1f\x45TB_IN_GAME_MISSION_TYPE_TRIPLE\x10\x04\x12%\n!ETB_IN_GAME_MISSION_TYPE_STRAIGHT\x10\x05\x12\"\n\x1e\x45TB_IN_GAME_MISSION_TYPE_FLUSH\x10\x06\x12\'\n#ETB_IN_GAME_MISSION_TYPE_FULL_HOUSE\x10\x07\x12&\n\"ETB_IN_GAME_MISSION_TYPE_FOUR_CARD\x10\x08\x12+\n\'ETB_IN_GAME_MISSION_TYPE_STRAIGHT_FLUSH\x10\t\x12&\n\"ETB_IN_GAME_MISSION_TYPE_FIVE_CARD\x10\n\x12(\n$ETB_IN_GAME_MISSION_TYPE_FLUSH_HOUSE\x10\x0b\x12\'\n#ETB_IN_GAME_MISSION_TYPE_FLUSH_FIVE\x10\x0c\x12\x0f\n\x0bOVER_KILL_3\x10\r\x12\x0f\n\x0bOVER_KILL_5\x10\x0e\x12\x0f\n\x0bOVER_KILL_7\x10\x0f\x12\x10\n\x0cOVER_KILL_10\x10\x10\x12\x18\n\x14NO_THROW_ROUND_CLEAR\x10\x11\x12 \n\x1c\x45TB_IN_GAME_MISSION_TYPE_MAX\x10\x12*P\n\x0f\x45TB_REPORT_TYPE\x12\x18\n\x14\x45TB_REPORT_TYPE_NONE\x10\x00\x12\x0b\n\x07\x41\x42USIVE\x10\x01\x12\x08\n\x04SPAM\x10\x02\x12\x0c\n\x08\x43ONFLICT\x10\x03*\x80\x01\n\x14\x45TB_TIMING_PACK_TYPE\x12\x1d\n\x19\x45TB_TIMING_PACK_TYPE_NONE\x10\x00\x12!\n\x1d\x45TB_TIMING_PACK_TYPE_BOOKMARK\x10\x01\x12\t\n\x05STAGE\x10\x02\x12\x0b\n\x07USER_LV\x10\x03\x12\x0e\n\nORTALAB_LV\x10\x04*\xd2\x62\n\x10\x45TB_ABILITY_TYPE\x12\x19\n\x15\x45TB_ABILITY_TYPE_NONE\x10\x00\x12\x1d\n\x19\x45TB_ABILITY_TYPE_MULTIPLY\x10\x01\x12\x19\n\x15\x45TB_ABILITY_TYPE_CHIP\x10\x02\x12(\n$ETB_ABILITY_TYPE_MULTIPLY_PLAY_TRUMP\x10\x03\x12$\n ETB_ABILITY_TYPE_CHIP_PLAY_TRUMP\x10\x04\x12\x30\n,ETB_ABILITY_TYPE_MULTIPLY_INCLUDE_POKER_HAND\x10\x05\x12,\n(ETB_ABILITY_TYPE_CHIP_INCLUDE_POKER_HAND\x10\x06\x12\x1a\n\x16\x45TB_ABILITY_TYPE_LOANS\x10\x07\x12,\n(ETB_ABILITY_TYPE_CHIP_REMAIN_THROW_COUNT\x10\x08\x12&\n\"ETB_ABILITY_TYPE_ROUND_END_DESTROY\x10\t\x12\x1f\n\x1b\x45TB_ABILITY_TYPE_COPY_RIGHT\x10\n\x12/\n+ETB_ABILITY_TYPE_MULTIPLY_PLAYED_CARD_COUNT\x10\x0b\x12+\n\'ETB_ABILITY_TYPE_CHIP_PLAYED_CARD_COUNT\x10\x0c\x12*\n&ETB_ABILITY_TYPE_MULTIPLY_TURN_COUNT_N\x10\r\x12$\n ETB_ABILITY_TYPE_MULTIPLY_RANDOM\x10\x0e\x12\x30\n,ETB_ABILITY_TYPE_MULTIPLY_PLAY_PLAYCARD_RANK\x10\x0f\x12,\n(ETB_ABILITY_TYPE_CHIP_PLAY_PLAYCARD_RANK\x10\x10\x12\'\n#ETB_ABILITY_TYPE_ALL_COUNT_PLAYCARD\x10\x11\x12\x30\n,ETB_ABILITY_TYPE_MULTIPLY_HAND_PLAYCARD_RANK\x10\x12\x12,\n(ETB_ABILITY_TYPE_CHIP_HAND_PLAYCARD_RANK\x10\x13\x12/\n+ETB_ABILITY_TYPE_MULTIPLY_DECK_ENHANCE_CARD\x10\x14\x12,\n(ETB_ABILITY_TYPE_CHIP_DECK_ENHANCE_BONUS\x10\x15\x12=\n9ETB_ABILITY_TYPE_MULTIPLY_DECK_OVER_COUNT_4_PLAYCARD_RANK\x10\x16\x12\x39\n5ETB_ABILITY_TYPE_CHIP_DECK_OVER_COUNT_4_PLAYCARD_RANK\x10\x17\x12(\n$ETB_ABILITY_TYPE_GET_GOLD_PLAY_TRUMP\x10\x18\x12-\n)ETB_ABILITY_TYPE_MULTIPLY_PLAY_TRUMP_RATE\x10\x19\x12(\n$ETB_ABILITY_TYPE_REGARD_AS_FACE_TYPE\x10\x1a\x12*\n&ETB_ABILITY_TYPE_REGARD_AS_NUMBER_TYPE\x10\x1b\x12\x37\n3ETB_ABILITY_TYPE_MULTIPLY_INCREASE_NONE_PLAY_3_CARD\x10\x1c\x12;\n7ETB_ABILITY_TYPE_GET_GOLD_THROW_ONCE_N_FACE_NUMBER_TYPE\x10\x1d\x12=\n9ETB_ABILITY_TYPE_MULTIPLY_FIRST_PLAYCARD_FACE_NUMBER_TYPE\x10\x1e\x12%\n!ETB_ABILITY_TYPE_SHOP_REROLL_FREE\x10\x1f\x12\x32\n.ETB_ABILITY_TYPE_MULTIPLY_INCREASE_SHOP_REROLL\x10 \x12$\n ETB_ABILITY_TYPE_CHIP_DECK_COUNT\x10!\x12\x34\n0ETB_ABILITY_TYPE_MULTIPLY_HAND_ROW_RANK_MULTIPLE\x10\"\x12.\n*ETB_ABILITY_TYPE_MULTIPLY_HAVE_JOKER_COUNT\x10#\x12\x33\n/ETB_ABILITY_TYPE_MULTIPLY_POKER_HAND_PLAY_COUNT\x10$\x12&\n\"ETB_ABILITY_TYPE_SELL_COST_UP_SELP\x10%\x12;\n7ETB_ABILITY_TYPE_GET_GOLD_PLAY_RANDOM_TARGET_POKER_HAND\x10&\x12-\n)ETB_ABILITY_TYPE_CREATE_JOKER_ROUND_START\x10\'\x12\x38\n4ETB_ABILITY_TYPE_GET_GOLD_TARGET_BY_RANDOM_RANK_CARD\x10(\x12+\n\'ETB_ABILITY_TYPE_MULTIPLY_INCREASE_TURN\x10)\x12\'\n#ETB_ABILITY_TYPE_CHIP_INCREASE_TURN\x10*\x12)\n%ETB_ABILITY_TYPE_ADD_GOLD_ROUND_CLEAR\x10+\x12+\n\'ETB_ABILITY_TYPE_MULTIPLY_DECREASE_TURN\x10,\x12/\n+ETB_ABILITY_TYPE_GET_GOLD_PLAY_ENHANCE_CARD\x10-\x12\x36\n2ETB_ABILITY_TYPE_MULTIPLY_DIRECTION_JOKER_ALL_COST\x10.\x12\x30\n,ETB_ABILITY_TYPE_MORE_TRIGGER_FIRST_PLAYCARD\x10/\x12.\n*ETB_ABILITY_TYPE_MULTIPLY_JOKER_EMPTY_SLOT\x10\x30\x12\x34\n0ETB_ABILITY_TYPE_FLUSH_STRAIGHT_CONDITION_4_CARD\x10\x31\x12&\n\"ETB_ABILITY_TYPE_MORE_TRIGGER_HAND\x10\x32\x12+\n\'ETB_ABILITY_TYPE_MORE_TRIGGER_LAST_TURN\x10\x33\x12/\n+ETB_ABILITY_TYPE_MORE_TRIGGER_PLAYCARD_RANK\x10\x34\x12\x32\n.ETB_ABILITY_TYPE_PLAY_POKER_HAND_RATE_LEVEL_UP\x10\x35\x12\x34\n0ETB_ABILITY_TYPE_MULTIPLY_DECK_CARD_2_TRUMP_TYPE\x10\x36\x12\x30\n,ETB_ABILITY_TYPE_USED_PLAYCARD_CHIP_RATE_ADD\x10\x37\x12/\n+ETB_ABILITY_TYPE_MULTIPLY_PLAYED_POKER_HAND\x10\x38\x12\x31\n-ETB_ABILITY_TYPE_MULTIPLY_ENHANCE_CARD_ABSORB\x10\x39\x12-\n)ETB_ABILITY_TYPE_STRAIGHT_CONDITION_1_GAP\x10:\x12,\n(ETB_ABILITY_TYPE_GET_GOLD_ROUND_INCREASE\x10;\x12\x36\n2ETB_ABILITY_TYPE_GET_GOLD_DECK_PLAYCARD_RANK_COUNT\x10<\x12,\n(ETB_ABILITY_TYPE_PLAYCARD_CHANGE_ENHANCE\x10=\x12#\n\x1f\x45TB_ABILITY_TYPE_BLIND_BAN_SELL\x10>\x12\'\n#ETB_ABILITY_TYPE_HAVE_JOKER_COST_UP\x10?\x12,\n(ETB_ABILITY_TYPE_HANDCOUNT_DECREASE_TURN\x10@\x12\x30\n,ETB_ABILITY_TYPE_MULTIPLY_DECK_UNDER_COUNT_N\x10\x41\x12&\n\"ETB_ABILITY_TYPE_GET_GOLD_INTEREST\x10\x42\x12\x33\n/ETB_ABILITY_TYPE_MULTIPLY_INCREASE_USED_ENHANCE\x10\x43\x12&\n\"ETB_ABILITY_TYPE_CHIP_HOLDING_GOLD\x10\x44\x12\x36\n2ETB_ABILITY_TYPE_GET_GOLD_THROW_CARD_COUNT_DESTROY\x10\x45\x12\x36\n2ETB_ABILITY_TYPE_MULTIPLY_INCREASE_POKERHAND_COUNT\x10\x46\x12\x32\n.ETB_ABILITY_TYPE_MULTIPLY_THROW_COUNT_DECREASE\x10G\x12/\n+ETB_ABILITY_TYPE_MORE_TRIGGER_ALL_COUNTDOWN\x10H\x12<\n8ETB_ABILITY_TYPE_CHIP_INCREASE_RANDOM_TARGET_TRUMP_THROW\x10I\x12\x35\n1ETB_ABILITY_TYPE_ATTACK_COUNT_LAST_ATTACK_DESTROY\x10J\x12*\n&ETB_ABILITY_TYPE_MULTIPLY_ATTACK_COUNT\x10K\x12\x37\n3ETB_ABILITY_TYPE_CREATE_ROUND_START_RANDOM_PLAYCARD\x10L\x12$\n ETB_ABILITY_TYPE_REGARD_AS_TRUMP\x10M\x12\x33\n/ETB_ABILITY_TYPE_MULTIPLY_ENHANCE_DESTROY_COUNT\x10N\x12\x1c\n\x18\x45TB_ABILITY_TYPE_RATE_UP\x10O\x12>\n:ETB_ABILITY_TYPE_MULTIPLY_INCREASE_RANDOM_TARGET_PLAY_CARD\x10P\x12\x32\n.ETB_ABILITY_TYPE_MULTIPLY_INCLUDING_TRUMP_TYPE\x10Q\x12\x32\n.ETB_ABILITY_TYPE_GET_GOLD_PLAY_MONSTER_PENALTY\x10R\x12,\n(ETB_ABILITY_TYPE_CREATE_TURN_RANDOM_ITEM\x10S\x12+\n\'ETB_ABILITY_TYPE_SHOP_ROUND_ITEM_FREE_N\x10T\x12\x34\n0ETB_ABILITY_TYPE_FIRST_THROW_POKER_HAND_LEVEL_UP\x10U\x12-\n)ETB_ABILITY_TYPE_FIRST_PLAY_ONE_CARD_COPY\x10V\x12*\n&ETB_ABILITY_TYPE_MULTIPLY_HOLDING_GOLD\x10W\x12=\n9ETB_ABILITY_TYPE_TURN_PLAYCARD_DESTROY_CREATE_RANDOM_ITEM\x10X\x12\x32\n.ETB_ABILITY_TYPE_POKER_HAND_CREATE_RANDOM_ITEM\x10Y\x12@\n<ETB_ABILITY_TYPE_MULTIPLY_INCREASE_NONE_PLAY_MOST_POKER_HAND\x10Z\x12*\n&ETB_ABILITY_TYPE_MULTIPLY_JOKER_RARITY\x10[\x12;\n7ETB_ABILITY_TYPE_MULTIPLY_RANDOM_TARGET_TRUMP_PLAY_CARD\x10\\\x12\x31\n-ETB_ABILITY_TYPE_MULTIPLY_INCREASE_SELL_JOKER\x10]\x12<\n8ETB_ABILITY_TYPE_MULTIPLY_THROW_RANDOM_TARGET_RANK_RESET\x10^\x12:\n6ETB_ABILITY_TYPE_MULTIPLY_HAVE_DECK_COUNT_ENHANCE_CARD\x10_\x12\x32\n.ETB_ABILITY_TYPE_CHIP_INCREASE_PLAY_POKER_HAND\x10`\x12\'\n#ETB_ABILITY_TYPE_CHIP_DECREASE_TURN\x10\x61\x12\x31\n-ETB_ABILITY_TYPE_ITEM_PLAYCARD_CHANGE_ENHANCE\x10\x62\x12*\n&ETB_ABILITY_TYPE_ITEM_PLAYCARD_DESTROY\x10\x63\x12/\n+ETB_ABILITY_TYPE_ITEM_PLAYCARD_CHANGE_TRUMP\x10\x64\x12\x32\n.ETB_ABILITY_TYPE_ITEM_CREATE_JOKER_RANDOM_CARD\x10\x65\x12,\n(ETB_ABILITY_TYPE_ITEM_POKER_HAND_UPGRADE\x10\x66\x12;\n7ETB_ABILITY_TYPE_ITEM_PLAYCARD_CHANGE_RAND_ONE_TYPE_ALL\x10g\x12=\n9ETB_ABILITY_TYPE_ITEM_PLAYCARD_CHANGE_RAND_ONE_NUMBER_ALL\x10h\x12\x1f\n\x1b\x45TB_ABILITY_TYPE_JOKER_SLOT\x10i\x12 \n\x1c\x45TB_ABILITY_TYPE_REROLL_COST\x10j\x12%\n!ETB_ABILITY_TYPE_ATTACK_COUNT_ADD\x10k\x12$\n ETB_ABILITY_TYPE_THROW_COUNT_ADD\x10l\x12\'\n#ETB_ABILITY_TYPE_INTEREST_COUNT_ADD\x10m\x12#\n\x1f\x45TB_ABILITY_TYPE_HAND_COUNT_ADD\x10n\x12-\n)ETB_ABILITY_TYPE_POKER_HAND_ALL_LEVEL_ADD\x10o\x12\'\n#ETB_ABILITY_TYPE_ITEM_CARD_SLOT_ADD\x10p\x12&\n\"ETB_ABILITY_TYPE_SHOP_DISCOUNT_PER\x10q\x12&\n\"ETB_ABILITY_TYPE_DEBUFF_TRUMP_TYPE\x10r\x12\"\n\x1e\x45TB_ABILITY_TYPE_DEBUFF_CARD_N\x10s\x12\x34\n0ETB_ABILITY_TYPE_DEBUFF_BEFORE_ROUND_PLAYED_CARD\x10t\x12+\n\'ETB_ABILITY_TYPE_BLIND_HAND_DRAW_RANDOM\x10u\x12*\n&ETB_ABILITY_TYPE_BLIND_HAND_FIRST_CARD\x10v\x12)\n%ETB_ABILITY_TYPE_BLIND_HAND_DRAW_CARD\x10w\x12$\n ETB_ABILITY_TYPE_BLIND_CARD_RANK\x10x\x12/\n+ETB_ABILITY_TYPE_PLAY_POKER_HAND_LEVEL_DOWN\x10y\x12\x1a\n\x16\x45TB_ABILITY_TYPE_HP_UP\x10z\x12*\n&ETB_ABILITY_TYPE_CALCULATION_REDUCTION\x10{\x12\x35\n1ETB_ABILITY_TYPE_PLAY_MOST_POKER_HAND_PLAY_GOLD_0\x10|\x12\'\n#ETB_ABILITY_TYPE_GET_GOLD_PLAY_CARD\x10}\x12\'\n#ETB_ABILITY_TYPE_LIMIT_ATTACK_COUNT\x10~\x12/\n+ETB_ABILITY_TYPE_PLAY_POKER_HAND_ONE_BY_ONE\x10\x7f\x12.\n)ETB_ABILITY_TYPE_PLAY_POKER_HAND_ONLY_ONE\x10\x80\x01\x12(\n#ETB_ABILITY_TYPE_PLAY_AND_DRAW_CARD\x10\x81\x01\x12\x31\n,ETB_ABILITY_TYPE_PLAY_CARD_THROW_RANDOM_CARD\x10\x82\x01\x12/\n*ETB_ABILITY_TYPE_PLAY_CARD_ONLY_CARD_COUNT\x10\x83\x01\x12\x36\n1ETB_ABILITY_TYPE_PLAY_CARD_ONLY_CARD_COUNT_RANDOM\x10\x84\x01\x12&\n!ETB_ABILITY_TYPE_LIMIT_TROW_COUNT\x10\x85\x01\x12\x32\n-ETB_ABILITY_TYPE_DEBUFF_POKER_HAND_POKER_HAND\x10\x86\x01\x12(\n#ETB_ABILITY_TYPE_GET_GOLD_ATT_COUNT\x10\x87\x01\x12/\n*ETB_ABILITY_TYPE_MULTIPLY_AFTER_TURN_ENTRY\x10\x88\x01\x12\x36\n1ETB_ABILITY_TYPE_MORE_TRIGGER_POKER_HAND_LEVEL_UP\x10\x89\x01\x12.\n)ETB_ABILITY_TYPE_GET_GOLD_TARGET_PLAYCARD\x10\x8a\x01\x12.\n)ETB_ABILITY_TYPE_GET_ITEM_TARGET_PLAYCARD\x10\x8b\x01\x12.\n)ETB_ABILITY_TYPE_MULTIPLY_TARGET_PLAYCARD\x10\x8c\x01\x12*\n%ETB_ABILITY_TYPE_CHIP_TARGET_PLAYCARD\x10\x8d\x01\x12+\n&ETB_ABILITY_TYPE_FIXED_REWARD_INTEREST\x10\x8e\x01\x12!\n\x1c\x45TB_ABILITY_TYPE_FREE_FREEZE\x10\x8f\x01\x12\x36\n1ETB_ABILITY_TYPE_CHANGE_ENHANCE_CARD_BY_FACE_TYPE\x10\x90\x01\x12\x30\n+ETB_ABILITY_TYPE_MULTIPLY_PLAY_ENHANCE_CARD\x10\x91\x01\x12,\n\'ETB_ABILITY_TYPE_CHIP_PLAY_ENHANCE_CARD\x10\x92\x01\x12\x41\n<ETB_ABILITY_TYPE_CONSIDERED_POKER_HAND_LEVEL_BY_ENHANCE_CARD\x10\x93\x01\x12\"\n\x1d\x45TB_ABILITY_TYPE_SELL_COST_UP\x10\x94\x01\x12)\n$ETB_ABILITY_TYPE_SHOP_DISCOUNT_COUNT\x10\x95\x01\x12/\n*ETB_ABILITY_TYPE_MULTIPLY_POKER_HAND_COMBO\x10\x96\x01\x12\x35\n0ETB_ABILITY_TYPE_ALL_TRIGGERBY_TARGET_POKER_HAND\x10\x97\x01\x12-\n(ETB_ABILITY_TYPE_MORE_TRIGGER_TRUMP_TYPE\x10\x98\x01\x12,\n\'ETB_ABILITY_TYPE_ADD_TRUMP_TYPE_TO_DECK\x10\x99\x01\x12/\n*ETB_ABILITY_TYPE_MORE_TRIGGER_ENHANCE_CARD\x10\x9a\x01\x12\x35\n0ETB_ABILITY_TYPE_CREATE_TURN_RANDOM_ENHANCE_CARD\x10\x9b\x01\x12/\n*ETB_ABILITY_TYPE_MULTIPLY_DECK_COUNT_UNDER\x10\x9c\x01\x12,\n\'ETB_ABILITY_TYPE_MULTIPLY_DECK_COUNT_UP\x10\x9d\x01\x12:\n5ETB_ABILITY_TYPE_PLAYCARD_CHANGE_ENHANCE_BY_FACE_TYPE\x10\x9e\x01\x12)\n$ETB_ABILITY_TYPE_CHIP_PLAY_FACE_TYPE\x10\x9f\x01\x12-\n(ETB_ABILITY_TYPE_MULTIPLY_PLAY_FACE_TYPE\x10\xa0\x01\x12\x36\n1ETB_ABILITY_TYPE_MULTIPLY_INCREASE_NONE_FACE_TYPE\x10\xa1\x01\x12,\n\'ETB_ABILITY_TYPE_MORE_TRIGGER_FACE_TYPE\x10\xa2\x01\x12\x32\n-ETB_ABILITY_TYPE_ITEM_PLAYCARD_CHANGE_RANK_UP\x10\xa3\x01\x12.\n)ETB_ABILITY_TYPE_ITEM_PLAYCARD_COPY_RIGHT\x10\xa4\x01\x12;\n6ETB_ABILITY_TYPE_LABEL_NOT_INCLUDED_IN_ANY_AFFILIATION\x10\xa5\x01\x12.\n)ETB_ABILITY_TYPE_LABEL_DEFAULT_CHIP_COVER\x10\xa6\x01\x12*\n%ETB_ABILITY_TYPE_LABEL_ALL_TRUMP_TYPE\x10\xa7\x01\x12%\n ETB_ABILITY_TYPE_LABEL_GOLD_CARD\x10\xa8\x01\x12&\n!ETB_ABILITY_TYPE_LABEL_LUCKY_CARD\x10\xa9\x01\x12$\n\x1f\x45TB_ABILITY_TYPE_LABEL_RED_SEAL\x10\xaa\x01\x12%\n ETB_ABILITY_TYPE_LABEL_BLUE_SEAL\x10\xab\x01\x12\x33\n.ETB_ABILITY_TYPE_SCROLL_CREATE_PLAYCARD_RANDOM\x10\xac\x01\x12=\n8ETB_ABILITY_TYPE_SCROLL_CREATE_PLAYCARD_RANDOM_SAME_CARD\x10\xad\x01\x12G\nBETB_ABILITY_TYPE_SCROLL_CREATE_PLAYCARD_TARGET_TRUMP_RANDOM_NUMBER\x10\xae\x01\x12G\nBETB_ABILITY_TYPE_SCROLL_CREATE_PLAYCARD_TARGET_NUMBER_RANDOM_TRUMP\x10\xaf\x01\x12I\nDETB_ABILITY_TYPE_SCROLL_CREATE_PLAYCARD_TARGET_3_NUMBER_RANDOM_TRUMP\x10\xb0\x01\x12N\nIETB_ABILITY_TYPE_SCROLL_CREATE_PLAYCARD_TARGET_3_NUMBER_RANDOM_SAME_TRUMP\x10\xb1\x01\x12>\n9ETB_ABILITY_TYPE_SCROLL_CREATE_PLAYCARD_TARGET_NUMBER_SET\x10\xb2\x01\x12=\n8ETB_ABILITY_TYPE_SCROLL_CREATE_PLAYCARD_TARGET_TRUMP_SET\x10\xb3\x01\x12\x45\n@ETB_ABILITY_TYPE_SCROLL_CREATE_PLAYCARD_TARGET_LABEL_RANDOM_CARD\x10\xb4\x01\x12G\nBETB_ABILITY_TYPE_SCROLL_CREATE_PLAYCARD_TARGET_LABEL_TARGET_NUMBER\x10\xb5\x01\x12\x46\nAETB_ABILITY_TYPE_SCROLL_CREATE_PLAYCARD_TARGET_LABEL_TARGET_TRUMP\x10\xb6\x01\x12@\n;ETB_ABILITY_TYPE_CREATE_ROUND_START_RANDOM_ENHANCE_PLAYCARD\x10\xb7\x01\x12\x37\n2ETB_ABILITY_TYPE_ITEM_PLAYCARD_CHANGE_ONE_TYPE_ALL\x10\xb8\x01\x12\x39\n4ETB_ABILITY_TYPE_ITEM_PLAYCARD_CHANGE_ONE_NUMBER_ALL\x10\xb9\x01\x12\x1f\n\x1a\x45TB_ABILITY_TYPE_SHOP_SLOT\x10\xba\x01\x12.\n)ETB_ABILITY_TYPE_MULTIPLY_HAVE_ITME_COUNT\x10\xbb\x01\x12*\n%ETB_ABILITY_TYPE_CHIP_HAVE_ITME_COUNT\x10\xbc\x01\x12\'\n\"ETB_ABILITY_TYPE_ADD_THROW_BY_TURN\x10\xbd\x01\x12.\n)ETB_ABILITY_TYPE_NONE_PLAYED_CARD_DESTROY\x10\xbe\x01\x12)\n$ETB_ABILITY_TYPE_MULTIPLY_TOMB_COUNT\x10\xbf\x01\x12%\n ETB_ABILITY_TYPE_CHIP_TOMB_COUNT\x10\xc0\x01\x12\x38\n3ETB_ABILITY_TYPE_DESTROY_CARD_CHANGE_TARGET_ENHANCE\x10\xc1\x01\x12\x38\n3ETB_ABILITY_TYPE_DESTROY_CARD_CHANGE_RANDOM_ENHANCE\x10\xc2\x01\x12#\n\x1e\x45TB_ABILITY_TYPE_GET_GOLD_TURN\x10\xc3\x01\x12\x35\n0ETB_ABILITY_TYPE_CHIP_USER_STATUS_POKER_HAND_ALL\x10\xc4\x01\x12\x31\n,ETB_ABILITY_TYPE_CHIP_USER_STATUS_POKER_HAND\x10\xc5\x01\x12\x39\n4ETB_ABILITY_TYPE_MULTIPLY_USER_STATUS_POKER_HAND_ALL\x10\xc6\x01\x12\x35\n0ETB_ABILITY_TYPE_MULTIPLY_USER_STATUS_POKER_HAND\x10\xc7\x01\x12\x31\n,ETB_ABILITY_TYPE_USER_STATUS_RANDOM_SLOT_ADD\x10\xc8\x01\x12\x31\n,ETB_ABILITY_TYPE_USER_STATUS_PRESET_SLOT_ADD\x10\xc9\x01\x12\x45\n@ETB_ABILITY_TYPE_DEBUFF_MULTIPLY_REDUCTION_MISMATCHED_POKER_HAND\x10\xca\x01\x12\x30\n+ETB_ABILITY_TYPE_DEBUFF_ONLY_OVERKILL_DEATH\x10\xcb\x01\x12\x35\n0ETB_ABILITY_TYPE_DEBUFF_RANDOM_CHANGE_HAND_CARDS\x10\xcc\x01\x12/\n*ETB_ABILITY_TYPE_DEBUFF_PLAY_CARDS_DESTROY\x10\xcd\x01\x12:\n5ETB_ABILITY_TYPE_DEBUFF_CLEAR_ENHANCE_CARD_THROW_RATE\x10\xce\x01\x12\x35\n0ETB_ABILITY_TYPE_DEBUFF_HP_UP_ENHANCE_CARD_COUNT\x10\xcf\x01\x12-\n(ETB_ABILITY_TYPE_DEBUFF_UNDER_CARD_COUNT\x10\xd0\x01\x12\x39\n4ETB_ABILITY_TYPE_DEBUFF_CLEAR_ENHANCE_CARD_PLAY_CARD\x10\xd1\x01\x12;\n6ETB_ABILITY_TYPE_DEBUFF_DESTROY_RANDOM_JOKER_ROUND_END\x10\xd2\x01\x12=\n8ETB_ABILITY_TYPE_DEBUFF_DESTROY_RANDOM_JOKER_THROW_COUNT\x10\xd3\x01\x12>\n9ETB_ABILITY_TYPE_DEBUFF_DESTROY_RANDOM_JOKER_ATTACK_COUNT\x10\xd4\x01\x12-\n(ETB_ABILITY_TYPE_DEBUFF_DECK_COUNT_HP_UP\x10\xd5\x01\x12\x31\n,ETB_ABILITY_TYPE_DEBUFF_DAMAGE_POKER_HAND_LV\x10\xd6\x01\x12)\n$ETB_ABILITY_TYPE_RESURRECTION_RANDOM\x10\xd7\x01\x12/\n*ETB_ABILITY_TYPE_MULTIPLY_TOMB_TRUMP_COUNT\x10\xd8\x01\x12.\n)ETB_ABILITY_TYPE_RESURRECTION_THROW_COUNT\x10\xd9\x01\x12)\n$ETB_ABILITY_TYPE_DESTROY_THROW_COUNT\x10\xda\x01\x12\x1e\n\x19\x45TB_ABILITY_TYPE_OVERKILL\x10\xdb\x01\x12@\n;ETB_ABILITY_TYPE_DESTROY_THROW_COUNT_CREATE_RANDOM_PLAYCARD\x10\xdc\x01\x12\x41\n<ETB_ABILITY_TYPE_MULTIPLY_INCREASE_DESTROY_LEFT_RANDOM_JOKER\x10\xdd\x01\x12,\n\'ETB_ABILITY_TYPE_CREATE_ITEM_UNDER_GOLD\x10\xde\x01\x12.\n)ETB_ABILITY_TYPE_MULTIPLY_PLAY_CARD_COUNT\x10\xdf\x01\x12\x34\n/ETB_ABILITY_TYPE_MULTIPLY_PLAY_CARD_RANK_3_CARD\x10\xe0\x01\x12*\n%ETB_ABILITY_TYPE_MULTIPLY_THROW_COUNT\x10\xe1\x01\x12.\n)ETB_ABILITY_TYPE_MULTIPLY_TOMB_RANK_COUNT\x10\xe2\x01\x12*\n%ETB_ABILITY_TYPE_CHIP_TOMB_RANK_COUNT\x10\xe3\x01\x12\x36\n1ETB_ABILITY_TYPE_NONE_PLAYED_CARD_DESTROY_RATE_UP\x10\xe4\x01\x12\x39\n4ETB_ABILITY_TYPE_CREATE_PLAYCARD_COPY_BY_MIRROR_ITEM\x10\xe5\x01\x12:\n5ETB_ABILITY_TYPE_MULTIPLY_DECK_NOT_HAVE_PLAYCARD_RANK\x10\xe6\x01\x12\x36\n1ETB_ABILITY_TYPE_CHIP_DECK_NOT_HAVE_PLAYCARD_RANK\x10\xe7\x01\x12\x44\n?ETB_ABILITY_TYPE_MULTIPLY_TARGET_CONSUME_ITEM_COMPOUND_INTEREST\x10\xe8\x01\x12\x33\n.ETB_ABILITY_TYPE_HP_PER_DMG_INCLUDE_POKER_HAND\x10\xe9\x01\x12\x33\n.ETB_ABILITY_TYPE_CHANGE_ENHANCE_CARD_ITEM_USED\x10\xea\x01\x12\x34\n/ETB_ABILITY_TYPE_CREATE_TURN_RANDOM_TARGET_ITEM\x10\xeb\x01\x12\x42\n=ETB_ABILITY_TYPE_MULTIPLY_TARGET_CONSUME_ITEM_SIMPLE_INTEREST\x10\xec\x01\x12\x31\n,ETB_ABILITY_TYPE_TARGET_ITEM_SELECT_COUNT_UP\x10\xed\x01\x12&\n!ETB_ABILITY_TYPE_DEBUFF_HANDCOUNT\x10\xee\x01\x12\x43\n>ETB_ABILITY_TYPE_DEBUFF_MULTIPLY_DECREASE_NOT_USE_CONSUME_ITEM\x10\xef\x01\x12@\n;ETB_ABILITY_TYPE_DEBUFF_DESTROY_JOKER_DIRECTION_ROUND_START\x10\xf0\x01\x12\x35\n0ETB_ABILITY_TYPE_DEBUFF_ATTACK_TROW_ALL_HANDCARD\x10\xf1\x01\x12\x34\n/ETB_ABILITY_TYPE_DEBUFF_DECRESE_GOLD_RATE_THROW\x10\xf2\x01\x12\x35\n0ETB_ABILITY_TYPE_DEBUFF_DECRESE_GOLD_RATE_ATTACK\x10\xf3\x01\x12\x31\n,ETB_ABILITY_TYPE_DEBUFF_BAN_JOKER_SELL_JOKER\x10\xf4\x01\x12(\n#ETB_ABILITY_TYPE_DEBUFF_THROW_HP_UP\x10\xf5\x01\x12\x30\n+ETB_ABILITY_TYPE_DEBUFF_DROW_CARD_RANK_DOWN\x10\xf6\x01\x12\x34\n/ETB_ABILITY_TYPE_MULTIPLY_PLAY_CARD_RANK_2_CARD\x10\xf7\x01\x12\x34\n/ETB_ABILITY_TYPE_RESURRECTION_ALL_CURRENT_ROUND\x10\xf8\x01\x12+\n&ETB_ABILITY_TYPE_HP_PER_DMG_TURN_COUNT\x10\xf9\x01\x12\x35\n0ETB_ABILITY_TYPE_MULTIPLY_COMPOUND_INTEREST_TURN\x10\xfa\x01\x12<\n7ETB_ABILITY_TYPE_MULTIPLY_PLAY_CARD_TARGET_INCLUDE_RANK\x10\xfb\x01\x12\x37\n2ETB_ABILITY_TYPE_DEBUFF_MULTIPLY_DECREASE_HAS_COIN\x10\xfc\x01\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'BattleEnumsFull_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  DESCRIPTOR._options = None
  _globals['_EBT_SORTTYPE']._serialized_start=42
  _globals['_EBT_SORTTYPE']._serialized_end=99
  _globals['_EBT_ATTENTION_UPDATE_TYPE']._serialized_start=102
  _globals['_EBT_ATTENTION_UPDATE_TYPE']._serialized_end=334
  _globals['_ETB_IN_GAME_MISSION_STATE']._serialized_start=337
  _globals['_ETB_IN_GAME_MISSION_STATE']._serialized_end=465
  _globals['_EBT_SOUND_TYPE']._serialized_start=468
  _globals['_EBT_SOUND_TYPE']._serialized_end=781
  _globals['_EBT_BATTLE_LOG_EVENT_TYPE']._serialized_start=784
  _globals['_EBT_BATTLE_LOG_EVENT_TYPE']._serialized_end=3026
  _globals['_EBT_BATTLE_LOG_PAYLOAD_TYPE']._serialized_start=3029
  _globals['_EBT_BATTLE_LOG_PAYLOAD_TYPE']._serialized_end=3482
  _globals['_ETB_SFX_UI']._serialized_start=3484
  _globals['_ETB_SFX_UI']._serialized_end=3606
  _globals['_ETB_TRUMP_TYPE']._serialized_start=3608
  _globals['_ETB_TRUMP_TYPE']._serialized_end=3718
  _globals['_ETB_CARD_TYPE']._serialized_start=3721
  _globals['_ETB_CARD_TYPE']._serialized_end=3861
  _globals['_ETB_POKER_HANDS_TYPE']._serialized_start=3864
  _globals['_ETB_POKER_HANDS_TYPE']._serialized_end=4122
  _globals['_ETB_STAGE_DIFFICULTY_TYPE']._serialized_start=4124
  _globals['_ETB_STAGE_DIFFICULTY_TYPE']._serialized_end=4223
  _globals['_ETB_BATTLE_STATE']._serialized_start=4225
  _globals['_ETB_BATTLE_STATE']._serialized_end=4312
  _globals['_ETB_IN_GAME_STATE_TURN']._serialized_start=4315
  _globals['_ETB_IN_GAME_STATE_TURN']._serialized_end=4913
  _globals['_ETB_ABILITY_CALCULATE_TYPE']._serialized_start=4915
  _globals['_ETB_ABILITY_CALCULATE_TYPE']._serialized_end=5040
  _globals['_ETB_CALCULATOR_AREA']._serialized_start=5043
  _globals['_ETB_CALCULATOR_AREA']._serialized_end=5258
  _globals['_ETB_INGAME_SHOP_ITEM_TYPE']._serialized_start=5260
  _globals['_ETB_INGAME_SHOP_ITEM_TYPE']._serialized_end=5323
  _globals['_ETB_ENHANCE_TYPE']._serialized_start=5326
  _globals['_ETB_ENHANCE_TYPE']._serialized_end=5547
  _globals['_ETB_ENHANCE_COLOR_TYPE']._serialized_start=5549
  _globals['_ETB_ENHANCE_COLOR_TYPE']._serialized_end=5670
  _globals['_ETB_CHAPTER_TYPE']._serialized_start=5672
  _globals['_ETB_CHAPTER_TYPE']._serialized_end=5755
  _globals['_ETB_RARITY_TYPE']._serialized_start=5758
  _globals['_ETB_RARITY_TYPE']._serialized_end=5947
  _globals['_ETB_JOKER_RARITY_TYPE']._serialized_start=5950
  _globals['_ETB_JOKER_RARITY_TYPE']._serialized_end=6217
  _globals['_ETB_ITEM_RARITY_TYPE']._serialized_start=6220
  _globals['_ETB_ITEM_RARITY_TYPE']._serialized_end=6474
  _globals['_ETB_SCROLL_RARITY_TYPE']._serialized_start=6477
  _globals['_ETB_SCROLL_RARITY_TYPE']._serialized_end=6757
  _globals['_ETB_OUTGAME_ITEM_TYPE']._serialized_start=6760
  _globals['_ETB_OUTGAME_ITEM_TYPE']._serialized_end=6907
  _globals['_ETB_MONSTER_TYPE']._serialized_start=6909
  _globals['_ETB_MONSTER_TYPE']._serialized_end=6992
  _globals['_ETB_GAME_MODE']._serialized_start=6994
  _globals['_ETB_GAME_MODE']._serialized_end=7097
  _globals['_ETB_SHOP_PERIOD_TYPE']._serialized_start=7099
  _globals['_ETB_SHOP_PERIOD_TYPE']._serialized_end=7213
  _globals['_ETB_SHOP_MILEAGE_TYPE']._serialized_start=7215
  _globals['_ETB_SHOP_MILEAGE_TYPE']._serialized_end=7340
  _globals['_ETB_SHOP_SALE_TYPE']._serialized_start=7342
  _globals['_ETB_SHOP_SALE_TYPE']._serialized_end=7431
  _globals['_ETB_OUTGAME_ITEM_GET_PLACE']._serialized_start=7434
  _globals['_ETB_OUTGAME_ITEM_GET_PLACE']._serialized_end=7609
  _globals['_ETB_ABILITY_CATEGORY_TYPE']._serialized_start=7612
  _globals['_ETB_ABILITY_CATEGORY_TYPE']._serialized_end=7834
  _globals['_ETB_TOGGLE_SETTING_TYPE']._serialized_start=7837
  _globals['_ETB_TOGGLE_SETTING_TYPE']._serialized_end=8034
  _globals['_ETB_OVER_KILL']._serialized_start=8037
  _globals['_ETB_OVER_KILL']._serialized_end=8230
  _globals['_ETB_MISSION_RECORD_TYPE']._serialized_start=8232
  _globals['_ETB_MISSION_RECORD_TYPE']._serialized_end=8326
  _globals['_ETB_IN_GAME_MISSION_REWARD_TYPE']._serialized_start=8329
  _globals['_ETB_IN_GAME_MISSION_REWARD_TYPE']._serialized_end=8602
  _globals['_ETB_IN_GAME_MISSION_TYPE']._serialized_start=8605
  _globals['_ETB_IN_GAME_MISSION_TYPE']._serialized_end=9270
  _globals['_ETB_REPORT_TYPE']._serialized_start=9272
  _globals['_ETB_REPORT_TYPE']._serialized_end=9352
  _globals['_ETB_TIMING_PACK_TYPE']._serialized_start=9355
  _globals['_ETB_TIMING_PACK_TYPE']._serialized_end=9483
  _globals['_ETB_ABILITY_TYPE']._serialized_start=9486
  _globals['_ETB_ABILITY_TYPE']._serialized_end=22112
# @@protoc_insertion_point(module_scope)
