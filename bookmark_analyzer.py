import pandas as pd
import json
import logging
import pickle
from datetime import datetime
from typing import List, Dict, Any, Optional
from collections import Counter, defaultdict
import os
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from s3_utils import download_multiple_s3_files
from config import BOOKMARK_NAME_FILE

# 로깅 설정
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)

# 캐시 디렉토리 설정
BOOKMARK_CACHE_DIR = "cache/bookmark_analysis"
BOOKMARK_FILE_CACHE_DIR = "cache/bookmark_files"

# 책갈피 이름 매핑 캐시
_bookmark_names_cache = {}
_bookmark_names_loaded = False

def load_bookmark_names():
    """책갈피 이름 매핑을 로드합니다."""
    global _bookmark_names_cache, _bookmark_names_loaded

    if _bookmark_names_loaded:
        return

    try:
        if os.path.exists(BOOKMARK_NAME_FILE):
            with open(BOOKMARK_NAME_FILE, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line and ' ' in line:
                        parts = line.split(' ', 1)
                        try:
                            idx = int(parts[0])
                            name = parts[1]
                            _bookmark_names_cache[idx] = name
                        except ValueError:
                            continue
            logger.info(f"책갈피 이름 {len(_bookmark_names_cache)}개 로드 완료")
        else:
            logger.warning(f"책갈피 이름 파일을 찾을 수 없습니다: {BOOKMARK_NAME_FILE}")
    except Exception as e:
        logger.error(f"책갈피 이름 로드 중 오류: {e}")

    _bookmark_names_loaded = True

def get_bookmark_korean_name(bookmark_id: int) -> str:
    """책갈피 ID를 한글 이름으로 변환합니다."""
    load_bookmark_names()

    # 1000번 이상은 더미 데이터로 처리
    if bookmark_id >= 1000:
        return f"더미데이터 {bookmark_id}"

    korean_name = _bookmark_names_cache.get(bookmark_id)
    if korean_name:
        return f"{korean_name} (ID:{bookmark_id})"
    return f"책갈피 {bookmark_id}"

def ensure_cache_directories():
    """캐시 디렉토리들이 존재하는지 확인하고 생성합니다."""
    os.makedirs(BOOKMARK_CACHE_DIR, exist_ok=True)
    os.makedirs(BOOKMARK_FILE_CACHE_DIR, exist_ok=True)

def get_bookmark_cache_key(s3_files: List[str]) -> str:
    """S3 파일 리스트를 기반으로 캐시 키를 생성합니다."""
    import hashlib
    files_str = '|'.join(sorted(s3_files))
    return hashlib.md5(files_str.encode()).hexdigest()

def save_bookmark_analysis_cache(result: Dict, cache_key: str):
    """bookmark 분석 결과를 캐시에 저장합니다."""
    try:
        ensure_cache_directories()
        cache_file = os.path.join(BOOKMARK_CACHE_DIR, f"{cache_key}.pkl")

        with open(cache_file, 'wb') as f:
            pickle.dump(result, f)

        logger.info(f"bookmark 분석 결과 캐시 저장: {cache_key}")
    except Exception as e:
        logger.error(f"bookmark 분석 캐시 저장 실패: {e}")

def load_bookmark_analysis_cache(cache_key: str) -> Optional[Dict]:
    """캐시에서 bookmark 분석 결과를 로드합니다."""
    try:
        cache_file = os.path.join(BOOKMARK_CACHE_DIR, f"{cache_key}.pkl")

        if not os.path.exists(cache_file):
            return None

        # 캐시 파일이 24시간 이내인지 확인
        file_age = datetime.now() - datetime.fromtimestamp(os.path.getmtime(cache_file))
        if file_age.total_seconds() > 24 * 3600:  # 24시간
            os.remove(cache_file)
            logger.info(f"오래된 bookmark 캐시 파일 삭제: {cache_key}")
            return None

        with open(cache_file, 'rb') as f:
            result = pickle.load(f)

        logger.info(f"bookmark 분석 결과 캐시 로드: {cache_key}")
        return result

    except Exception as e:
        logger.error(f"bookmark 분석 캐시 로드 실패: {e}")
        return None

def save_bookmark_file_cache(s3_file: str, bookmark_data: Optional[Dict]):
    """개별 파일의 bookmark 데이터를 캐시에 저장합니다."""
    try:
        ensure_cache_directories()
        # S3 파일 경로를 안전한 파일명으로 변환
        safe_filename = s3_file.replace('/', '_').replace('\\', '_')
        cache_file = os.path.join(BOOKMARK_FILE_CACHE_DIR, f"{safe_filename}.pkl")

        with open(cache_file, 'wb') as f:
            pickle.dump(bookmark_data, f)

    except Exception as e:
        logger.error(f"bookmark 파일 캐시 저장 실패: {s3_file}, 오류: {e}")

def load_bookmark_file_cache(s3_file: str) -> Optional[Dict]:
    """개별 파일의 bookmark 데이터를 캐시에서 로드합니다."""
    try:
        safe_filename = s3_file.replace('/', '_').replace('\\', '_')
        cache_file = os.path.join(BOOKMARK_FILE_CACHE_DIR, f"{safe_filename}.pkl")

        if not os.path.exists(cache_file):
            return None

        # 캐시 파일이 24시간 이내인지 확인
        file_age = datetime.now() - datetime.fromtimestamp(os.path.getmtime(cache_file))
        if file_age.total_seconds() > 24 * 3600:  # 24시간
            os.remove(cache_file)
            return None

        with open(cache_file, 'rb') as f:
            return pickle.load(f)

    except Exception as e:
        logger.error(f"bookmark 파일 캐시 로드 실패: {s3_file}, 오류: {e}")
        return None

def clear_old_bookmark_cache():
    """오래된 bookmark 캐시 파일들을 정리합니다."""
    try:
        ensure_cache_directories()
        current_time = datetime.now()

        # 분석 결과 캐시 정리
        for cache_dir in [BOOKMARK_CACHE_DIR, BOOKMARK_FILE_CACHE_DIR]:
            if os.path.exists(cache_dir):
                for filename in os.listdir(cache_dir):
                    file_path = os.path.join(cache_dir, filename)
                    if os.path.isfile(file_path):
                        file_age = current_time - datetime.fromtimestamp(os.path.getmtime(file_path))
                        if file_age.total_seconds() > 24 * 3600:  # 24시간
                            os.remove(file_path)
                            logger.info(f"오래된 bookmark 캐시 파일 삭제: {file_path}")
    except Exception as e:
        logger.error(f"bookmark 캐시 정리 실패: {e}")

def parse_log_file_for_bookmark_analysis(file_path: str) -> List[Dict]:
    """
    로그 파일을 파싱하여 bookmark 분석에 필요한 데이터를 추출합니다.

    Args:
        file_path: 로그 파일 경로

    Returns:
        파싱된 로그 리스트
    """
    try:
        from log_parser import process_log_file

        # 기존 log_parser의 process_log_file 함수 사용
        df = process_log_file(file_path)

        if df.empty:
            logger.debug(f"파일 {os.path.basename(file_path)}: 빈 DataFrame")
            return []

        # CURRENCY_DATA 이벤트만 필터링
        current_goods_df = df[df['event_type'] == 'MAF_ANALYTICS_LOG_EVENT_TYPE_USER_CURRENCY_DATA']

        if current_goods_df.empty:
            logger.debug(f"파일 {os.path.basename(file_path)}: CURRENCY_DATA 이벤트 없음 (전체 {len(df)}개 로그)")
            return []

        logger.info(f"파일 {os.path.basename(file_path)}: 전체 {len(df)}개 로그 중 {len(current_goods_df)}개의 CURRENCY_DATA 이벤트 발견")

        # DataFrame을 Dict 리스트로 변환
        logs = []

        for _, row in current_goods_df.iterrows():
            log_dict = {
                'event_type': row['event_type'],
                'time': None,
                'jsonData': None
            }

            # 시간 정보 추출 (purchase_analyzer.py와 동일한 방식)
            time_value = None
            if 'payload_time' in row and pd.notna(row['payload_time']):
                time_value = row['payload_time']

                # 시간이 문자열인 경우 datetime으로 변환
                if isinstance(time_value, str):
                    try:
                        # ISO 형식 시간 파싱
                        if 'T' in time_value:
                            time_value = datetime.fromisoformat(time_value.replace('Z', '+00:00'))
                        else:
                            time_value = datetime.strptime(time_value, '%Y-%m-%d %H:%M:%S')
                    except Exception as e:
                        logger.debug(f"시간 파싱 실패: {e}")
                        time_value = None
                elif isinstance(time_value, (int, float)):
                    try:
                        # timestamp인 경우 (밀리초 단위)
                        time_value = datetime.fromtimestamp(time_value / 1000)
                    except Exception as e:
                        logger.debug(f"timestamp 파싱 실패: {e}")
                        time_value = None

            log_dict['time'] = time_value

            # payload_goods_data에서 jsonData 추출 (nan 값 처리)
            def clean_value(value):
                if pd.isna(value) or str(value).strip() == 'nan':
                    return None
                return value

            json_data = clean_value(row.get('payload_goods_data'))
            if json_data:
                log_dict['jsonData'] = json_data
                logs.append(log_dict)
                logger.debug(f"유효한 CURRENCY_DATA 로그 추가: jsonData 길이 {len(str(json_data))}")
            else:
                logger.debug("payload_goods_data가 없거나 비어있음")

        logger.info(f"파일 {os.path.basename(file_path)}: {len(logs)}개의 유효한 CURRENCY_DATA 로그 추출")
        return logs

    except Exception as e:
        logger.error(f"로그 파일 파싱 실패: {file_path}, 오류: {e}")
        return []

def extract_user_id_from_filename(file_path: str) -> Optional[str]:
    """
    파일명에서 사용자 ID를 추출합니다.

    Args:
        file_path: 파일 경로

    Returns:
        사용자 ID 또는 None
    """
    try:
        filename = os.path.basename(file_path)
        # 파일명 패턴: userID_HH_MM_SS.pb.zst
        if '_' in filename:
            return filename.split('_')[0]
        return None
    except Exception:
        return None

def extract_bookmark_data_from_log(log: Dict) -> Optional[List[Dict]]:
    """
    로그에서 bookmark 데이터를 추출합니다.

    Args:
        log: 파싱된 로그 딕셔너리

    Returns:
        bookmark 데이터 리스트 또는 None
    """
    try:
        # CURRENCY_DATA 이벤트 타입 확인
        event_type = log.get('event_type')
        if event_type != 'MAF_ANALYTICS_LOG_EVENT_TYPE_USER_CURRENCY_DATA':
            logger.debug(f"이벤트 타입이 CURRENCY_DATA가 아님: {event_type}")
            return None

        # jsonData에서 bookmark 데이터 추출
        json_data = log.get('jsonData')
        if not json_data:
            logger.debug("jsonData가 없음")
            return None

        logger.debug(f"jsonData 타입: {type(json_data)}, 길이: {len(str(json_data))}")

        # JSON 문자열을 파싱
        if isinstance(json_data, str):
            try:
                parsed_data = json.loads(json_data)
                logger.debug(f"JSON 파싱 성공, 키들: {list(parsed_data.keys()) if isinstance(parsed_data, dict) else 'dict가 아님'}")
            except json.JSONDecodeError as e:
                logger.warning(f"JSON 파싱 실패: {e}")
                return None
        else:
            parsed_data = json_data
            logger.debug(f"이미 파싱된 데이터, 타입: {type(parsed_data)}")

        # bookmark 데이터 추출
        bookmark_data = parsed_data.get('bookmark', [])
        if not bookmark_data:
            logger.debug(f"bookmark 데이터가 없음. 사용 가능한 키들: {list(parsed_data.keys()) if isinstance(parsed_data, dict) else 'dict가 아님'}")
            return None

        logger.debug(f"bookmark 데이터 발견: {len(bookmark_data)}개 항목")

        # 유효한 bookmark 데이터만 필터링 (id가 0이 아니고 is_a가 true인 것)
        valid_bookmarks = []
        for i, bookmark in enumerate(bookmark_data):
            if isinstance(bookmark, dict):
                bookmark_id = bookmark.get('id', 0)
                is_active = bookmark.get('is_a', False)

                # 유효한 ID 범위 확인 (1000번 이상은 더미 데이터로 제외)
                if bookmark_id > 0 and bookmark_id < 1000 and is_active:
                    valid_bookmarks.append({
                        'id': bookmark_id,
                        'lv': bookmark.get('lv', 0),
                        'e_lv': bookmark.get('e_lv', 0),
                        'p_lv': bookmark.get('p_lv', 0),
                        'is_active': is_active
                    })
                    logger.debug(f"bookmark {i}: 보유 중 - ID:{bookmark_id}, is_a:{is_active}")
                else:
                    if bookmark_id >= 1000:
                        logger.debug(f"bookmark {i}: 더미 데이터 제외 - ID:{bookmark_id}")
                    elif not is_active:
                        logger.debug(f"bookmark {i}: 미보유 (is_a=false) - ID:{bookmark_id}")
                    elif bookmark_id <= 0:
                        logger.debug(f"bookmark {i}: 유효하지 않은 ID - ID:{bookmark_id}")
            else:
                logger.debug(f"bookmark {i}: 유효하지 않은 형식 - {bookmark}")

        logger.debug(f"유효한 bookmark: {len(valid_bookmarks)}개")
        return valid_bookmarks if valid_bookmarks else None
        
    except Exception as e:
        logger.warning(f"bookmark 데이터 추출 실패: {e}")
        return None

def find_latest_current_goods_data(logs: List[Dict]) -> Optional[Dict]:
    """
    로그 리스트에서 가장 최신의 CURRENCY_DATA 로그를 찾습니다.

    Args:
        logs: 로그 리스트

    Returns:
        가장 최신의 CURRENCY_DATA 로그 또는 None
    """
    current_goods_logs = []

    for log in logs:
        if log.get('event_type') == 'MAF_ANALYTICS_LOG_EVENT_TYPE_USER_CURRENCY_DATA':
            current_goods_logs.append(log)

    if not current_goods_logs:
        return None

    # 시간 순으로 정렬해서 가장 최신 것 반환
    current_goods_logs.sort(key=lambda x: x.get('time') or datetime.min, reverse=True)
    return current_goods_logs[0]

def analyze_user_bookmark_data(user_id: str, s3_files: List[str], download_dir: str) -> Optional[Dict]:
    """
    특정 사용자의 bookmark 데이터를 분석합니다.

    Args:
        user_id: 분석할 사용자 ID
        s3_files: S3 파일 리스트
        download_dir: 다운로드 디렉토리

    Returns:
        사용자의 bookmark 분석 결과
    """
    logger.info(f"개별 사용자 bookmark 분석 시작: {user_id}")

    try:
        # 사용자 관련 파일들만 필터링
        user_files = []
        for file_path in s3_files:
            file_user_id = extract_user_id_from_filename(file_path)
            if file_user_id == user_id:
                user_files.append(file_path)
        
        if not user_files:
            return None
            
        # 파일 다운로드
        downloaded_files = download_multiple_s3_files(user_files, download_dir)
        if not downloaded_files:
            return None
            
        # 모든 로그 수집
        all_logs = []
        for file_path in downloaded_files:
            try:
                logs = parse_log_file_for_bookmark_analysis(file_path)
                if logs:
                    all_logs.extend(logs)
            except Exception as e:
                logger.warning(f"파일 처리 실패: {file_path}, 오류: {e}")
                continue
                
        if not all_logs:
            return None
            
        # 가장 최신의 CURRENCY_DATA 로그 찾기
        latest_log = find_latest_current_goods_data(all_logs)
        if not latest_log:
            return None
            
        # bookmark 데이터 추출
        bookmark_data = extract_bookmark_data_from_log(latest_log)
        if not bookmark_data:
            return None
            
        return {
            'user_id': user_id,
            'timestamp': latest_log.get('time'),
            'bookmark_data': bookmark_data
        }
        
    except Exception as e:
        logger.error(f"사용자 bookmark 분석 실패: {user_id}, 오류: {e}")
        return None

def analyze_all_users_bookmark_data(s3_files: List[str], download_dir: str) -> Dict:
    """
    모든 사용자의 bookmark 데이터를 분석합니다.

    Args:
        s3_files: S3 파일 리스트
        download_dir: 다운로드 디렉토리

    Returns:
        전체 사용자 bookmark 분석 결과
    """
    logger.info(f"bookmark 분석 시작: {len(s3_files)}개 파일")

    # 오래된 캐시 정리
    clear_old_bookmark_cache()

    # 캐시 키 생성
    cache_key = get_bookmark_cache_key(s3_files)

    # 캐시에서 결과 로드 시도
    cached_result = load_bookmark_analysis_cache(cache_key)
    if cached_result:
        logger.info(f"캐시에서 bookmark 분석 결과 로드 완료: {cache_key}")
        return cached_result

    logger.info(f"새로운 bookmark 분석 시작: {cache_key}")

    try:
            
        # 파일 다운로드
        downloaded_files = download_multiple_s3_files(s3_files, download_dir)
        if not downloaded_files:
            return {'error': '다운로드된 파일이 없습니다'}
            
        # 사용자별 최신 bookmark 데이터 저장
        user_bookmark_data = {}

        # 점진적 캐싱: 캐시된 파일과 새로 처리할 파일 분리
        cached_files = 0
        new_files_to_process = []
        all_current_goods_logs = []

        # 각 파일별로 캐시 확인
        for s3_file, local_file in zip(s3_files, downloaded_files):
            cached_logs = load_bookmark_file_cache(s3_file)
            if cached_logs is not None:
                # 캐시된 결과 사용
                all_current_goods_logs.extend(cached_logs)
                cached_files += 1
            else:
                # 새로 처리해야 할 파일
                new_files_to_process.append((s3_file, local_file))

        logger.info(f"캐시 확인 완료: 캐시된 파일 {cached_files}개, 새로 처리할 파일 {len(new_files_to_process)}개")

        # 새로 처리할 파일이 있는 경우에만 병렬 처리 실행
        if new_files_to_process:
            logger.info(f"병렬 처리로 새 파일 파싱 시작: {len(new_files_to_process)}개 파일")

            # 병렬 처리로 파일 분석
            def process_file(s3_file, file_path):
                try:
                    logs = parse_log_file_for_bookmark_analysis(file_path)
                    if not logs:
                        # 빈 결과도 캐시에 저장
                        save_bookmark_file_cache(s3_file, [])
                        return []

                    # 파일명에서 사용자 ID 추출
                    user_id = extract_user_id_from_filename(file_path)

                    if not user_id:
                        save_bookmark_file_cache(s3_file, [])
                        return []

                    # CURRENCY_DATA 로그들 찾기
                    current_goods_logs = []
                    for log in logs:
                        if log.get('event_type') == 'MAF_ANALYTICS_LOG_EVENT_TYPE_USER_CURRENCY_DATA':
                            log['user_id'] = user_id  # 사용자 ID 추가
                            current_goods_logs.append(log)

                    # 결과를 캐시에 저장
                    save_bookmark_file_cache(s3_file, current_goods_logs)
                    return current_goods_logs

                except Exception as e:
                    logger.warning(f"파일 처리 실패: {file_path}, 오류: {e}")
                    save_bookmark_file_cache(s3_file, [])
                    return []
        
            # 병렬 처리 실행
            max_workers = min(8, os.cpu_count() or 4)

            with ThreadPoolExecutor(max_workers=max_workers) as executor:
                future_to_file = {executor.submit(process_file, s3_file, local_file): (s3_file, local_file)
                                for s3_file, local_file in new_files_to_process}

                for future in as_completed(future_to_file):
                    try:
                        logs = future.result()
                        all_current_goods_logs.extend(logs)
                    except Exception as e:
                        logger.error(f"파일 처리 중 오류: {e}")
                        continue
        
        # 사용자별로 가장 최신 로그만 유지
        logger.info(f"수집된 CURRENCY_DATA 로그 총 {len(all_current_goods_logs)}개 처리 시작")

        processed_users = 0
        failed_extractions = 0

        for log in all_current_goods_logs:
            user_id = log.get('user_id')
            if not user_id:
                logger.debug("user_id가 없는 로그 건너뛰기")
                continue

            log_time = log.get('time', datetime.min)

            if user_id not in user_bookmark_data or log_time > user_bookmark_data[user_id]['timestamp']:
                logger.debug(f"사용자 {user_id}의 로그에서 bookmark 데이터 추출 시도")
                bookmark_data = extract_bookmark_data_from_log(log)
                if bookmark_data:
                    user_bookmark_data[user_id] = {
                        'user_id': user_id,
                        'timestamp': log_time,
                        'bookmark_data': bookmark_data
                    }
                    processed_users += 1
                    logger.debug(f"사용자 {user_id}: {len(bookmark_data)}개 bookmark 데이터 추출 성공")
                else:
                    failed_extractions += 1
                    logger.debug(f"사용자 {user_id}: bookmark 데이터 추출 실패")

        logger.info(f"처리 완료: {processed_users}명 성공, {failed_extractions}개 실패")
        
        result = {
            'total_users': len(user_bookmark_data),
            'user_data': user_bookmark_data
        }

        # 결과를 캐시에 저장
        save_bookmark_analysis_cache(result, cache_key)

        return result

    except Exception as e:
        logger.error(f"전체 사용자 bookmark 분석 실패: {e}")
        return {'error': f'분석 실패: {e}'}

def create_individual_bookmark_summary(user_data: Dict) -> pd.DataFrame:
    """
    개별 사용자의 bookmark 데이터를 DataFrame으로 변환합니다.

    Args:
        user_data: 사용자 bookmark 분석 결과

    Returns:
        bookmark 데이터 DataFrame
    """
    if not user_data or 'bookmark_data' not in user_data:
        return pd.DataFrame()

    bookmark_data = user_data['bookmark_data']

    # DataFrame 생성
    df = pd.DataFrame(bookmark_data)

    # 컬럼 순서 정렬 및 한글 이름 추가
    if not df.empty:
        # 한글 이름 컬럼 추가
        df['bookmark_name'] = df['id'].apply(get_bookmark_korean_name)
        # 컬럼 순서 재정렬: 한글 이름을 첫 번째로
        df = df[['bookmark_name', 'id', 'lv', 'e_lv', 'p_lv']].sort_values('id')
        df.reset_index(drop=True, inplace=True)

    return df

def create_overall_bookmark_statistics(all_user_data: Dict) -> pd.DataFrame:
    """
    전체 사용자의 bookmark e_lv 통계를 생성합니다.
    북마크 ID당 하나의 줄에 모든 e_lv 통계를 가로로 나열합니다.

    Args:
        all_user_data: 전체 사용자 bookmark 분석 결과

    Returns:
        e_lv 통계 DataFrame (북마크별 가로 형태)
    """
    logger.info("전체 bookmark 통계 생성 시작")

    if not all_user_data or 'user_data' not in all_user_data:
        logger.warning("all_user_data가 없거나 user_data 키가 없음")
        return pd.DataFrame()

    user_data = all_user_data['user_data']
    logger.info(f"분석할 사용자 수: {len(user_data)}")

    if not user_data:
        logger.warning("사용자 데이터가 비어있음")
        return pd.DataFrame()

    # bookmark ID별 e_lv 데이터 수집
    bookmark_stats = defaultdict(lambda: defaultdict(int))
    bookmark_totals = defaultdict(int)
    total_bookmarks = 0

    for user_id, user_info in user_data.items():
        bookmark_data = user_info.get('bookmark_data', [])
        logger.debug(f"사용자 {user_id}: {len(bookmark_data)}개 bookmark")

        for bookmark in bookmark_data:
            bookmark_id = bookmark['id']
            e_lv = bookmark['e_lv']

            bookmark_stats[bookmark_id][e_lv] += 1
            bookmark_totals[bookmark_id] += 1
            total_bookmarks += 1

    logger.info(f"총 {total_bookmarks}개 bookmark 데이터 수집, {len(bookmark_stats)}개 고유 bookmark ID")

    if not bookmark_stats:
        logger.warning("bookmark_stats가 비어있음 - 통계 생성 불가")
        return pd.DataFrame()

    # 모든 e_lv 값 수집 (정렬된 순서로)
    all_e_lv_values = set()
    for e_lv_counts in bookmark_stats.values():
        all_e_lv_values.update(e_lv_counts.keys())
    all_e_lv_values = sorted(all_e_lv_values)

    # 통계 데이터 생성 (가로 형태)
    stats_data = []

    for bookmark_id in sorted(bookmark_stats.keys()):
        e_lv_counts = bookmark_stats[bookmark_id]
        total_count = bookmark_totals[bookmark_id]

        logger.debug(f"bookmark_id {bookmark_id}: 총 {total_count}개, e_lv 분포: {dict(e_lv_counts)}")

        # 한 줄에 모든 e_lv 통계를 담을 딕셔너리
        row_data = {
            'bookmark_name': get_bookmark_korean_name(bookmark_id),
            'total_count': total_count
        }

        # 각 e_lv별 개수와 비율 추가
        for e_lv in all_e_lv_values:
            count = e_lv_counts.get(e_lv, 0)
            ratio = (count / total_count * 100) if total_count > 0 else 0

            row_data[f'e_lv_{e_lv}_count'] = count
            row_data[f'e_lv_{e_lv}_ratio'] = f"{ratio:.1f}%"

        stats_data.append(row_data)

    logger.info(f"통계 데이터 생성 완료: {len(stats_data)}개 북마크, {len(all_e_lv_values)}개 e_lv 레벨")

    # DataFrame 생성
    df = pd.DataFrame(stats_data)

    # 컬럼 순서 정리: bookmark_name, total_count, 그 다음 e_lv별 통계
    if not df.empty:
        base_columns = ['bookmark_name', 'total_count']
        e_lv_columns = []

        for e_lv in all_e_lv_values:
            e_lv_columns.extend([f'e_lv_{e_lv}_count', f'e_lv_{e_lv}_ratio'])

        df = df[base_columns + e_lv_columns]

    return df
