﻿syntax = "proto3";
package google.protobuf;

enum EBT_SortType {
  EBT_SORTTYPE_NONE = 0;
  HIGH = 1;
  TYPE = 2;
}

enum EBT_ATTENTION_UPDATE_TYPE {
  EBT_ATTENTION_UPDATE_TYPE_NONE = 0;
  CHIPS = 1;
  X_CHIPS = 2;
  H_X_CHIPS = 3;
  MULT = 4;
  X_MULT = 5;
  H_X_MULT = 6;
  H_MULT = 7;
  DOLLARS = 8;
  REPEAT = 9;
  JOKER = 10;
  EXTRA = 11;
  EBT_ATTENTION_UPDATE_TYPE_MAX = 12;
}

enum ETB_IN_GAME_MISSION_STATE {
  ETB_IN_GAME_MISSION_STATE_NONE = 0;
  MISSION_PROGRESS = 1;
  MISSION_COMPLETE = 2;
  REWARD_COMPLETE = 3;
}

enum EBT_SOUND_TYPE {
  EBT_SOUND_TYPE_NONE = 0;
  CLICK_POSITIVE = 1;
  ATTACK = 2;
  ATTACK_OVER_KILL = 3;
  ATTACK_UNIT = 4;
  BOSS_ABILITY = 5;
  CARD_1 = 6;
  CARD_2 = 7;
  CHIP_1 = 8;
  SHOP_BUY_1 = 9;
  SHOP_BUY_2 = 10;
  EXPLOSION = 11;
  GENERIC_1 = 12;
  MULT_P = 13;
  MULT_M = 14;
  EBT_SOUND_TYPE_REPEAT = 15;
  REPEAT_UNIT = 16;
  SHOP_REROLL = 17;
}

enum EBT_BATTLE_LOG_EVENT_TYPE {
  EBT_BATTLE_LOG_EVENT_TYPE_NONE = 0;
  EBT_BATTLE_LOG_EVENT_TYPE_PLAYING_CARD_DECK_INIT = 1;
  EBT_BATTLE_LOG_EVENT_TYPE_RESET_PLAY_DECK = 2;
  EBT_BATTLE_LOG_EVENT_TYPE_PLAYING_CARD_DECK_SETTING = 3;
  EBT_BATTLE_LOG_EVENT_TYPE_DRAW_CARD = 4;
  EBT_BATTLE_LOG_EVENT_TYPE_BUY_JOKER = 5;
  EBT_BATTLE_LOG_EVENT_TYPE_SELL_JOKER = 6;
  EBT_BATTLE_LOG_EVENT_TYPE_BUY_CONSUME_ITEM = 7;
  EBT_BATTLE_LOG_EVENT_TYPE_SELL_CONSUME_ITEM = 8;
  EBT_BATTLE_LOG_EVENT_TYPE_BUY_SCROLL_ITEM = 9;
  EBT_BATTLE_LOG_EVENT_TYPE_USE_CONSUME_ITEM = 10;
  EBT_BATTLE_LOG_EVENT_TYPE_USE_SCROLL = 11;
  EBT_BATTLE_LOG_EVENT_TYPE_CARD_EVAL = 12;
  EBT_BATTLE_LOG_EVENT_TYPE_PLAY_HAND = 13;
  EBT_BATTLE_LOG_EVENT_TYPE_UPDATE_PLAYING_CARD = 14;
  EBT_BATTLE_LOG_EVENT_TYPE_UPDATE_PLAYING_FULL_DECK = 15;
  EBT_BATTLE_LOG_EVENT_TYPE_THROW_CARD = 16;
  EBT_BATTLE_LOG_EVENT_TYPE_PLAY_HAND_BEFORE_SETTING = 17;
  EBT_BATTLE_LOG_EVENT_TYPE_PLAY_HAND_AFTER_CLEAR_DATA = 18;
  EBT_BATTLE_LOG_EVENT_TYPE_CALCULATE_MONSTER_HP = 19;
  EBT_BATTLE_LOG_EVENT_TYPE_BOSS_ABILITY = 20;
  EBT_BATTLE_LOG_EVENT_TYPE_ROUND_CLEAR = 21;
  EBT_BATTLE_LOG_EVENT_TYPE_ROUND_CLEAR_COIN = 22;
  EBT_BATTLE_LOG_EVENT_TYPE_STAGE_CLEAR = 23;
  EBT_BATTLE_LOG_EVENT_TYPE_BONUS_ATTACK = 24;
  EBT_BATTLE_LOG_EVENT_TYPE_MONSTER_INIT = 25;
  EBT_BATTLE_LOG_EVENT_TYPE_MONSTER_INIT_ABILITY = 26;
  EBT_BATTLE_LOG_EVENT_TYPE_OVER_KILL = 27;
  EBT_BATTLE_LOG_EVENT_TYPE_SHOP_OPEN = 28;
  EBT_BATTLE_LOG_EVENT_TYPE_SHOP_PRODUCT_REROLL = 29;
  EBT_BATTLE_LOG_EVENT_TYPE_SFX_PLAY = 30;
  EBT_BATTLE_LOG_EVENT_TYPE_TIME_DELAY = 31;
  EBT_BATTLE_LOG_EVENT_TYPE_CARD_UNPACK = 32;
  EBT_BATTLE_LOG_EVENT_TYPE_ROUND_CLEAR_CHALLENGE = 33;
  EBT_BATTLE_LOG_EVENT_TYPE_DESTROY_PLAY_CARD = 34;
  EBT_BATTLE_LOG_EVENT_TYPE_RESURRECTION_PLAY_CARD = 35;
  EBT_BATTLE_LOG_EVENT_TYPE_MISSION_REROLL_STAGE_START = 36;
  EBT_BATTLE_LOG_EVENT_TYPE_MISSION_REWARD = 37;
  EBT_BATTLE_LOG_EVENT_TYPE_MISSION_REWARD_SELECT = 38;
  EBT_BATTLE_LOG_EVENT_TYPE_MISSION_REROLL = 39;
  EBT_BATTLE_LOG_EVENT_TYPE_MISSION_REWARD_SKIP = 40;
  EBT_BATTLE_LOG_EVENT_TYPE_ADD_NEW_JOKER = 41;
  EBT_BATTLE_LOG_EVENT_TYPE_ADD_NEW_CONSUME_ITEM = 42;
  EBT_BATTLE_LOG_EVENT_TYPE_REMOVE_JOKER = 43;
  EBT_BATTLE_LOG_EVENT_TYPE_REMOVE_CONSUME_ITEM = 44;
  EBT_BATTLE_LOG_EVENT_TYPE_ROUND_START = 45;
  EBT_BATTLE_LOG_EVENT_TYPE_SHOP_LEVEL_UP = 46;
}

 enum EBT_BATTLE_LOG_PAYLOAD_TYPE {
  None = 0;
  Int = 1;
  Bool = 2;
  String = 3;
  AbilityType = 4;
  PokerHandsType = 5;
  MissionRewardType = 6;
  Card = 7;
  CardList = 8;
  IntList = 9;
  CardIndexList = 10;
  GameInfoSnapshot = 11;
  EvaluateResult = 12;
  EvaluateConfig = 13;
  Float = 14;
  Double = 15;
  GameInfo = 16;
  EvaluateContext = 17;
  OverKillType = 18;
  ULong = 19;
  StringDictionary = 20;
  StringDictionaryList = 21;
  ProductList = 22;
  InGameJoker = 23;
  InGameConsumeItem = 24;
  Product = 25;
 }

enum ETB_SFX_UI {
  ETB_SFX_UI_NONE = 0;
  CLICK_NORMAL = 1;
  ETB_SFX_UI_CLICK_POSITIVE = 2;
  CLICK_NEGATIVE = 3;
  CLICK_PURCHASE = 4;
}

enum ETB_Trump_Type {
  ETB_TRUMP_TYPE_NONE = 0;
  SPADE = 1;
  HEART = 2;
  CLUB = 3;
  DIAMOND = 4;
  ETB_TRUMP_TYPE_MAX = 5;
}

enum ETB_CARD_TYPE {
  ETB_CARD_TYPE_NONE = 0;
  PLAYING_CARD = 1;
  ETB_CARD_TYPE_JOKER = 2;
  CONSUME_ITEM = 3;
  SCROLL_ITEM = 4;
  ETB_CARD_TYPE_MAX = 5;
}

enum ETB_Poker_Hands_Type {
  ETB_POKER_HANDS_TYPE_NONE = 0;
  HIGH_CARD = 1;
  PAIR = 2;
  TWO_PAIR = 3;
  TRIPLE = 4;
  STRAIGHT = 5;
  FLUSH = 6;
  FULL_HOUSE = 7;
  FOUR_CARD = 8;
  STRAIGHT_FLUSH = 9;
  FIVE_CARD = 10;
  FLUSH_HOUSE = 11;
  FLUSH_FIVE = 12;
  ETB_POKER_HANDS_TYPE_MAX = 13;
}

enum ETB_STAGE_DIFFICULTY_TYPE {
  ETB_STAGE_DIFFICULTY_TYPE_NONE = 0;
  TUTORIAL = 1;
  NORMAL = 2;
  HARD = 3;
}

enum ETB_BATTLE_STATE {
  ETB_BATTLE_STATE_NONE = 0;
  BATTLE = 1;
  SHOP = 2;
  MISSION_REWARD = 3;
}

enum ETB_IN_GAME_STATE_TURN {
  ETB_IN_GAME_STATE_TURN_NONE = 0;
  GAME_START = 1;
  ROUND_START = 2;
  FIRST_DRAW = 3;
  PLAYER_OPERATE_TURN = 4;
  TURN_START_BEGIN = 5;
  TURN_START_AFTER = 6;
  JOKER_ENHANCE_TURN = 7;
  ETB_IN_GAME_STATE_TURN_PLAYING_CARD = 8;
  HAND_CARD = 9;
  TOMB_CARD = 10;
  ITEM_SLOT = 11;
  TURN_END_BEGIN = 12;
  TURN_END_AFTER = 13;
  TURN_ATTACK = 14;
  CARD_THROW = 15;
  CARD_DRAW = 16;
  ROUND_END = 17;
  ETB_IN_GAME_STATE_TURN_USE_CONSUME_ITEM = 18;
  ETB_IN_GAME_STATE_TURN_USE_SCROLL = 19;
  DESTROYED_PLAY_CARD = 20;
  OVER_KILL = 21;
  CARD_RESURRECTION = 22;
  BOOKMARK_LEVEL_BONUS_TURN = 23;
  ETB_IN_GAME_STATE_TURN_MAX = 24;
}

enum ETB_ABILITY_CALCULATE_TYPE {
  ETB_ABILITY_CALCULATE_TYPE_NONE = 0;
  PLUS = 1;
  MULTIPLY = 2;
  ETB_ABILITY_CALCULATE_TYPE_MAX = 3;
}

enum ETB_CALCULATOR_AREA {
  ETB_CALCULATOR_AREA_NONE = 0;
  PLAY = 1;
  HAND = 2;
  ETB_CALCULATOR_AREA_JOKER = 3;
  BOOKMARK = 4;
  ETB_CALCULATOR_AREA_ITEM_SLOT = 5;
  ETB_CALCULATOR_AREA_TOMB_CARD = 6;
  ETB_CALCULATOR_AREA_MAX = 7;
}

enum ETB_INGAME_SHOP_ITEM_TYPE {
  ETB_INGAME_SHOP_ITEM_TYPE_NONE = 0;
}

enum ETB_ENHANCE_TYPE {
  ETB_ENHANCE_TYPE_NONE = 0;
  WILDE = 1;
  BONUS = 2;
  MULTIP = 3;
  GLASS = 4;
  STEEL = 5;
  STONE = 6;
  GOLD = 7;
  LUCKY = 8;
  YELLO_SEAL = 9;
  RED_SEAL = 10;
  BLUE_SEAL = 11;
  PURPLE_SEAL = 12;
  ETB_ENHANCE_TYPE_MAX = 13;
}

enum ETB_ENHANCE_COLOR_TYPE {
  ETB_ENHANCE_COLOR_TYPE_NONE = 0;
  RED = 1;
  BLUE = 2;
  GREEN = 3;
  GRAY = 4;
  PURPLE = 5;
  YELLOW = 6;
}

enum ETB_CHAPTER_TYPE {
  ETB_CHAPTER_TYPE_NONE = 0;
  SCENARIO = 1;
  SPECIAL = 2;
  EVENT = 3;
}

enum ETB_RARITY_TYPE {
  ETB_RARITY_TYPE_NONE = 0;
  TIER_1 = 1;
  TIER_2 = 2;
  TIER_3 = 3;
  TIER_4 = 4;
  TIER_5 = 5;
  TIER_6 = 6;
  TIER_7 = 7;
  TIER_8 = 8;
  TIER_9 = 9;
  TIER_10 = 10;
  ETB_RARITY_TYPE_MAX = 11;
}

enum ETB_JOKER_RARITY_TYPE {
  ETB_JOKER_RARITY_TYPE_NONE = 0;
  JOKER_TIER_1 = 1;
  JOKER_TIER_2 = 2;
  JOKER_TIER_3 = 3;
  JOKER_TIER_4 = 4;
  JOKER_TIER_5 = 5;
  JOKER_TIER_6 = 6;
  JOKER_TIER_7 = 7;
  JOKER_TIER_8 = 8;
  JOKER_TIER_9 = 9;
  JOKER_TIER_10 = 10;
  ETB_JOKER_RARITY_TYPE_MAX = 11;
}

enum ETB_ITEM_RARITY_TYPE {
  ETB_ITEM_RARITY_TYPE_NONE = 0;
  ITEM_TIER_1 = 1;
  ITEM_TIER_2 = 2;
  ITEM_TIER_3 = 3;
  ITEM_TIER_4 = 4;
  ITEM_TIER_5 = 5;
  ITEM_TIER_6 = 6;
  ITEM_TIER_7 = 7;
  ITEM_TIER_8 = 8;
  ITEM_TIER_9 = 9;
  ITEM_TIER_10 = 10;
  ETB_ITEM_RARITY_TYPE_MAX = 11;
}

enum ETB_SCROLL_RARITY_TYPE {
  ETB_SCROLL_RARITY_TYPE_NONE = 0;
  SCROLL_TIER_1 = 1;
  SCROLL_TIER_2 = 2;
  SCROLL_TIER_3 = 3;
  SCROLL_TIER_4 = 4;
  SCROLL_TIER_5 = 5;
  SCROLL_TIER_6 = 6;
  SCROLL_TIER_7 = 7;
  SCROLL_TIER_8 = 8;
  SCROLL_TIER_9 = 9;
  SCROLL_TIER_10 = 10;
  ETB_SCROLL_RARITY_TYPE_MAX = 11;
}

enum ETB_OUTGAME_ITEM_TYPE {
  ETB_OUTGAME_ITEM_TYPE_NONE = 0;
  ITEM = 1;
  REWARD_BOX = 2;
  JOKER_PIECE = 3;
  POKER_HAND_PIECE = 4;
  BOOK_MARK = 5;
  SYSTEM = 6;
}

enum ETB_MONSTER_TYPE {
  ETB_MONSTER_TYPE_NONE = 0;
  MONSTER = 1;
  BOSS = 2;
  BOSS_HARD = 3;
}

enum ETB_GAME_MODE {
  ETB_GAME_MODE_NONE = 0;
  ETB_GAME_MODE_NORMAL = 1;
  RANK = 2;
  ETB_GAME_MODE_TUTORIAL = 99;
}

enum ETB_SHOP_PERIOD_TYPE {
  ETB_SHOP_PERIOD_TYPE_NONE = 0;
  ONETIME = 1;
  DAILY = 2;
  WEEKLY = 3;
  MONTHLY = 4;
  PERIOD = 5;
}

enum ETB_SHOP_MILEAGE_TYPE {
  ETB_SHOP_MILEAGE_TYPE_NONE = 0;
  MILEAGE1 = 1;
  PERIOD_MILEAGE1 = 2;
  MILEAGE2 = 3;
  PERIOD_MILEAGE2 = 4;
}

enum ETB_SHOP_SALE_TYPE {
  ETB_SHOP_SALE_TYPE_NONE = 0;
  CASH = 1;
  EXCHANGE = 2;
  AD_PRODUCT = 3;
}

enum ETB_OUTGAME_ITEM_GET_PLACE {
  ETB_OUTGAME_ITEM_GET_PLACE_NONE = 0;
  ETB_OUTGAME_ITEM_GET_PLACE_SHOP = 1;
  RANKING = 2;
  STORY = 3;
  GACHA = 4;
  ETB_OUTGAME_ITEM_GET_PLACE_EVENT = 5;
}

enum ETB_ABILITY_CATEGORY_TYPE {
  ETB_ABILITY_CATEGORY_TYPE_NONE = 0;
  CHIP = 1;
  ETB_ABILITY_CATEGORY_TYPE_MULTIPLY = 2;
  ETB_ABILITY_CATEGORY_TYPE_GOLD = 3;
  ETC = 4;
  REPETITION = 5;
  DESTROY = 6;
  ETB_ABILITY_CATEGORY_TYPE_MAX = 7;
}

enum ETB_TOGGLE_SETTING_TYPE {
  ETB_TOGGLE_SETTING_TYPE_NONE = 0;
  BOOKMARK_CUTSCENE = 1;
  VIBERATE = 2;
  PUSH = 3;
  BATTLE_BUTTON_REVERSE = 4;
  CHAT = 5;
  ATK_ACTION_SKIP = 6;
  ETB_TOGGLE_SETTING_TYPE_MAX = 7;
}

enum ETB_OVER_KILL {
  ETB_OVER_KILL_NONE = 0;
  LEVEL_1 = 1;
  LEVEL_2 = 2;
  LEVEL_3 = 3;
  LEVEL_4 = 4;
  LEVEL_5 = 5;
  LEVEL_6 = 6;
  LEVEL_7 = 7;
  LEVEL_8 = 8;
  LEVEL_9 = 9;
  LEVEL_10 = 10;
  ETB_OVER_KILL_MAX = 11;
}

enum ETB_MISSION_RECORD_TYPE {
  ETB_MISSION_RECORD_TYPE_NONE = 0;
  INCREASE = 1;
  TOTAL = 2;
  BEST = 3;
}

enum ETB_IN_GAME_MISSION_REWARD_TYPE {
  ETB_IN_GAME_MISSION_REWARD_TYPE_NONE = 0;
  COIN = 1;
  ITEM_CHOICE_TIER_1 = 2;
  ITEM_CHOICE_TIER_2 = 3;
  ITEM_CHOICE_TIER_3 = 4;
  JOKER_CHOICE_TIER_1 = 5;
  JOKER_CHOICE_TIER_2 = 6;
  JOKER_CHOICE_TIER_3 = 7;
  ETB_IN_GAME_MISSION_REWARD_TYPE_MAX = 8;
}

enum ETB_IN_GAME_MISSION_TYPE {
  ETB_IN_GAME_MISSION_TYPE_NONE = 0;
  ETB_IN_GAME_MISSION_TYPE_HIGH_CARD = 1;
  ETB_IN_GAME_MISSION_TYPE_PAIR = 2;
  ETB_IN_GAME_MISSION_TYPE_TWO_PAIR = 3;
  ETB_IN_GAME_MISSION_TYPE_TRIPLE = 4;
  ETB_IN_GAME_MISSION_TYPE_STRAIGHT = 5;
  ETB_IN_GAME_MISSION_TYPE_FLUSH = 6;
  ETB_IN_GAME_MISSION_TYPE_FULL_HOUSE = 7;
  ETB_IN_GAME_MISSION_TYPE_FOUR_CARD = 8;
  ETB_IN_GAME_MISSION_TYPE_STRAIGHT_FLUSH = 9;
  ETB_IN_GAME_MISSION_TYPE_FIVE_CARD = 10;
  ETB_IN_GAME_MISSION_TYPE_FLUSH_HOUSE = 11;
  ETB_IN_GAME_MISSION_TYPE_FLUSH_FIVE = 12;
  OVER_KILL_3 = 13;
  OVER_KILL_5 = 14;
  OVER_KILL_7 = 15;
  OVER_KILL_10 = 16;
  NO_THROW_ROUND_CLEAR = 17;
  ETB_IN_GAME_MISSION_TYPE_MAX = 18;
}

enum ETB_REPORT_TYPE {
  ETB_REPORT_TYPE_NONE = 0;
  ABUSIVE = 1;
  SPAM = 2;
  CONFLICT = 3;
}

enum ETB_TIMING_PACK_TYPE {
  ETB_TIMING_PACK_TYPE_NONE = 0;
  ETB_TIMING_PACK_TYPE_BOOKMARK = 1;
  STAGE = 2;
  USER_LV = 3;
  ORTALAB_LV = 4;
}

enum ETB_ABILITY_TYPE {
 ETB_ABILITY_TYPE_NONE                                                     = 0;
 ETB_ABILITY_TYPE_MULTIPLY                                                 = 1;
 ETB_ABILITY_TYPE_CHIP                                                     = 2;
 ETB_ABILITY_TYPE_MULTIPLY_PLAY_TRUMP                                      = 3;
 ETB_ABILITY_TYPE_CHIP_PLAY_TRUMP                                          = 4;
 ETB_ABILITY_TYPE_MULTIPLY_INCLUDE_POKER_HAND                              = 5;
 ETB_ABILITY_TYPE_CHIP_INCLUDE_POKER_HAND                                  = 6;
 ETB_ABILITY_TYPE_LOANS                                                    = 7;
 ETB_ABILITY_TYPE_CHIP_REMAIN_THROW_COUNT                                  = 8;
 ETB_ABILITY_TYPE_ROUND_END_DESTROY                                        = 9;
 ETB_ABILITY_TYPE_COPY_RIGHT                                               = 10;
 ETB_ABILITY_TYPE_MULTIPLY_PLAYED_CARD_COUNT                               = 11;
 ETB_ABILITY_TYPE_CHIP_PLAYED_CARD_COUNT                                   = 12;
 ETB_ABILITY_TYPE_MULTIPLY_TURN_COUNT_N                                    = 13;
 ETB_ABILITY_TYPE_MULTIPLY_RANDOM                                          = 14;
 ETB_ABILITY_TYPE_MULTIPLY_PLAY_PLAYCARD_RANK                              = 15;
 ETB_ABILITY_TYPE_CHIP_PLAY_PLAYCARD_RANK                                  = 16;
 ETB_ABILITY_TYPE_ALL_COUNT_PLAYCARD                                       = 17;
 ETB_ABILITY_TYPE_MULTIPLY_HAND_PLAYCARD_RANK                              = 18;
 ETB_ABILITY_TYPE_CHIP_HAND_PLAYCARD_RANK                                  = 19;
 ETB_ABILITY_TYPE_MULTIPLY_DECK_ENHANCE_CARD                               = 20;
 ETB_ABILITY_TYPE_CHIP_DECK_ENHANCE_BONUS                                  = 21;
 ETB_ABILITY_TYPE_MULTIPLY_DECK_OVER_COUNT_4_PLAYCARD_RANK                 = 22;
 ETB_ABILITY_TYPE_CHIP_DECK_OVER_COUNT_4_PLAYCARD_RANK                     = 23;
 ETB_ABILITY_TYPE_GET_GOLD_PLAY_TRUMP                                      = 24;
 ETB_ABILITY_TYPE_MULTIPLY_PLAY_TRUMP_RATE                                 = 25;
 ETB_ABILITY_TYPE_REGARD_AS_FACE_TYPE                                      = 26;
 ETB_ABILITY_TYPE_REGARD_AS_NUMBER_TYPE                                    = 27;
 ETB_ABILITY_TYPE_MULTIPLY_INCREASE_NONE_PLAY_3_CARD                       = 28;
 ETB_ABILITY_TYPE_GET_GOLD_THROW_ONCE_N_FACE_NUMBER_TYPE                   = 29;
 ETB_ABILITY_TYPE_MULTIPLY_FIRST_PLAYCARD_FACE_NUMBER_TYPE                 = 30;
 ETB_ABILITY_TYPE_SHOP_REROLL_FREE                                         = 31;
 ETB_ABILITY_TYPE_MULTIPLY_INCREASE_SHOP_REROLL                            = 32;
 ETB_ABILITY_TYPE_CHIP_DECK_COUNT                                          = 33;
 ETB_ABILITY_TYPE_MULTIPLY_HAND_ROW_RANK_MULTIPLE                          = 34;
 ETB_ABILITY_TYPE_MULTIPLY_HAVE_JOKER_COUNT                                = 35;
 ETB_ABILITY_TYPE_MULTIPLY_POKER_HAND_PLAY_COUNT                           = 36;
 ETB_ABILITY_TYPE_SELL_COST_UP_SELP                                        = 37;
 ETB_ABILITY_TYPE_GET_GOLD_PLAY_RANDOM_TARGET_POKER_HAND                   = 38;
 ETB_ABILITY_TYPE_CREATE_JOKER_ROUND_START                                 = 39;
 ETB_ABILITY_TYPE_GET_GOLD_TARGET_BY_RANDOM_RANK_CARD                      = 40;
 ETB_ABILITY_TYPE_MULTIPLY_INCREASE_TURN                                   = 41;
 ETB_ABILITY_TYPE_CHIP_INCREASE_TURN                                       = 42;
 ETB_ABILITY_TYPE_ADD_GOLD_ROUND_CLEAR                                     = 43;
 ETB_ABILITY_TYPE_MULTIPLY_DECREASE_TURN                                   = 44;
 ETB_ABILITY_TYPE_GET_GOLD_PLAY_ENHANCE_CARD                               = 45;
 ETB_ABILITY_TYPE_MULTIPLY_DIRECTION_JOKER_ALL_COST                        = 46;
 ETB_ABILITY_TYPE_MORE_TRIGGER_FIRST_PLAYCARD                              = 47;
 ETB_ABILITY_TYPE_MULTIPLY_JOKER_EMPTY_SLOT                                = 48;
 ETB_ABILITY_TYPE_FLUSH_STRAIGHT_CONDITION_4_CARD                          = 49;
 ETB_ABILITY_TYPE_MORE_TRIGGER_HAND                                        = 50;
 ETB_ABILITY_TYPE_MORE_TRIGGER_LAST_TURN                                   = 51;
 ETB_ABILITY_TYPE_MORE_TRIGGER_PLAYCARD_RANK                               = 52;
 ETB_ABILITY_TYPE_PLAY_POKER_HAND_RATE_LEVEL_UP                            = 53;
 ETB_ABILITY_TYPE_MULTIPLY_DECK_CARD_2_TRUMP_TYPE                          = 54;
 ETB_ABILITY_TYPE_USED_PLAYCARD_CHIP_RATE_ADD                              = 55;
 ETB_ABILITY_TYPE_MULTIPLY_PLAYED_POKER_HAND                               = 56;
 ETB_ABILITY_TYPE_MULTIPLY_ENHANCE_CARD_ABSORB                             = 57;
 ETB_ABILITY_TYPE_STRAIGHT_CONDITION_1_GAP                                 = 58;
 ETB_ABILITY_TYPE_GET_GOLD_ROUND_INCREASE                                  = 59;
 ETB_ABILITY_TYPE_GET_GOLD_DECK_PLAYCARD_RANK_COUNT                        = 60;
 ETB_ABILITY_TYPE_PLAYCARD_CHANGE_ENHANCE                                  = 61;
 ETB_ABILITY_TYPE_BLIND_BAN_SELL                                           = 62;
 ETB_ABILITY_TYPE_HAVE_JOKER_COST_UP                                       = 63;
 ETB_ABILITY_TYPE_HANDCOUNT_DECREASE_TURN                                  = 64;
 ETB_ABILITY_TYPE_MULTIPLY_DECK_UNDER_COUNT_N                              = 65;
 ETB_ABILITY_TYPE_GET_GOLD_INTEREST                                        = 66;
 ETB_ABILITY_TYPE_MULTIPLY_INCREASE_USED_ENHANCE                           = 67;
 ETB_ABILITY_TYPE_CHIP_HOLDING_GOLD                                        = 68;
 ETB_ABILITY_TYPE_GET_GOLD_THROW_CARD_COUNT_DESTROY                        = 69;
 ETB_ABILITY_TYPE_MULTIPLY_INCREASE_POKERHAND_COUNT                        = 70;
 ETB_ABILITY_TYPE_MULTIPLY_THROW_COUNT_DECREASE                            = 71;
 ETB_ABILITY_TYPE_MORE_TRIGGER_ALL_COUNTDOWN                               = 72;
 ETB_ABILITY_TYPE_CHIP_INCREASE_RANDOM_TARGET_TRUMP_THROW                  = 73;
 ETB_ABILITY_TYPE_ATTACK_COUNT_LAST_ATTACK_DESTROY                         = 74;
 ETB_ABILITY_TYPE_MULTIPLY_ATTACK_COUNT                                    = 75;
 ETB_ABILITY_TYPE_CREATE_ROUND_START_RANDOM_PLAYCARD                       = 76;
 ETB_ABILITY_TYPE_REGARD_AS_TRUMP                                          = 77;
 ETB_ABILITY_TYPE_MULTIPLY_ENHANCE_DESTROY_COUNT                           = 78;
 ETB_ABILITY_TYPE_RATE_UP                                                  = 79;
 ETB_ABILITY_TYPE_MULTIPLY_INCREASE_RANDOM_TARGET_PLAY_CARD                = 80;
 ETB_ABILITY_TYPE_MULTIPLY_INCLUDING_TRUMP_TYPE                            = 81;
 ETB_ABILITY_TYPE_GET_GOLD_PLAY_MONSTER_PENALTY                            = 82;
 ETB_ABILITY_TYPE_CREATE_TURN_RANDOM_ITEM                                  = 83;
 ETB_ABILITY_TYPE_SHOP_ROUND_ITEM_FREE_N                                   = 84;
 ETB_ABILITY_TYPE_FIRST_THROW_POKER_HAND_LEVEL_UP                          = 85;
 ETB_ABILITY_TYPE_FIRST_PLAY_ONE_CARD_COPY                                 = 86;
 ETB_ABILITY_TYPE_MULTIPLY_HOLDING_GOLD                                    = 87;
 ETB_ABILITY_TYPE_TURN_PLAYCARD_DESTROY_CREATE_RANDOM_ITEM                 = 88;
 ETB_ABILITY_TYPE_POKER_HAND_CREATE_RANDOM_ITEM                            = 89;
 ETB_ABILITY_TYPE_MULTIPLY_INCREASE_NONE_PLAY_MOST_POKER_HAND              = 90;
 ETB_ABILITY_TYPE_MULTIPLY_JOKER_RARITY                                    = 91;
 ETB_ABILITY_TYPE_MULTIPLY_RANDOM_TARGET_TRUMP_PLAY_CARD                   = 92;
 ETB_ABILITY_TYPE_MULTIPLY_INCREASE_SELL_JOKER                             = 93;
 ETB_ABILITY_TYPE_MULTIPLY_THROW_RANDOM_TARGET_RANK_RESET                  = 94;
 ETB_ABILITY_TYPE_MULTIPLY_HAVE_DECK_COUNT_ENHANCE_CARD                    = 95;
 ETB_ABILITY_TYPE_CHIP_INCREASE_PLAY_POKER_HAND                            = 96;
 ETB_ABILITY_TYPE_CHIP_DECREASE_TURN                                       = 97;
 ETB_ABILITY_TYPE_ITEM_PLAYCARD_CHANGE_ENHANCE                             = 98;
 ETB_ABILITY_TYPE_ITEM_PLAYCARD_DESTROY                                    = 99;
 ETB_ABILITY_TYPE_ITEM_PLAYCARD_CHANGE_TRUMP                               = 100;
 ETB_ABILITY_TYPE_ITEM_CREATE_JOKER_RANDOM_CARD                            = 101;
 ETB_ABILITY_TYPE_ITEM_POKER_HAND_UPGRADE                                  = 102;
 ETB_ABILITY_TYPE_ITEM_PLAYCARD_CHANGE_RAND_ONE_TYPE_ALL                   = 103;
 ETB_ABILITY_TYPE_ITEM_PLAYCARD_CHANGE_RAND_ONE_NUMBER_ALL                 = 104;
 ETB_ABILITY_TYPE_JOKER_SLOT                                               = 105;
 ETB_ABILITY_TYPE_REROLL_COST                                              = 106;
 ETB_ABILITY_TYPE_ATTACK_COUNT_ADD                                         = 107;
 ETB_ABILITY_TYPE_THROW_COUNT_ADD                                          = 108;
 ETB_ABILITY_TYPE_INTEREST_COUNT_ADD                                       = 109;
 ETB_ABILITY_TYPE_HAND_COUNT_ADD                                           = 110;
 ETB_ABILITY_TYPE_POKER_HAND_ALL_LEVEL_ADD                                 = 111;
 ETB_ABILITY_TYPE_ITEM_CARD_SLOT_ADD                                       = 112;
 ETB_ABILITY_TYPE_SHOP_DISCOUNT_PER                                        = 113;
 ETB_ABILITY_TYPE_DEBUFF_TRUMP_TYPE                                        = 114;
 ETB_ABILITY_TYPE_DEBUFF_CARD_N                                            = 115;
 ETB_ABILITY_TYPE_DEBUFF_BEFORE_ROUND_PLAYED_CARD                          = 116;
 ETB_ABILITY_TYPE_BLIND_HAND_DRAW_RANDOM                                   = 117;
 ETB_ABILITY_TYPE_BLIND_HAND_FIRST_CARD                                    = 118;
 ETB_ABILITY_TYPE_BLIND_HAND_DRAW_CARD                                     = 119;
 ETB_ABILITY_TYPE_BLIND_CARD_RANK                                          = 120;
 ETB_ABILITY_TYPE_PLAY_POKER_HAND_LEVEL_DOWN                               = 121;
 ETB_ABILITY_TYPE_HP_UP                                                    = 122;
 ETB_ABILITY_TYPE_CALCULATION_REDUCTION                                    = 123;
 ETB_ABILITY_TYPE_PLAY_MOST_POKER_HAND_PLAY_GOLD_0                         = 124;
 ETB_ABILITY_TYPE_GET_GOLD_PLAY_CARD                                       = 125;
 ETB_ABILITY_TYPE_LIMIT_ATTACK_COUNT                                       = 126;
 ETB_ABILITY_TYPE_PLAY_POKER_HAND_ONE_BY_ONE                               = 127;
 ETB_ABILITY_TYPE_PLAY_POKER_HAND_ONLY_ONE                                 = 128;
 ETB_ABILITY_TYPE_PLAY_AND_DRAW_CARD                                       = 129;
 ETB_ABILITY_TYPE_PLAY_CARD_THROW_RANDOM_CARD                              = 130;
 ETB_ABILITY_TYPE_PLAY_CARD_ONLY_CARD_COUNT                                = 131;
 ETB_ABILITY_TYPE_PLAY_CARD_ONLY_CARD_COUNT_RANDOM                         = 132;
 ETB_ABILITY_TYPE_LIMIT_TROW_COUNT                                         = 133;
 ETB_ABILITY_TYPE_DEBUFF_POKER_HAND_POKER_HAND                             = 134;
 ETB_ABILITY_TYPE_GET_GOLD_ATT_COUNT                                       = 135;
 ETB_ABILITY_TYPE_MULTIPLY_AFTER_TURN_ENTRY                                = 136;
 ETB_ABILITY_TYPE_MORE_TRIGGER_POKER_HAND_LEVEL_UP                         = 137;
 ETB_ABILITY_TYPE_GET_GOLD_TARGET_PLAYCARD                                 = 138;
 ETB_ABILITY_TYPE_GET_ITEM_TARGET_PLAYCARD                                 = 139;
 ETB_ABILITY_TYPE_MULTIPLY_TARGET_PLAYCARD                                 = 140;
 ETB_ABILITY_TYPE_CHIP_TARGET_PLAYCARD                                     = 141;
 ETB_ABILITY_TYPE_FIXED_REWARD_INTEREST                                    = 142;
 ETB_ABILITY_TYPE_FREE_FREEZE                                              = 143;
 ETB_ABILITY_TYPE_CHANGE_ENHANCE_CARD_BY_FACE_TYPE                         = 144;
 ETB_ABILITY_TYPE_MULTIPLY_PLAY_ENHANCE_CARD                               = 145;
 ETB_ABILITY_TYPE_CHIP_PLAY_ENHANCE_CARD                                   = 146;
 ETB_ABILITY_TYPE_CONSIDERED_POKER_HAND_LEVEL_BY_ENHANCE_CARD              = 147;
 ETB_ABILITY_TYPE_SELL_COST_UP                                             = 148;
 ETB_ABILITY_TYPE_SHOP_DISCOUNT_COUNT                                      = 149;
 ETB_ABILITY_TYPE_MULTIPLY_POKER_HAND_COMBO                                = 150;
 ETB_ABILITY_TYPE_ALL_TRIGGERBY_TARGET_POKER_HAND                          = 151;
 ETB_ABILITY_TYPE_MORE_TRIGGER_TRUMP_TYPE                                  = 152;
 ETB_ABILITY_TYPE_ADD_TRUMP_TYPE_TO_DECK                                   = 153;
 ETB_ABILITY_TYPE_MORE_TRIGGER_ENHANCE_CARD                                = 154;
 ETB_ABILITY_TYPE_CREATE_TURN_RANDOM_ENHANCE_CARD                          = 155;
 ETB_ABILITY_TYPE_MULTIPLY_DECK_COUNT_UNDER                                = 156;
 ETB_ABILITY_TYPE_MULTIPLY_DECK_COUNT_UP                                   = 157;
 ETB_ABILITY_TYPE_PLAYCARD_CHANGE_ENHANCE_BY_FACE_TYPE                     = 158;
 ETB_ABILITY_TYPE_CHIP_PLAY_FACE_TYPE                                      = 159;
 ETB_ABILITY_TYPE_MULTIPLY_PLAY_FACE_TYPE                                  = 160;
 ETB_ABILITY_TYPE_MULTIPLY_INCREASE_NONE_FACE_TYPE                         = 161;
 ETB_ABILITY_TYPE_MORE_TRIGGER_FACE_TYPE                                   = 162;
 ETB_ABILITY_TYPE_ITEM_PLAYCARD_CHANGE_RANK_UP                             = 163;
 ETB_ABILITY_TYPE_ITEM_PLAYCARD_COPY_RIGHT                                 = 164;
 ETB_ABILITY_TYPE_LABEL_NOT_INCLUDED_IN_ANY_AFFILIATION                    = 165;
 ETB_ABILITY_TYPE_LABEL_DEFAULT_CHIP_COVER                                 = 166;
 ETB_ABILITY_TYPE_LABEL_ALL_TRUMP_TYPE                                     = 167;
 ETB_ABILITY_TYPE_LABEL_GOLD_CARD                                          = 168;
 ETB_ABILITY_TYPE_LABEL_LUCKY_CARD                                         = 169;
 ETB_ABILITY_TYPE_LABEL_RED_SEAL                                           = 170;
 ETB_ABILITY_TYPE_LABEL_BLUE_SEAL                                          = 171;
 ETB_ABILITY_TYPE_SCROLL_CREATE_PLAYCARD_RANDOM                            = 172;
 ETB_ABILITY_TYPE_SCROLL_CREATE_PLAYCARD_RANDOM_SAME_CARD                  = 173;
 ETB_ABILITY_TYPE_SCROLL_CREATE_PLAYCARD_TARGET_TRUMP_RANDOM_NUMBER        = 174;
 ETB_ABILITY_TYPE_SCROLL_CREATE_PLAYCARD_TARGET_NUMBER_RANDOM_TRUMP        = 175;
 ETB_ABILITY_TYPE_SCROLL_CREATE_PLAYCARD_TARGET_3_NUMBER_RANDOM_TRUMP      = 176;
 ETB_ABILITY_TYPE_SCROLL_CREATE_PLAYCARD_TARGET_3_NUMBER_RANDOM_SAME_TRUMP = 177;
 ETB_ABILITY_TYPE_SCROLL_CREATE_PLAYCARD_TARGET_NUMBER_SET                 = 178;
 ETB_ABILITY_TYPE_SCROLL_CREATE_PLAYCARD_TARGET_TRUMP_SET                  = 179;
 ETB_ABILITY_TYPE_SCROLL_CREATE_PLAYCARD_TARGET_LABEL_RANDOM_CARD          = 180;
 ETB_ABILITY_TYPE_SCROLL_CREATE_PLAYCARD_TARGET_LABEL_TARGET_NUMBER        = 181;
 ETB_ABILITY_TYPE_SCROLL_CREATE_PLAYCARD_TARGET_LABEL_TARGET_TRUMP         = 182;
 ETB_ABILITY_TYPE_CREATE_ROUND_START_RANDOM_ENHANCE_PLAYCARD               = 183;
 ETB_ABILITY_TYPE_ITEM_PLAYCARD_CHANGE_ONE_TYPE_ALL                        = 184;
 ETB_ABILITY_TYPE_ITEM_PLAYCARD_CHANGE_ONE_NUMBER_ALL                      = 185;
 ETB_ABILITY_TYPE_SHOP_SLOT                                                = 186;
 ETB_ABILITY_TYPE_MULTIPLY_HAVE_ITME_COUNT                                 = 187;
 ETB_ABILITY_TYPE_CHIP_HAVE_ITME_COUNT                                     = 188;
 ETB_ABILITY_TYPE_ADD_THROW_BY_TURN                                        = 189;
 ETB_ABILITY_TYPE_NONE_PLAYED_CARD_DESTROY                                 = 190;
 ETB_ABILITY_TYPE_MULTIPLY_TOMB_COUNT                                      = 191;
 ETB_ABILITY_TYPE_CHIP_TOMB_COUNT                                          = 192;
 ETB_ABILITY_TYPE_DESTROY_CARD_CHANGE_TARGET_ENHANCE                       = 193;
 ETB_ABILITY_TYPE_DESTROY_CARD_CHANGE_RANDOM_ENHANCE                       = 194;
 ETB_ABILITY_TYPE_GET_GOLD_TURN                                            = 195;
 ETB_ABILITY_TYPE_CHIP_USER_STATUS_POKER_HAND_ALL                          = 196;
 ETB_ABILITY_TYPE_CHIP_USER_STATUS_POKER_HAND                              = 197;
 ETB_ABILITY_TYPE_MULTIPLY_USER_STATUS_POKER_HAND_ALL                      = 198;
 ETB_ABILITY_TYPE_MULTIPLY_USER_STATUS_POKER_HAND                          = 199;
 ETB_ABILITY_TYPE_USER_STATUS_RANDOM_SLOT_ADD                              = 200;
 ETB_ABILITY_TYPE_USER_STATUS_PRESET_SLOT_ADD                              = 201;
 ETB_ABILITY_TYPE_DEBUFF_MULTIPLY_REDUCTION_MISMATCHED_POKER_HAND          = 202;
 ETB_ABILITY_TYPE_DEBUFF_ONLY_OVERKILL_DEATH                               = 203;
 ETB_ABILITY_TYPE_DEBUFF_RANDOM_CHANGE_HAND_CARDS                          = 204;
 ETB_ABILITY_TYPE_DEBUFF_PLAY_CARDS_DESTROY                                = 205;
 ETB_ABILITY_TYPE_DEBUFF_CLEAR_ENHANCE_CARD_THROW_RATE                     = 206;
 ETB_ABILITY_TYPE_DEBUFF_HP_UP_ENHANCE_CARD_COUNT                          = 207;
 ETB_ABILITY_TYPE_DEBUFF_UNDER_CARD_COUNT                                  = 208;
 ETB_ABILITY_TYPE_DEBUFF_CLEAR_ENHANCE_CARD_PLAY_CARD                      = 209;
 ETB_ABILITY_TYPE_DEBUFF_DESTROY_RANDOM_JOKER_ROUND_END                    = 210;
 ETB_ABILITY_TYPE_DEBUFF_DESTROY_RANDOM_JOKER_THROW_COUNT                  = 211;
 ETB_ABILITY_TYPE_DEBUFF_DESTROY_RANDOM_JOKER_ATTACK_COUNT                 = 212;
 ETB_ABILITY_TYPE_DEBUFF_DECK_COUNT_HP_UP                                  = 213;
 ETB_ABILITY_TYPE_DEBUFF_DAMAGE_POKER_HAND_LV                              = 214;
 ETB_ABILITY_TYPE_RESURRECTION_RANDOM                                      = 215;
 ETB_ABILITY_TYPE_MULTIPLY_TOMB_TRUMP_COUNT                                = 216;
 ETB_ABILITY_TYPE_RESURRECTION_THROW_COUNT                                 = 217;
 ETB_ABILITY_TYPE_DESTROY_THROW_COUNT                                      = 218;
 ETB_ABILITY_TYPE_OVERKILL                                                 = 219;
 ETB_ABILITY_TYPE_DESTROY_THROW_COUNT_CREATE_RANDOM_PLAYCARD               = 220;
 ETB_ABILITY_TYPE_MULTIPLY_INCREASE_DESTROY_LEFT_RANDOM_JOKER              = 221;
 ETB_ABILITY_TYPE_CREATE_ITEM_UNDER_GOLD                                   = 222;
 ETB_ABILITY_TYPE_MULTIPLY_PLAY_CARD_COUNT                                 = 223;
 ETB_ABILITY_TYPE_MULTIPLY_PLAY_CARD_RANK_3_CARD                           = 224;
 ETB_ABILITY_TYPE_MULTIPLY_THROW_COUNT                                     = 225;
 ETB_ABILITY_TYPE_MULTIPLY_TOMB_RANK_COUNT                                 = 226;
 ETB_ABILITY_TYPE_CHIP_TOMB_RANK_COUNT                                     = 227;
 ETB_ABILITY_TYPE_NONE_PLAYED_CARD_DESTROY_RATE_UP                         = 228;
 ETB_ABILITY_TYPE_CREATE_PLAYCARD_COPY_BY_MIRROR_ITEM                      = 229;
 ETB_ABILITY_TYPE_MULTIPLY_DECK_NOT_HAVE_PLAYCARD_RANK                     = 230;
 ETB_ABILITY_TYPE_CHIP_DECK_NOT_HAVE_PLAYCARD_RANK                         = 231;
 ETB_ABILITY_TYPE_MULTIPLY_TARGET_CONSUME_ITEM_COMPOUND_INTEREST           = 232;
 ETB_ABILITY_TYPE_HP_PER_DMG_INCLUDE_POKER_HAND                            = 233;
 ETB_ABILITY_TYPE_CHANGE_ENHANCE_CARD_ITEM_USED                            = 234;
 ETB_ABILITY_TYPE_CREATE_TURN_RANDOM_TARGET_ITEM                           = 235;
 ETB_ABILITY_TYPE_MULTIPLY_TARGET_CONSUME_ITEM_SIMPLE_INTEREST             = 236;
 ETB_ABILITY_TYPE_TARGET_ITEM_SELECT_COUNT_UP                              = 237;
 ETB_ABILITY_TYPE_DEBUFF_HANDCOUNT                                         = 238;
 ETB_ABILITY_TYPE_DEBUFF_MULTIPLY_DECREASE_NOT_USE_CONSUME_ITEM            = 239;
 ETB_ABILITY_TYPE_DEBUFF_DESTROY_JOKER_DIRECTION_ROUND_START               = 240;
 ETB_ABILITY_TYPE_DEBUFF_ATTACK_TROW_ALL_HANDCARD                          = 241;
 ETB_ABILITY_TYPE_DEBUFF_DECRESE_GOLD_RATE_THROW                           = 242;
 ETB_ABILITY_TYPE_DEBUFF_DECRESE_GOLD_RATE_ATTACK                          = 243;
 ETB_ABILITY_TYPE_DEBUFF_BAN_JOKER_SELL_JOKER                              = 244;
 ETB_ABILITY_TYPE_DEBUFF_THROW_HP_UP                                       = 245;
 ETB_ABILITY_TYPE_DEBUFF_DROW_CARD_RANK_DOWN                               = 246;
 ETB_ABILITY_TYPE_MULTIPLY_PLAY_CARD_RANK_2_CARD                           = 247;
 ETB_ABILITY_TYPE_RESURRECTION_ALL_CURRENT_ROUND                           = 248;
 ETB_ABILITY_TYPE_HP_PER_DMG_TURN_COUNT                                    = 249;
 ETB_ABILITY_TYPE_MULTIPLY_COMPOUND_INTEREST_TURN                          = 250;
 ETB_ABILITY_TYPE_MULTIPLY_PLAY_CARD_TARGET_INCLUDE_RANK                   = 251;
 ETB_ABILITY_TYPE_DEBUFF_MULTIPLY_DECREASE_HAS_COIN		                   = 252;
}