# ACE Battle Log Analysis Tool

## 프로젝트 개요

이 프로젝트는 "ACE"라는 모바일 카드 게임의 전투 로그를 분석하고 시각화하는 Streamlit 기반 웹 애플리케이션입니다. S3에 저장된 Protobuf 형식의 전투 로그 파일을 다운로드, 압축 해제, 파싱하여 다양한 관점에서 분석 결과를 제공합니다.

## 주요 기능

*   **S3 로그 파일 연동**: AWS S3 버킷에서 직접 로그 파일 목록을 가져와 처리합니다.
*   **Protobuf 파싱**: Protobuf로 직렬화된 전투 로그를 파싱하여 Pandas DataFrame으로 변환합니다.
*   **데이터 시각화**:
    *   **로그 뷰**: 파싱된 로그 데이터를 테이블 형태로 상세히 보여줍니다.
    *   **Mind Map 뷰**: 전투 로그의 전체 구조(라운드 → 행동 → 이벤트)를 인터랙티브한 마인드맵으로 시각화합니다.
    *   **통계 뷰**: 책갈피 조합, 랭킹전 도달 라운드, 챕터 실패 구간, 책갈피/조커 사용 빈도 등 다양한 통계를 제공합니다.
*   **성능 최적화**:
    *   `@st.cache_data`를 활용한 데이터 캐싱
    *   `concurrent.futures`를 이용한 병렬 파일 처리
    *   필요한 데이터만 빠르게 파싱하는 최적화된 함수 사용
    *   zstandard 압축 해제 시 버퍼 크기 조절

## 프로젝트 구조

*   `streamlit_app.py`: 메인 애플리케이션 로직, UI 구성
*   `log_parser.py`: Protobuf 로그 파일 파싱 관련 함수
*   `s3_utils.py`: AWS S3 연동 및 파일 다운로드/압축 해제 함수
*   `ui_components.py`: Streamlit UI 컴포넌트 생성 함수
*   `mindmap_utils.py`: 로그 데이터를 마인드맵용 마크다운으로 변환하는 함수
*   `data_utils.py`: 데이터 처리 관련 유틸리티 함수
*   `config.py`: 프로젝트 설정, 상수, 매핑 데이터 관리
*   `korean_names.py`: 책갈피, 조커 ID를 한글 이름으로 변환
*   `*.proto`: Protobuf 메시지 정의 파일
*   `*_pb2.py`: Protobuf 컴파일 결과물 (Python 코드)
*   `requirements.txt`: 의존성 패키지 목록
*   `ReadMe.txt`: 프로젝트 관련 명령어 모음

## 실행 방법

1.  **의존성 설치**:
    ```bash
    pip install -r requirements.txt
    ```
2.  **Protobuf 컴파일 (필요시)**:
    ```bash
    .\protoc-25.6-win64\bin\protoc.exe --proto_path=. --python_out=. BattleStruct_PROTO.proto
    .\protoc-25.6-win64\bin\protoc.exe --proto_path=. --python_out=. BattleEnumsFull.proto
    ```
3.  **Streamlit 실행**:
    ```bash
    streamlit run streamlit_app.py
    ```

## 주요 의존성

*   `streamlit`: 웹 애플리케이션 프레임워크
*   `pandas`: 데이터 분석 및 처리
*   `boto3`: AWS S3 연동
*   `protobuf`: Protobuf 직렬화/역직렬화
*   `zstandard`: Zstandard 압축 해제
*   `streamlit-markmap`: 마인드맵 시각화
