"""
데이터 유틸리티 모듈

이 모듈은 데이터 처리, 변환, 필터링 등의 공통 유틸리티 함수들을 제공합니다.
"""

import pandas as pd
from typing import Dict, Any, List, Optional, Tuple, Union
from config import (
    FIELD_ICON_RULES,
    PAYLOAD_TYPE_MAPPING,
    LARGE_DATA_THRESHOLD
)


def get_field_icon(field_name: str) -> str:
    """필드 이름에 따라 적절한 아이콘을 반환합니다."""
    if not field_name:
        return FIELD_ICON_RULES["default"]
    
    field_name_lower = field_name.lower()
    
    # 직접 매칭 확인
    for keyword, icon in FIELD_ICON_RULES.items():
        if keyword == "default":
            continue
        if keyword in field_name_lower:
            return icon
    
    return FIELD_ICON_RULES["default"]


def get_active_payload_data(payload_type: str, scalar_values: Dict, reference_values: Dict) -> Tuple[Dict, Dict]:
    """PayloadType에 따라 실제로 사용되는 데이터만 반환합니다."""
    if not payload_type:
        return scalar_values, reference_values
    
    payload_type_str = str(payload_type).upper()
    active_scalar = {}
    active_reference = reference_values.copy()  # reference는 항상 포함
    
    # PayloadType에 따른 scalar 값 필터링
    if payload_type_str in PAYLOAD_TYPE_MAPPING:
        target_field = PAYLOAD_TYPE_MAPPING[payload_type_str]
        if target_field in scalar_values:
            active_scalar[target_field] = scalar_values[target_field]
    
    return active_scalar, active_reference


def filter_empty_data(data: Dict[str, Any], show_empty: bool = True) -> Dict[str, Any]:
    """빈 데이터를 필터링합니다."""
    if show_empty:
        return data
    
    filtered = {}
    for key, value in data.items():
        if value is not None and value != "" and value != [] and value != {}:
            filtered[key] = value
    
    return filtered


def format_data_value(value: Any, field_name: str = "") -> str:
    """데이터 값을 적절한 형식으로 포맷팅합니다."""
    if value is None:
        return "None"
    
    if isinstance(value, bool):
        return "✅" if value else "❌"
    
    if isinstance(value, (int, float)):
        if field_name.lower() in ["money", "dollar", "coin"]:
            return f"💰 {value:,}"
        elif field_name.lower() in ["hp", "health"]:
            return f"❤️ {value:,}"
        elif field_name.lower() in ["damage", "attack"]:
            return f"⚔️ {value:,}"
        else:
            return f"{value:,}" if isinstance(value, int) else f"{value:.2f}"
    
    if isinstance(value, str):
        return value if value else "(빈 문자열)"
    
    if isinstance(value, (list, tuple)):
        return f"[{len(value)}개 항목]" if value else "[]"
    
    if isinstance(value, dict):
        return f"{{{len(value)}개 키}}" if value else "{}"
    
    return str(value)


def organize_files_by_date(files: List[str], log_path_filter: str = "All") -> Dict[str, Dict[str, Dict[str, List[str]]]]:
    """파일 목록을 날짜별로 구조화합니다.

    Args:
        files: 파일 경로 목록
        log_path_filter: "All", "Live", "Dev" 중 하나
                        All = BattleLog + BattleLog_dev
                        Live = BattleLog만
                        Dev = BattleLog_dev만
    """
    date_map = {}
    
    for key in files:
        parts = key.split('/')
        if len(parts) >= 5:
            _, log_path, date_str, log_type = parts[:4]  # Log, BattleLog/BattleLog_dev, 2025_6_25, Rank/Chapter

            # 로그 경로 필터링
            if log_path_filter == "Live" and log_path != 'BattleLog':
                continue
            elif log_path_filter == "Dev" and log_path != 'BattleLog_dev':                
                continue
            elif log_path_filter == "All":
                # BattleLog 또는 BattleLog_dev만 처리
                if log_path not in ['BattleLog', 'BattleLog_dev']:
                    continue
            else:
                # 알 수 없는 필터는 ALL로 처리 (기본값)
                if log_path != 'BattleLog' and log_path != 'BattleLog_dev':
                    continue

            if date_str not in date_map:
                date_map[date_str] = {}
            if log_type not in date_map[date_str]:
                date_map[date_str][log_type] = {}

            if log_type == "Chapter" and len(parts) >= 5:
                # Chapter 파일의 경우 status 추출
                if len(parts) >= 6:
                    status = parts[4]  # SUCCESS 또는 FAIL
                else:
                    status = "ALL"  # 상태 정보가 없는 경우

                if status not in date_map[date_str][log_type]:
                    date_map[date_str][log_type][status] = []
                date_map[date_str][log_type][status].append(key)
            elif log_type == "Rank" and len(parts) >= 5:
                if "ALL" not in date_map[date_str][log_type]:
                    date_map[date_str][log_type]["ALL"] = []
                date_map[date_str][log_type]["ALL"].append(key)
            # Error_Log 및 기타 로그 타입도 처리
            elif len(parts) >= 5:
                if "ALL" not in date_map[date_str][log_type]:
                    date_map[date_str][log_type]["ALL"] = []
                date_map[date_str][log_type]["ALL"].append(key)

    return date_map


def is_large_dataset(df: pd.DataFrame) -> bool:
    """데이터셋이 대용량인지 확인합니다."""
    return len(df) > LARGE_DATA_THRESHOLD


def get_data_summary(df: pd.DataFrame) -> Dict[str, Any]:
    """데이터프레임의 요약 정보를 반환합니다."""
    return {
        "total_rows": len(df),
        "total_columns": len(df.columns),
        "memory_usage": df.memory_usage(deep=True).sum(),
        "is_large": is_large_dataset(df),
        "columns": list(df.columns)
    }


def safe_get_nested_value(data: Dict, path: str, default: Any = None) -> Any:
    """중첩된 딕셔너리에서 안전하게 값을 가져옵니다."""
    try:
        keys = path.split('.')
        current = data
        for key in keys:
            if isinstance(current, dict) and key in current:
                current = current[key]
            else:
                return default
        return current
    except Exception:
        return default


def flatten_dict(data: Dict, parent_key: str = '', sep: str = '.') -> Dict:
    """중첩된 딕셔너리를 평면화합니다."""
    items = []
    for key, value in data.items():
        new_key = f"{parent_key}{sep}{key}" if parent_key else key
        
        if isinstance(value, dict):
            items.extend(flatten_dict(value, new_key, sep=sep).items())
        else:
            items.append((new_key, value))
    
    return dict(items)


def group_data_by_key(data_list: List[Dict], group_key: str) -> Dict[str, List[Dict]]:
    """데이터 리스트를 특정 키로 그룹화합니다."""
    grouped = {}
    for item in data_list:
        key_value = item.get(group_key, 'unknown')
        if key_value not in grouped:
            grouped[key_value] = []
        grouped[key_value].append(item)
    
    return grouped


def filter_data_by_criteria(data: List[Dict], criteria: Dict[str, Any]) -> List[Dict]:
    """조건에 따라 데이터를 필터링합니다."""
    filtered = []
    for item in data:
        match = True
        for key, expected_value in criteria.items():
            if key not in item or item[key] != expected_value:
                match = False
                break
        if match:
            filtered.append(item)
    
    return filtered


def calculate_statistics(values: List[Union[int, float]]) -> Dict[str, float]:
    """숫자 리스트의 통계를 계산합니다."""
    if not values:
        return {}
    
    values = [v for v in values if v is not None]
    if not values:
        return {}
    
    return {
        "count": len(values),
        "sum": sum(values),
        "mean": sum(values) / len(values),
        "min": min(values),
        "max": max(values),
        "median": sorted(values)[len(values) // 2]
    }


def clean_data_for_display(data: Any, max_length: int = 100) -> str:
    """표시용으로 데이터를 정리합니다."""
    if data is None:
        return "None"
    
    str_data = str(data)
    if len(str_data) > max_length:
        return str_data[:max_length] + "..."
    
    return str_data


def validate_data_structure(data: Dict, required_keys: List[str]) -> Tuple[bool, List[str]]:
    """데이터 구조가 유효한지 검증합니다."""
    missing_keys = []
    for key in required_keys:
        if key not in data:
            missing_keys.append(key)
    
    return len(missing_keys) == 0, missing_keys


def merge_data_sources(primary: Dict, secondary: Dict, conflict_resolution: str = 'primary') -> Dict:
    """두 데이터 소스를 병합합니다."""
    merged = secondary.copy() if conflict_resolution == 'secondary' else primary.copy()
    
    other = primary if conflict_resolution == 'secondary' else secondary
    
    for key, value in other.items():
        if key not in merged:
            merged[key] = value
        elif conflict_resolution == 'merge' and isinstance(merged[key], dict) and isinstance(value, dict):
            merged[key] = merge_data_sources(merged[key], value, conflict_resolution)
    
    return merged


def extract_unique_values(data_list: List[Dict], key: str) -> List[Any]:
    """데이터 리스트에서 특정 키의 고유값들을 추출합니다."""
    unique_values = set()
    for item in data_list:
        if key in item and item[key] is not None:
            unique_values.add(item[key])
    
    return sorted(list(unique_values))


def paginate_data(data: List[Any], page: int, page_size: int) -> Tuple[List[Any], Dict[str, int]]:
    """데이터를 페이지네이션합니다."""
    total_items = len(data)
    total_pages = (total_items + page_size - 1) // page_size
    
    start_idx = (page - 1) * page_size
    end_idx = min(start_idx + page_size, total_items)
    
    paginated_data = data[start_idx:end_idx]
    
    pagination_info = {
        "current_page": page,
        "total_pages": total_pages,
        "total_items": total_items,
        "items_per_page": page_size,
        "start_index": start_idx,
        "end_index": end_idx
    }
    
    return paginated_data, pagination_info
