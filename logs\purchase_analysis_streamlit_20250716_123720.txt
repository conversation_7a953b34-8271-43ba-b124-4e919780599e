구매 분석 로그 - 2025-07-16 12:37:20
================================================================================

💾 새로운 구매 분석 시작 (캐시 없음)
📥 S3 파일 다운로드 시작: 72985개 파일
✅ 다운로드 완료: 72985개 파일
🔍 개별 파일 캐시 확인 중...
📋 캐시 확인: 100/72985 파일, 캐시된 파일: 100개
📋 캐시 확인: 200/72985 파일, 캐시된 파일: 200개
📋 캐시 확인: 300/72985 파일, 캐시된 파일: 300개
📋 캐시 확인: 400/72985 파일, 캐시된 파일: 400개
📋 캐시 확인: 500/72985 파일, 캐시된 파일: 500개
📋 캐시 확인: 600/72985 파일, 캐시된 파일: 600개
📋 캐시 확인: 700/72985 파일, 캐시된 파일: 700개
📋 캐시 확인: 800/72985 파일, 캐시된 파일: 800개
📋 캐시 확인: 900/72985 파일, 캐시된 파일: 900개
📋 캐시 확인: 1000/72985 파일, 캐시된 파일: 1000개
📋 캐시 확인: 1100/72985 파일, 캐시된 파일: 1100개
📋 캐시 확인: 1200/72985 파일, 캐시된 파일: 1200개
📋 캐시 확인: 1300/72985 파일, 캐시된 파일: 1300개
📋 캐시 확인: 1400/72985 파일, 캐시된 파일: 1400개
📋 캐시 확인: 1500/72985 파일, 캐시된 파일: 1500개
📋 캐시 확인: 1600/72985 파일, 캐시된 파일: 1600개
📋 캐시 확인: 1700/72985 파일, 캐시된 파일: 1700개
📋 캐시 확인: 1800/72985 파일, 캐시된 파일: 1800개
📋 캐시 확인: 1900/72985 파일, 캐시된 파일: 1900개
📋 캐시 확인: 2000/72985 파일, 캐시된 파일: 2000개
📋 캐시 확인: 2100/72985 파일, 캐시된 파일: 2100개
📋 캐시 확인: 2200/72985 파일, 캐시된 파일: 2200개
📋 캐시 확인: 2300/72985 파일, 캐시된 파일: 2300개
📋 캐시 확인: 2400/72985 파일, 캐시된 파일: 2400개
📋 캐시 확인: 2500/72985 파일, 캐시된 파일: 2500개
📋 캐시 확인: 2600/72985 파일, 캐시된 파일: 2600개
📋 캐시 확인: 2700/72985 파일, 캐시된 파일: 2700개
📋 캐시 확인: 2800/72985 파일, 캐시된 파일: 2800개
📋 캐시 확인: 2900/72985 파일, 캐시된 파일: 2900개
📋 캐시 확인: 3000/72985 파일, 캐시된 파일: 3000개
📋 캐시 확인: 3100/72985 파일, 캐시된 파일: 3100개
📋 캐시 확인: 3200/72985 파일, 캐시된 파일: 3200개
📋 캐시 확인: 3300/72985 파일, 캐시된 파일: 3300개
📋 캐시 확인: 3400/72985 파일, 캐시된 파일: 3400개
📋 캐시 확인: 3500/72985 파일, 캐시된 파일: 3500개
📋 캐시 확인: 3600/72985 파일, 캐시된 파일: 3600개
📋 캐시 확인: 3700/72985 파일, 캐시된 파일: 3700개
📋 캐시 확인: 3800/72985 파일, 캐시된 파일: 3800개
📋 캐시 확인: 3900/72985 파일, 캐시된 파일: 3900개
📋 캐시 확인: 4000/72985 파일, 캐시된 파일: 4000개
📋 캐시 확인: 4100/72985 파일, 캐시된 파일: 4100개
📋 캐시 확인: 4200/72985 파일, 캐시된 파일: 4200개
📋 캐시 확인: 4300/72985 파일, 캐시된 파일: 4300개
📋 캐시 확인: 4400/72985 파일, 캐시된 파일: 4400개
📋 캐시 확인: 4500/72985 파일, 캐시된 파일: 4500개
📋 캐시 확인: 4600/72985 파일, 캐시된 파일: 4600개
📋 캐시 확인: 4700/72985 파일, 캐시된 파일: 4700개
📋 캐시 확인: 4800/72985 파일, 캐시된 파일: 4800개
📋 캐시 확인: 4900/72985 파일, 캐시된 파일: 4900개
📋 캐시 확인: 5000/72985 파일, 캐시된 파일: 5000개
📋 캐시 확인: 5100/72985 파일, 캐시된 파일: 5100개
📋 캐시 확인: 5200/72985 파일, 캐시된 파일: 5200개
📋 캐시 확인: 5300/72985 파일, 캐시된 파일: 5300개
📋 캐시 확인: 5400/72985 파일, 캐시된 파일: 5400개
📋 캐시 확인: 5500/72985 파일, 캐시된 파일: 5500개
📋 캐시 확인: 5600/72985 파일, 캐시된 파일: 5600개
📋 캐시 확인: 5700/72985 파일, 캐시된 파일: 5700개
📋 캐시 확인: 5800/72985 파일, 캐시된 파일: 5800개
📋 캐시 확인: 5900/72985 파일, 캐시된 파일: 5900개
📋 캐시 확인: 6000/72985 파일, 캐시된 파일: 6000개
📋 캐시 확인: 6100/72985 파일, 캐시된 파일: 6100개
📋 캐시 확인: 6200/72985 파일, 캐시된 파일: 6200개
📋 캐시 확인: 6300/72985 파일, 캐시된 파일: 6300개
📋 캐시 확인: 6400/72985 파일, 캐시된 파일: 6400개
📋 캐시 확인: 6500/72985 파일, 캐시된 파일: 6500개
📋 캐시 확인: 6600/72985 파일, 캐시된 파일: 6600개
📋 캐시 확인: 6700/72985 파일, 캐시된 파일: 6700개
📋 캐시 확인: 6800/72985 파일, 캐시된 파일: 6800개
📋 캐시 확인: 6900/72985 파일, 캐시된 파일: 6900개
📋 캐시 확인: 7000/72985 파일, 캐시된 파일: 7000개
📋 캐시 확인: 7100/72985 파일, 캐시된 파일: 7100개
📋 캐시 확인: 7200/72985 파일, 캐시된 파일: 7200개
📋 캐시 확인: 7300/72985 파일, 캐시된 파일: 7300개
📋 캐시 확인: 7400/72985 파일, 캐시된 파일: 7400개
📋 캐시 확인: 7500/72985 파일, 캐시된 파일: 7500개
📋 캐시 확인: 7600/72985 파일, 캐시된 파일: 7600개
📋 캐시 확인: 7700/72985 파일, 캐시된 파일: 7700개
📋 캐시 확인: 7800/72985 파일, 캐시된 파일: 7800개
📋 캐시 확인: 7900/72985 파일, 캐시된 파일: 7900개
📋 캐시 확인: 8000/72985 파일, 캐시된 파일: 8000개
📋 캐시 확인: 8100/72985 파일, 캐시된 파일: 8100개
📋 캐시 확인: 8200/72985 파일, 캐시된 파일: 8200개
📋 캐시 확인: 8300/72985 파일, 캐시된 파일: 8300개
📋 캐시 확인: 8400/72985 파일, 캐시된 파일: 8400개
📋 캐시 확인: 8500/72985 파일, 캐시된 파일: 8500개
📋 캐시 확인: 8600/72985 파일, 캐시된 파일: 8600개
📋 캐시 확인: 8700/72985 파일, 캐시된 파일: 8700개
📋 캐시 확인: 8800/72985 파일, 캐시된 파일: 8800개
📋 캐시 확인: 8900/72985 파일, 캐시된 파일: 8900개
📋 캐시 확인: 9000/72985 파일, 캐시된 파일: 9000개
📋 캐시 확인: 9100/72985 파일, 캐시된 파일: 9100개
📋 캐시 확인: 9200/72985 파일, 캐시된 파일: 9200개
📋 캐시 확인: 9300/72985 파일, 캐시된 파일: 9300개
📋 캐시 확인: 9400/72985 파일, 캐시된 파일: 9400개
📋 캐시 확인: 9500/72985 파일, 캐시된 파일: 9500개
📋 캐시 확인: 9600/72985 파일, 캐시된 파일: 9600개
📋 캐시 확인: 9700/72985 파일, 캐시된 파일: 9700개
📋 캐시 확인: 9800/72985 파일, 캐시된 파일: 9800개
📋 캐시 확인: 9900/72985 파일, 캐시된 파일: 9900개
📋 캐시 확인: 10000/72985 파일, 캐시된 파일: 10000개
📋 캐시 확인: 10100/72985 파일, 캐시된 파일: 10100개
📋 캐시 확인: 10200/72985 파일, 캐시된 파일: 10200개
📋 캐시 확인: 10300/72985 파일, 캐시된 파일: 10300개
📋 캐시 확인: 10400/72985 파일, 캐시된 파일: 10400개
📋 캐시 확인: 10500/72985 파일, 캐시된 파일: 10500개
📋 캐시 확인: 10600/72985 파일, 캐시된 파일: 10600개
📋 캐시 확인: 10700/72985 파일, 캐시된 파일: 10700개
📋 캐시 확인: 10800/72985 파일, 캐시된 파일: 10800개
📋 캐시 확인: 10900/72985 파일, 캐시된 파일: 10900개
📋 캐시 확인: 11000/72985 파일, 캐시된 파일: 11000개
📋 캐시 확인: 11100/72985 파일, 캐시된 파일: 11100개
📋 캐시 확인: 11200/72985 파일, 캐시된 파일: 11200개
📋 캐시 확인: 11300/72985 파일, 캐시된 파일: 11300개
📋 캐시 확인: 11400/72985 파일, 캐시된 파일: 11400개
📋 캐시 확인: 11500/72985 파일, 캐시된 파일: 11500개
📋 캐시 확인: 11600/72985 파일, 캐시된 파일: 11600개
📋 캐시 확인: 11700/72985 파일, 캐시된 파일: 11700개
📋 캐시 확인: 11800/72985 파일, 캐시된 파일: 11800개
📋 캐시 확인: 11900/72985 파일, 캐시된 파일: 11900개
📋 캐시 확인: 12000/72985 파일, 캐시된 파일: 12000개
📋 캐시 확인: 12100/72985 파일, 캐시된 파일: 12100개
📋 캐시 확인: 12200/72985 파일, 캐시된 파일: 12200개
📋 캐시 확인: 12300/72985 파일, 캐시된 파일: 12300개
📋 캐시 확인: 12400/72985 파일, 캐시된 파일: 12400개
📋 캐시 확인: 12500/72985 파일, 캐시된 파일: 12500개
📋 캐시 확인: 12600/72985 파일, 캐시된 파일: 12600개
📋 캐시 확인: 12700/72985 파일, 캐시된 파일: 12700개
📋 캐시 확인: 12800/72985 파일, 캐시된 파일: 12800개
📋 캐시 확인: 12900/72985 파일, 캐시된 파일: 12900개
📋 캐시 확인: 13000/72985 파일, 캐시된 파일: 13000개
📋 캐시 확인: 13100/72985 파일, 캐시된 파일: 13100개
📋 캐시 확인: 13200/72985 파일, 캐시된 파일: 13200개
📋 캐시 확인: 13300/72985 파일, 캐시된 파일: 13300개
📋 캐시 확인: 13400/72985 파일, 캐시된 파일: 13400개
📋 캐시 확인: 13500/72985 파일, 캐시된 파일: 13500개
📋 캐시 확인: 13600/72985 파일, 캐시된 파일: 13600개
📋 캐시 확인: 13700/72985 파일, 캐시된 파일: 13700개
📋 캐시 확인: 13800/72985 파일, 캐시된 파일: 13800개
📋 캐시 확인: 13900/72985 파일, 캐시된 파일: 13900개
📋 캐시 확인: 14000/72985 파일, 캐시된 파일: 14000개
📋 캐시 확인: 14100/72985 파일, 캐시된 파일: 14100개
📋 캐시 확인: 14200/72985 파일, 캐시된 파일: 14200개
📋 캐시 확인: 14300/72985 파일, 캐시된 파일: 14300개
📋 캐시 확인: 14400/72985 파일, 캐시된 파일: 14400개
📋 캐시 확인: 14500/72985 파일, 캐시된 파일: 14500개
📋 캐시 확인: 14600/72985 파일, 캐시된 파일: 14600개
📋 캐시 확인: 14700/72985 파일, 캐시된 파일: 14700개
📋 캐시 확인: 14800/72985 파일, 캐시된 파일: 14800개
📋 캐시 확인: 14900/72985 파일, 캐시된 파일: 14900개
📋 캐시 확인: 15000/72985 파일, 캐시된 파일: 15000개
📋 캐시 확인: 15100/72985 파일, 캐시된 파일: 15100개
📋 캐시 확인: 15200/72985 파일, 캐시된 파일: 15200개
📋 캐시 확인: 15300/72985 파일, 캐시된 파일: 15300개
📋 캐시 확인: 15400/72985 파일, 캐시된 파일: 15400개
📋 캐시 확인: 15500/72985 파일, 캐시된 파일: 15500개
📋 캐시 확인: 15600/72985 파일, 캐시된 파일: 15600개
📋 캐시 확인: 15700/72985 파일, 캐시된 파일: 15700개
📋 캐시 확인: 15800/72985 파일, 캐시된 파일: 15800개
📋 캐시 확인: 15900/72985 파일, 캐시된 파일: 15900개
📋 캐시 확인: 16000/72985 파일, 캐시된 파일: 16000개
📋 캐시 확인: 16100/72985 파일, 캐시된 파일: 16100개
📋 캐시 확인: 16200/72985 파일, 캐시된 파일: 16200개
📋 캐시 확인: 16300/72985 파일, 캐시된 파일: 16300개
📋 캐시 확인: 16400/72985 파일, 캐시된 파일: 16400개
📋 캐시 확인: 16500/72985 파일, 캐시된 파일: 16500개
📋 캐시 확인: 16600/72985 파일, 캐시된 파일: 16600개
📋 캐시 확인: 16700/72985 파일, 캐시된 파일: 16700개
📋 캐시 확인: 16800/72985 파일, 캐시된 파일: 16800개
📋 캐시 확인: 16900/72985 파일, 캐시된 파일: 16900개
📋 캐시 확인: 17000/72985 파일, 캐시된 파일: 17000개
📋 캐시 확인: 17100/72985 파일, 캐시된 파일: 17100개
📋 캐시 확인: 17200/72985 파일, 캐시된 파일: 17200개
📋 캐시 확인: 17300/72985 파일, 캐시된 파일: 17300개
📋 캐시 확인: 17400/72985 파일, 캐시된 파일: 17400개
📋 캐시 확인: 17500/72985 파일, 캐시된 파일: 17500개
📋 캐시 확인: 17600/72985 파일, 캐시된 파일: 17600개
📋 캐시 확인: 17700/72985 파일, 캐시된 파일: 17700개
📋 캐시 확인: 17800/72985 파일, 캐시된 파일: 17800개
📋 캐시 확인: 17900/72985 파일, 캐시된 파일: 17900개
📋 캐시 확인: 18000/72985 파일, 캐시된 파일: 18000개
📋 캐시 확인: 18100/72985 파일, 캐시된 파일: 18100개
📋 캐시 확인: 18200/72985 파일, 캐시된 파일: 18200개
📋 캐시 확인: 18300/72985 파일, 캐시된 파일: 18300개
📋 캐시 확인: 18400/72985 파일, 캐시된 파일: 18400개
📋 캐시 확인: 18500/72985 파일, 캐시된 파일: 18500개
📋 캐시 확인: 18600/72985 파일, 캐시된 파일: 18600개
📋 캐시 확인: 18700/72985 파일, 캐시된 파일: 18700개
📋 캐시 확인: 18800/72985 파일, 캐시된 파일: 18800개
📋 캐시 확인: 18900/72985 파일, 캐시된 파일: 18900개
📋 캐시 확인: 19000/72985 파일, 캐시된 파일: 19000개
📋 캐시 확인: 19100/72985 파일, 캐시된 파일: 19100개
📋 캐시 확인: 19200/72985 파일, 캐시된 파일: 19200개
📋 캐시 확인: 19300/72985 파일, 캐시된 파일: 19300개
📋 캐시 확인: 19400/72985 파일, 캐시된 파일: 19400개
📋 캐시 확인: 19500/72985 파일, 캐시된 파일: 19500개
📋 캐시 확인: 19600/72985 파일, 캐시된 파일: 19600개
📋 캐시 확인: 19700/72985 파일, 캐시된 파일: 19700개
📋 캐시 확인: 19800/72985 파일, 캐시된 파일: 19800개
📋 캐시 확인: 19900/72985 파일, 캐시된 파일: 19900개
📋 캐시 확인: 20000/72985 파일, 캐시된 파일: 20000개
📋 캐시 확인: 20100/72985 파일, 캐시된 파일: 20100개
📋 캐시 확인: 20200/72985 파일, 캐시된 파일: 20200개
📋 캐시 확인: 20300/72985 파일, 캐시된 파일: 20300개
📋 캐시 확인: 20400/72985 파일, 캐시된 파일: 20400개
📋 캐시 확인: 20500/72985 파일, 캐시된 파일: 20500개
📋 캐시 확인: 20600/72985 파일, 캐시된 파일: 20600개
📋 캐시 확인: 20700/72985 파일, 캐시된 파일: 20700개
📋 캐시 확인: 20800/72985 파일, 캐시된 파일: 20800개
📋 캐시 확인: 20900/72985 파일, 캐시된 파일: 20900개
📋 캐시 확인: 21000/72985 파일, 캐시된 파일: 21000개
📋 캐시 확인: 21100/72985 파일, 캐시된 파일: 21100개
📋 캐시 확인: 21200/72985 파일, 캐시된 파일: 21200개
📋 캐시 확인: 21300/72985 파일, 캐시된 파일: 21300개
📋 캐시 확인: 21400/72985 파일, 캐시된 파일: 21400개
📋 캐시 확인: 21500/72985 파일, 캐시된 파일: 21500개
📋 캐시 확인: 21600/72985 파일, 캐시된 파일: 21600개
📋 캐시 확인: 21700/72985 파일, 캐시된 파일: 21700개
📋 캐시 확인: 21800/72985 파일, 캐시된 파일: 21800개
📋 캐시 확인: 21900/72985 파일, 캐시된 파일: 21900개
📋 캐시 확인: 22000/72985 파일, 캐시된 파일: 22000개
📋 캐시 확인: 22100/72985 파일, 캐시된 파일: 22100개
📋 캐시 확인: 22200/72985 파일, 캐시된 파일: 22200개
📋 캐시 확인: 22300/72985 파일, 캐시된 파일: 22300개
📋 캐시 확인: 22400/72985 파일, 캐시된 파일: 22400개
📋 캐시 확인: 22500/72985 파일, 캐시된 파일: 22500개
📋 캐시 확인: 22600/72985 파일, 캐시된 파일: 22600개
📋 캐시 확인: 22700/72985 파일, 캐시된 파일: 22700개
📋 캐시 확인: 22800/72985 파일, 캐시된 파일: 22800개
📋 캐시 확인: 22900/72985 파일, 캐시된 파일: 22900개
📋 캐시 확인: 23000/72985 파일, 캐시된 파일: 23000개
📋 캐시 확인: 23100/72985 파일, 캐시된 파일: 23100개
📋 캐시 확인: 23200/72985 파일, 캐시된 파일: 23200개
📋 캐시 확인: 23300/72985 파일, 캐시된 파일: 23300개
📋 캐시 확인: 23400/72985 파일, 캐시된 파일: 23400개
📋 캐시 확인: 23500/72985 파일, 캐시된 파일: 23500개
📋 캐시 확인: 23600/72985 파일, 캐시된 파일: 23600개
📋 캐시 확인: 23700/72985 파일, 캐시된 파일: 23700개
📋 캐시 확인: 23800/72985 파일, 캐시된 파일: 23800개
📋 캐시 확인: 23900/72985 파일, 캐시된 파일: 23900개
📋 캐시 확인: 24000/72985 파일, 캐시된 파일: 24000개
📋 캐시 확인: 24100/72985 파일, 캐시된 파일: 24100개
📋 캐시 확인: 24200/72985 파일, 캐시된 파일: 24200개
📋 캐시 확인: 24300/72985 파일, 캐시된 파일: 24300개
📋 캐시 확인: 24400/72985 파일, 캐시된 파일: 24400개
📋 캐시 확인: 24500/72985 파일, 캐시된 파일: 24500개
📋 캐시 확인: 24600/72985 파일, 캐시된 파일: 24600개
📋 캐시 확인: 24700/72985 파일, 캐시된 파일: 24700개
📋 캐시 확인: 24800/72985 파일, 캐시된 파일: 24800개
📋 캐시 확인: 24900/72985 파일, 캐시된 파일: 24900개
📋 캐시 확인: 25000/72985 파일, 캐시된 파일: 25000개
📋 캐시 확인: 25100/72985 파일, 캐시된 파일: 25100개
📋 캐시 확인: 25200/72985 파일, 캐시된 파일: 25200개
📋 캐시 확인: 25300/72985 파일, 캐시된 파일: 25300개
📋 캐시 확인: 25400/72985 파일, 캐시된 파일: 25400개
📋 캐시 확인: 25500/72985 파일, 캐시된 파일: 25500개
📋 캐시 확인: 25600/72985 파일, 캐시된 파일: 25600개
📋 캐시 확인: 25700/72985 파일, 캐시된 파일: 25700개
📋 캐시 확인: 25800/72985 파일, 캐시된 파일: 25800개
📋 캐시 확인: 25900/72985 파일, 캐시된 파일: 25900개
📋 캐시 확인: 26000/72985 파일, 캐시된 파일: 26000개
📋 캐시 확인: 26100/72985 파일, 캐시된 파일: 26100개
📋 캐시 확인: 26200/72985 파일, 캐시된 파일: 26200개
📋 캐시 확인: 26300/72985 파일, 캐시된 파일: 26300개
📋 캐시 확인: 26400/72985 파일, 캐시된 파일: 26400개
📋 캐시 확인: 26500/72985 파일, 캐시된 파일: 26500개
📋 캐시 확인: 26600/72985 파일, 캐시된 파일: 26600개
📋 캐시 확인: 26700/72985 파일, 캐시된 파일: 26700개
📋 캐시 확인: 26800/72985 파일, 캐시된 파일: 26800개
📋 캐시 확인: 26900/72985 파일, 캐시된 파일: 26900개
📋 캐시 확인: 27000/72985 파일, 캐시된 파일: 27000개
📋 캐시 확인: 27100/72985 파일, 캐시된 파일: 27100개
📋 캐시 확인: 27200/72985 파일, 캐시된 파일: 27200개
📋 캐시 확인: 27300/72985 파일, 캐시된 파일: 27300개
📋 캐시 확인: 27400/72985 파일, 캐시된 파일: 27400개
📋 캐시 확인: 27500/72985 파일, 캐시된 파일: 27500개
📋 캐시 확인: 27600/72985 파일, 캐시된 파일: 27600개
📋 캐시 확인: 27700/72985 파일, 캐시된 파일: 27700개
📋 캐시 확인: 27800/72985 파일, 캐시된 파일: 27800개
📋 캐시 확인: 27900/72985 파일, 캐시된 파일: 27900개
📋 캐시 확인: 28000/72985 파일, 캐시된 파일: 28000개
📋 캐시 확인: 28100/72985 파일, 캐시된 파일: 28100개
📋 캐시 확인: 28200/72985 파일, 캐시된 파일: 28200개
📋 캐시 확인: 28300/72985 파일, 캐시된 파일: 28300개
📋 캐시 확인: 28400/72985 파일, 캐시된 파일: 28400개
📋 캐시 확인: 28500/72985 파일, 캐시된 파일: 28500개
📋 캐시 확인: 28600/72985 파일, 캐시된 파일: 28600개
📋 캐시 확인: 28700/72985 파일, 캐시된 파일: 28700개
📋 캐시 확인: 28800/72985 파일, 캐시된 파일: 28800개
📋 캐시 확인: 28900/72985 파일, 캐시된 파일: 28900개
📋 캐시 확인: 29000/72985 파일, 캐시된 파일: 29000개
📋 캐시 확인: 29100/72985 파일, 캐시된 파일: 29100개
📋 캐시 확인: 29200/72985 파일, 캐시된 파일: 29200개
📋 캐시 확인: 29300/72985 파일, 캐시된 파일: 29300개
📋 캐시 확인: 29400/72985 파일, 캐시된 파일: 29400개
📋 캐시 확인: 29500/72985 파일, 캐시된 파일: 29500개
📋 캐시 확인: 29600/72985 파일, 캐시된 파일: 29600개
📋 캐시 확인: 29700/72985 파일, 캐시된 파일: 29700개
📋 캐시 확인: 29800/72985 파일, 캐시된 파일: 29800개
📋 캐시 확인: 29900/72985 파일, 캐시된 파일: 29900개
📋 캐시 확인: 30000/72985 파일, 캐시된 파일: 30000개
📋 캐시 확인: 30100/72985 파일, 캐시된 파일: 30100개
📋 캐시 확인: 30200/72985 파일, 캐시된 파일: 30200개
📋 캐시 확인: 30300/72985 파일, 캐시된 파일: 30300개
📋 캐시 확인: 30400/72985 파일, 캐시된 파일: 30400개
📋 캐시 확인: 30500/72985 파일, 캐시된 파일: 30500개
📋 캐시 확인: 30600/72985 파일, 캐시된 파일: 30600개
📋 캐시 확인: 30700/72985 파일, 캐시된 파일: 30700개
📋 캐시 확인: 30800/72985 파일, 캐시된 파일: 30800개
📋 캐시 확인: 30900/72985 파일, 캐시된 파일: 30900개
📋 캐시 확인: 31000/72985 파일, 캐시된 파일: 31000개
📋 캐시 확인: 31100/72985 파일, 캐시된 파일: 31100개
📋 캐시 확인: 31200/72985 파일, 캐시된 파일: 31200개
📋 캐시 확인: 31300/72985 파일, 캐시된 파일: 31300개
📋 캐시 확인: 31400/72985 파일, 캐시된 파일: 31400개
📋 캐시 확인: 31500/72985 파일, 캐시된 파일: 31500개
📋 캐시 확인: 31600/72985 파일, 캐시된 파일: 31600개
📋 캐시 확인: 31700/72985 파일, 캐시된 파일: 31700개
📋 캐시 확인: 31800/72985 파일, 캐시된 파일: 31800개
📋 캐시 확인: 31900/72985 파일, 캐시된 파일: 31900개
📋 캐시 확인: 32000/72985 파일, 캐시된 파일: 32000개
📋 캐시 확인: 32100/72985 파일, 캐시된 파일: 32100개
📋 캐시 확인: 32200/72985 파일, 캐시된 파일: 32200개
📋 캐시 확인: 32300/72985 파일, 캐시된 파일: 32300개
📋 캐시 확인: 32400/72985 파일, 캐시된 파일: 32400개
📋 캐시 확인: 32500/72985 파일, 캐시된 파일: 32500개
📋 캐시 확인: 32600/72985 파일, 캐시된 파일: 32600개
📋 캐시 확인: 32700/72985 파일, 캐시된 파일: 32700개
📋 캐시 확인: 32800/72985 파일, 캐시된 파일: 32800개
📋 캐시 확인: 32900/72985 파일, 캐시된 파일: 32900개
📋 캐시 확인: 33000/72985 파일, 캐시된 파일: 33000개
📋 캐시 확인: 33100/72985 파일, 캐시된 파일: 33100개
📋 캐시 확인: 33200/72985 파일, 캐시된 파일: 33200개
📋 캐시 확인: 33300/72985 파일, 캐시된 파일: 33300개
📋 캐시 확인: 33400/72985 파일, 캐시된 파일: 33400개
📋 캐시 확인: 33500/72985 파일, 캐시된 파일: 33500개
📋 캐시 확인: 33600/72985 파일, 캐시된 파일: 33600개
📋 캐시 확인: 33700/72985 파일, 캐시된 파일: 33700개
📋 캐시 확인: 33800/72985 파일, 캐시된 파일: 33800개
📋 캐시 확인: 33900/72985 파일, 캐시된 파일: 33900개
📋 캐시 확인: 34000/72985 파일, 캐시된 파일: 34000개
📋 캐시 확인: 34100/72985 파일, 캐시된 파일: 34100개
📋 캐시 확인: 34200/72985 파일, 캐시된 파일: 34200개
📋 캐시 확인: 34300/72985 파일, 캐시된 파일: 34300개
📋 캐시 확인: 34400/72985 파일, 캐시된 파일: 34400개
📋 캐시 확인: 34500/72985 파일, 캐시된 파일: 34500개
📋 캐시 확인: 34600/72985 파일, 캐시된 파일: 34600개
📋 캐시 확인: 34700/72985 파일, 캐시된 파일: 34700개
📋 캐시 확인: 34800/72985 파일, 캐시된 파일: 34800개
📋 캐시 확인: 34900/72985 파일, 캐시된 파일: 34900개
📋 캐시 확인: 35000/72985 파일, 캐시된 파일: 35000개
📋 캐시 확인: 35100/72985 파일, 캐시된 파일: 35100개
📋 캐시 확인: 35200/72985 파일, 캐시된 파일: 35200개
📋 캐시 확인: 35300/72985 파일, 캐시된 파일: 35300개
📋 캐시 확인: 35400/72985 파일, 캐시된 파일: 35400개
📋 캐시 확인: 35500/72985 파일, 캐시된 파일: 35500개
📋 캐시 확인: 35600/72985 파일, 캐시된 파일: 35600개
📋 캐시 확인: 35700/72985 파일, 캐시된 파일: 35700개
📋 캐시 확인: 35800/72985 파일, 캐시된 파일: 35800개
📋 캐시 확인: 35900/72985 파일, 캐시된 파일: 35900개
📋 캐시 확인: 36000/72985 파일, 캐시된 파일: 36000개
📋 캐시 확인: 36100/72985 파일, 캐시된 파일: 36100개
📋 캐시 확인: 36200/72985 파일, 캐시된 파일: 36200개
📋 캐시 확인: 36300/72985 파일, 캐시된 파일: 36300개
📋 캐시 확인: 36400/72985 파일, 캐시된 파일: 36400개
📋 캐시 확인: 36500/72985 파일, 캐시된 파일: 36500개
📋 캐시 확인: 36600/72985 파일, 캐시된 파일: 36600개
📋 캐시 확인: 36700/72985 파일, 캐시된 파일: 36700개
📋 캐시 확인: 36800/72985 파일, 캐시된 파일: 36800개
📋 캐시 확인: 36900/72985 파일, 캐시된 파일: 36900개
📋 캐시 확인: 37000/72985 파일, 캐시된 파일: 37000개
📋 캐시 확인: 37100/72985 파일, 캐시된 파일: 37100개
📋 캐시 확인: 37200/72985 파일, 캐시된 파일: 37200개
📋 캐시 확인: 37300/72985 파일, 캐시된 파일: 37300개
📋 캐시 확인: 37400/72985 파일, 캐시된 파일: 37400개
📋 캐시 확인: 37500/72985 파일, 캐시된 파일: 37500개
📋 캐시 확인: 37600/72985 파일, 캐시된 파일: 37600개
📋 캐시 확인: 37700/72985 파일, 캐시된 파일: 37700개
📋 캐시 확인: 37800/72985 파일, 캐시된 파일: 37800개
📋 캐시 확인: 37900/72985 파일, 캐시된 파일: 37900개
📋 캐시 확인: 38000/72985 파일, 캐시된 파일: 38000개
📋 캐시 확인: 38100/72985 파일, 캐시된 파일: 38100개
📋 캐시 확인: 38200/72985 파일, 캐시된 파일: 38200개
📋 캐시 확인: 38300/72985 파일, 캐시된 파일: 38300개
📋 캐시 확인: 38400/72985 파일, 캐시된 파일: 38400개
📋 캐시 확인: 38500/72985 파일, 캐시된 파일: 38500개
📋 캐시 확인: 38600/72985 파일, 캐시된 파일: 38600개
📋 캐시 확인: 38700/72985 파일, 캐시된 파일: 38700개
📋 캐시 확인: 38800/72985 파일, 캐시된 파일: 38800개
📋 캐시 확인: 38900/72985 파일, 캐시된 파일: 38900개
📋 캐시 확인: 39000/72985 파일, 캐시된 파일: 39000개
📋 캐시 확인: 39100/72985 파일, 캐시된 파일: 39100개
📋 캐시 확인: 39200/72985 파일, 캐시된 파일: 39200개
📋 캐시 확인: 39300/72985 파일, 캐시된 파일: 39300개
📋 캐시 확인: 39400/72985 파일, 캐시된 파일: 39400개
📋 캐시 확인: 39500/72985 파일, 캐시된 파일: 39500개
📋 캐시 확인: 39600/72985 파일, 캐시된 파일: 39600개
📋 캐시 확인: 39700/72985 파일, 캐시된 파일: 39700개
📋 캐시 확인: 39800/72985 파일, 캐시된 파일: 39800개
📋 캐시 확인: 39900/72985 파일, 캐시된 파일: 39900개
📋 캐시 확인: 40000/72985 파일, 캐시된 파일: 40000개
📋 캐시 확인: 40100/72985 파일, 캐시된 파일: 40100개
📋 캐시 확인: 40200/72985 파일, 캐시된 파일: 40200개
📋 캐시 확인: 40300/72985 파일, 캐시된 파일: 40300개
📋 캐시 확인: 40400/72985 파일, 캐시된 파일: 40400개
📋 캐시 확인: 40500/72985 파일, 캐시된 파일: 40500개
📋 캐시 확인: 40600/72985 파일, 캐시된 파일: 40600개
📋 캐시 확인: 40700/72985 파일, 캐시된 파일: 40700개
📋 캐시 확인: 40800/72985 파일, 캐시된 파일: 40800개
📋 캐시 확인: 40900/72985 파일, 캐시된 파일: 40900개
📋 캐시 확인: 41000/72985 파일, 캐시된 파일: 41000개
📋 캐시 확인: 41100/72985 파일, 캐시된 파일: 41100개
📋 캐시 확인: 41200/72985 파일, 캐시된 파일: 41200개
📋 캐시 확인: 41300/72985 파일, 캐시된 파일: 41300개
📋 캐시 확인: 41400/72985 파일, 캐시된 파일: 41400개
📋 캐시 확인: 41500/72985 파일, 캐시된 파일: 41500개
📋 캐시 확인: 41600/72985 파일, 캐시된 파일: 41600개
📋 캐시 확인: 41700/72985 파일, 캐시된 파일: 41700개
📋 캐시 확인: 41800/72985 파일, 캐시된 파일: 41800개
📋 캐시 확인: 41900/72985 파일, 캐시된 파일: 41900개
📋 캐시 확인: 42000/72985 파일, 캐시된 파일: 42000개
📋 캐시 확인: 42100/72985 파일, 캐시된 파일: 42100개
📋 캐시 확인: 42200/72985 파일, 캐시된 파일: 42200개
📋 캐시 확인: 42300/72985 파일, 캐시된 파일: 42300개
📋 캐시 확인: 42400/72985 파일, 캐시된 파일: 42400개
📋 캐시 확인: 42500/72985 파일, 캐시된 파일: 42500개
📋 캐시 확인: 42600/72985 파일, 캐시된 파일: 42600개
📋 캐시 확인: 42700/72985 파일, 캐시된 파일: 42700개
📋 캐시 확인: 42800/72985 파일, 캐시된 파일: 42800개
📋 캐시 확인: 42900/72985 파일, 캐시된 파일: 42900개
📋 캐시 확인: 43000/72985 파일, 캐시된 파일: 43000개
📋 캐시 확인: 43100/72985 파일, 캐시된 파일: 43100개
📋 캐시 확인: 43200/72985 파일, 캐시된 파일: 43200개
📋 캐시 확인: 43300/72985 파일, 캐시된 파일: 43300개
📋 캐시 확인: 43400/72985 파일, 캐시된 파일: 43400개
📋 캐시 확인: 43500/72985 파일, 캐시된 파일: 43500개
📋 캐시 확인: 43600/72985 파일, 캐시된 파일: 43600개
📋 캐시 확인: 43700/72985 파일, 캐시된 파일: 43700개
📋 캐시 확인: 43800/72985 파일, 캐시된 파일: 43800개
📋 캐시 확인: 43900/72985 파일, 캐시된 파일: 43900개
📋 캐시 확인: 44000/72985 파일, 캐시된 파일: 44000개
📋 캐시 확인: 44100/72985 파일, 캐시된 파일: 44100개
📋 캐시 확인: 44200/72985 파일, 캐시된 파일: 44200개
📋 캐시 확인: 44300/72985 파일, 캐시된 파일: 44300개
📋 캐시 확인: 44400/72985 파일, 캐시된 파일: 44400개
📋 캐시 확인: 44500/72985 파일, 캐시된 파일: 44500개
📋 캐시 확인: 44600/72985 파일, 캐시된 파일: 44600개
📋 캐시 확인: 44700/72985 파일, 캐시된 파일: 44700개
📋 캐시 확인: 44800/72985 파일, 캐시된 파일: 44800개
📋 캐시 확인: 44900/72985 파일, 캐시된 파일: 44900개
📋 캐시 확인: 45000/72985 파일, 캐시된 파일: 45000개
📋 캐시 확인: 45100/72985 파일, 캐시된 파일: 45100개
📋 캐시 확인: 45200/72985 파일, 캐시된 파일: 45200개
📋 캐시 확인: 45300/72985 파일, 캐시된 파일: 45300개
📋 캐시 확인: 45400/72985 파일, 캐시된 파일: 45400개
📋 캐시 확인: 45500/72985 파일, 캐시된 파일: 45500개
📋 캐시 확인: 45600/72985 파일, 캐시된 파일: 45600개
📋 캐시 확인: 45700/72985 파일, 캐시된 파일: 45700개
📋 캐시 확인: 45800/72985 파일, 캐시된 파일: 45800개
📋 캐시 확인: 45900/72985 파일, 캐시된 파일: 45900개
📋 캐시 확인: 46000/72985 파일, 캐시된 파일: 46000개
📋 캐시 확인: 46100/72985 파일, 캐시된 파일: 46100개
📋 캐시 확인: 46200/72985 파일, 캐시된 파일: 46200개
📋 캐시 확인: 46300/72985 파일, 캐시된 파일: 46300개
📋 캐시 확인: 46400/72985 파일, 캐시된 파일: 46400개
📋 캐시 확인: 46500/72985 파일, 캐시된 파일: 46500개
📋 캐시 확인: 46600/72985 파일, 캐시된 파일: 46600개
📋 캐시 확인: 46700/72985 파일, 캐시된 파일: 46700개
📋 캐시 확인: 46800/72985 파일, 캐시된 파일: 46800개
📋 캐시 확인: 46900/72985 파일, 캐시된 파일: 46900개
📋 캐시 확인: 47000/72985 파일, 캐시된 파일: 47000개
📋 캐시 확인: 47100/72985 파일, 캐시된 파일: 47100개
📋 캐시 확인: 47200/72985 파일, 캐시된 파일: 47200개
📋 캐시 확인: 47300/72985 파일, 캐시된 파일: 47300개
📋 캐시 확인: 47400/72985 파일, 캐시된 파일: 47400개
📋 캐시 확인: 47500/72985 파일, 캐시된 파일: 47500개
📋 캐시 확인: 47600/72985 파일, 캐시된 파일: 47600개
📋 캐시 확인: 47700/72985 파일, 캐시된 파일: 47700개
📋 캐시 확인: 47800/72985 파일, 캐시된 파일: 47800개
📋 캐시 확인: 47900/72985 파일, 캐시된 파일: 47900개
📋 캐시 확인: 48000/72985 파일, 캐시된 파일: 48000개
📋 캐시 확인: 48100/72985 파일, 캐시된 파일: 48100개
📋 캐시 확인: 48200/72985 파일, 캐시된 파일: 48200개
📋 캐시 확인: 48300/72985 파일, 캐시된 파일: 48300개
📋 캐시 확인: 48400/72985 파일, 캐시된 파일: 48400개
📋 캐시 확인: 48500/72985 파일, 캐시된 파일: 48500개
📋 캐시 확인: 48600/72985 파일, 캐시된 파일: 48600개
📋 캐시 확인: 48700/72985 파일, 캐시된 파일: 48700개
📋 캐시 확인: 48800/72985 파일, 캐시된 파일: 48800개
📋 캐시 확인: 48900/72985 파일, 캐시된 파일: 48900개
📋 캐시 확인: 49000/72985 파일, 캐시된 파일: 49000개
📋 캐시 확인: 49100/72985 파일, 캐시된 파일: 49100개
📋 캐시 확인: 49200/72985 파일, 캐시된 파일: 49200개
📋 캐시 확인: 49300/72985 파일, 캐시된 파일: 49300개
📋 캐시 확인: 49400/72985 파일, 캐시된 파일: 49400개
📋 캐시 확인: 49500/72985 파일, 캐시된 파일: 49500개
📋 캐시 확인: 49600/72985 파일, 캐시된 파일: 49600개
📋 캐시 확인: 49700/72985 파일, 캐시된 파일: 49700개
📋 캐시 확인: 49800/72985 파일, 캐시된 파일: 49800개
📋 캐시 확인: 49900/72985 파일, 캐시된 파일: 49900개
📋 캐시 확인: 50000/72985 파일, 캐시된 파일: 50000개
📋 캐시 확인: 50100/72985 파일, 캐시된 파일: 50100개
📋 캐시 확인: 50200/72985 파일, 캐시된 파일: 50200개
📋 캐시 확인: 50300/72985 파일, 캐시된 파일: 50300개
📋 캐시 확인: 50400/72985 파일, 캐시된 파일: 50400개
📋 캐시 확인: 50500/72985 파일, 캐시된 파일: 50500개
📋 캐시 확인: 50600/72985 파일, 캐시된 파일: 50600개
📋 캐시 확인: 50700/72985 파일, 캐시된 파일: 50700개
📋 캐시 확인: 50800/72985 파일, 캐시된 파일: 50800개
📋 캐시 확인: 50900/72985 파일, 캐시된 파일: 50900개
📋 캐시 확인: 51000/72985 파일, 캐시된 파일: 51000개
📋 캐시 확인: 51100/72985 파일, 캐시된 파일: 51100개
📋 캐시 확인: 51200/72985 파일, 캐시된 파일: 51200개
📋 캐시 확인: 51300/72985 파일, 캐시된 파일: 51300개
📋 캐시 확인: 51400/72985 파일, 캐시된 파일: 51400개
📋 캐시 확인: 51500/72985 파일, 캐시된 파일: 51500개
📋 캐시 확인: 51600/72985 파일, 캐시된 파일: 51600개
📋 캐시 확인: 51700/72985 파일, 캐시된 파일: 51700개
📋 캐시 확인: 51800/72985 파일, 캐시된 파일: 51800개
📋 캐시 확인: 51900/72985 파일, 캐시된 파일: 51900개
📋 캐시 확인: 52000/72985 파일, 캐시된 파일: 52000개
📋 캐시 확인: 52100/72985 파일, 캐시된 파일: 52100개
📋 캐시 확인: 52200/72985 파일, 캐시된 파일: 52200개
📋 캐시 확인: 52300/72985 파일, 캐시된 파일: 52300개
📋 캐시 확인: 52400/72985 파일, 캐시된 파일: 52400개
📋 캐시 확인: 52500/72985 파일, 캐시된 파일: 52500개
📋 캐시 확인: 52600/72985 파일, 캐시된 파일: 52600개
📋 캐시 확인: 52700/72985 파일, 캐시된 파일: 52700개
📋 캐시 확인: 52800/72985 파일, 캐시된 파일: 52800개
📋 캐시 확인: 52900/72985 파일, 캐시된 파일: 52900개
📋 캐시 확인: 53000/72985 파일, 캐시된 파일: 53000개
📋 캐시 확인: 53100/72985 파일, 캐시된 파일: 53100개
📋 캐시 확인: 53200/72985 파일, 캐시된 파일: 53200개
📋 캐시 확인: 53300/72985 파일, 캐시된 파일: 53300개
📋 캐시 확인: 53400/72985 파일, 캐시된 파일: 53400개
📋 캐시 확인: 53500/72985 파일, 캐시된 파일: 53500개
📋 캐시 확인: 53600/72985 파일, 캐시된 파일: 53600개
📋 캐시 확인: 53700/72985 파일, 캐시된 파일: 53700개
📋 캐시 확인: 53800/72985 파일, 캐시된 파일: 53800개
📋 캐시 확인: 53900/72985 파일, 캐시된 파일: 53900개
📋 캐시 확인: 54000/72985 파일, 캐시된 파일: 54000개
📋 캐시 확인: 54100/72985 파일, 캐시된 파일: 54100개
📋 캐시 확인: 54200/72985 파일, 캐시된 파일: 54200개
📋 캐시 확인: 54300/72985 파일, 캐시된 파일: 54300개
📋 캐시 확인: 54400/72985 파일, 캐시된 파일: 54400개
📋 캐시 확인: 54500/72985 파일, 캐시된 파일: 54500개
📋 캐시 확인: 54600/72985 파일, 캐시된 파일: 54600개
📋 캐시 확인: 54700/72985 파일, 캐시된 파일: 54700개
📋 캐시 확인: 54800/72985 파일, 캐시된 파일: 54800개
📋 캐시 확인: 54900/72985 파일, 캐시된 파일: 54900개
📋 캐시 확인: 55000/72985 파일, 캐시된 파일: 55000개
📋 캐시 확인: 55100/72985 파일, 캐시된 파일: 55100개
📋 캐시 확인: 55200/72985 파일, 캐시된 파일: 55200개
📋 캐시 확인: 55300/72985 파일, 캐시된 파일: 55300개
📋 캐시 확인: 55400/72985 파일, 캐시된 파일: 55400개
📋 캐시 확인: 55500/72985 파일, 캐시된 파일: 55500개
📋 캐시 확인: 55600/72985 파일, 캐시된 파일: 55600개
📋 캐시 확인: 55700/72985 파일, 캐시된 파일: 55700개
📋 캐시 확인: 55800/72985 파일, 캐시된 파일: 55800개
📋 캐시 확인: 55900/72985 파일, 캐시된 파일: 55900개
📋 캐시 확인: 56000/72985 파일, 캐시된 파일: 56000개
📋 캐시 확인: 56100/72985 파일, 캐시된 파일: 56100개
📋 캐시 확인: 56200/72985 파일, 캐시된 파일: 56200개
📋 캐시 확인: 56300/72985 파일, 캐시된 파일: 56300개
📋 캐시 확인: 56400/72985 파일, 캐시된 파일: 56400개
📋 캐시 확인: 56500/72985 파일, 캐시된 파일: 56500개
📋 캐시 확인: 56600/72985 파일, 캐시된 파일: 56600개
📋 캐시 확인: 56700/72985 파일, 캐시된 파일: 56700개
📋 캐시 확인: 56800/72985 파일, 캐시된 파일: 56800개
📋 캐시 확인: 56900/72985 파일, 캐시된 파일: 56900개
📋 캐시 확인: 57000/72985 파일, 캐시된 파일: 57000개
📋 캐시 확인: 57100/72985 파일, 캐시된 파일: 57100개
📋 캐시 확인: 57200/72985 파일, 캐시된 파일: 57200개
📋 캐시 확인: 57300/72985 파일, 캐시된 파일: 57300개
📋 캐시 확인: 57400/72985 파일, 캐시된 파일: 57400개
📋 캐시 확인: 57500/72985 파일, 캐시된 파일: 57500개
📋 캐시 확인: 57600/72985 파일, 캐시된 파일: 57600개
📋 캐시 확인: 57700/72985 파일, 캐시된 파일: 57700개
📋 캐시 확인: 57800/72985 파일, 캐시된 파일: 57800개
📋 캐시 확인: 57900/72985 파일, 캐시된 파일: 57900개
📋 캐시 확인: 58000/72985 파일, 캐시된 파일: 58000개
📋 캐시 확인: 58100/72985 파일, 캐시된 파일: 58100개
📋 캐시 확인: 58200/72985 파일, 캐시된 파일: 58200개
📋 캐시 확인: 58300/72985 파일, 캐시된 파일: 58300개
📋 캐시 확인: 58400/72985 파일, 캐시된 파일: 58400개
📋 캐시 확인: 58500/72985 파일, 캐시된 파일: 58500개
📋 캐시 확인: 58600/72985 파일, 캐시된 파일: 58600개
📋 캐시 확인: 58700/72985 파일, 캐시된 파일: 58700개
📋 캐시 확인: 58800/72985 파일, 캐시된 파일: 58800개
📋 캐시 확인: 58900/72985 파일, 캐시된 파일: 58900개
📋 캐시 확인: 59000/72985 파일, 캐시된 파일: 59000개
📋 캐시 확인: 59100/72985 파일, 캐시된 파일: 59100개
📋 캐시 확인: 59200/72985 파일, 캐시된 파일: 59200개
📋 캐시 확인: 59300/72985 파일, 캐시된 파일: 59300개
📋 캐시 확인: 59400/72985 파일, 캐시된 파일: 59400개
📋 캐시 확인: 59500/72985 파일, 캐시된 파일: 59500개
📋 캐시 확인: 59600/72985 파일, 캐시된 파일: 59600개
📋 캐시 확인: 59700/72985 파일, 캐시된 파일: 59700개
📋 캐시 확인: 59800/72985 파일, 캐시된 파일: 59800개
📋 캐시 확인: 59900/72985 파일, 캐시된 파일: 59900개
📋 캐시 확인: 60000/72985 파일, 캐시된 파일: 60000개
📋 캐시 확인: 60100/72985 파일, 캐시된 파일: 60100개
📋 캐시 확인: 60200/72985 파일, 캐시된 파일: 60200개
📋 캐시 확인: 60300/72985 파일, 캐시된 파일: 60300개
📋 캐시 확인: 60400/72985 파일, 캐시된 파일: 60400개
📋 캐시 확인: 60500/72985 파일, 캐시된 파일: 60500개
📋 캐시 확인: 60600/72985 파일, 캐시된 파일: 60600개
📋 캐시 확인: 60700/72985 파일, 캐시된 파일: 60700개
📋 캐시 확인: 60800/72985 파일, 캐시된 파일: 60800개
📋 캐시 확인: 60900/72985 파일, 캐시된 파일: 60900개
📋 캐시 확인: 61000/72985 파일, 캐시된 파일: 61000개
📋 캐시 확인: 61100/72985 파일, 캐시된 파일: 61100개
📋 캐시 확인: 61200/72985 파일, 캐시된 파일: 61200개
📋 캐시 확인: 61300/72985 파일, 캐시된 파일: 61300개
📋 캐시 확인: 61400/72985 파일, 캐시된 파일: 61400개
📋 캐시 확인: 61500/72985 파일, 캐시된 파일: 61500개
📋 캐시 확인: 61600/72985 파일, 캐시된 파일: 61600개
📋 캐시 확인: 61700/72985 파일, 캐시된 파일: 61700개
📋 캐시 확인: 61800/72985 파일, 캐시된 파일: 61800개
📋 캐시 확인: 61900/72985 파일, 캐시된 파일: 61900개
📋 캐시 확인: 62000/72985 파일, 캐시된 파일: 62000개
📋 캐시 확인: 62100/72985 파일, 캐시된 파일: 62100개
📋 캐시 확인: 62200/72985 파일, 캐시된 파일: 62200개
📋 캐시 확인: 62300/72985 파일, 캐시된 파일: 62300개
📋 캐시 확인: 62400/72985 파일, 캐시된 파일: 62400개
📋 캐시 확인: 62500/72985 파일, 캐시된 파일: 62500개
📋 캐시 확인: 62600/72985 파일, 캐시된 파일: 62600개
📋 캐시 확인: 62700/72985 파일, 캐시된 파일: 62700개
📋 캐시 확인: 62800/72985 파일, 캐시된 파일: 62800개
📋 캐시 확인: 62900/72985 파일, 캐시된 파일: 62900개
📋 캐시 확인: 63000/72985 파일, 캐시된 파일: 63000개
📋 캐시 확인: 63100/72985 파일, 캐시된 파일: 63100개
📋 캐시 확인: 63200/72985 파일, 캐시된 파일: 63200개
📋 캐시 확인: 63300/72985 파일, 캐시된 파일: 63300개
📋 캐시 확인: 63400/72985 파일, 캐시된 파일: 63400개
📋 캐시 확인: 63500/72985 파일, 캐시된 파일: 63500개
📋 캐시 확인: 63600/72985 파일, 캐시된 파일: 63600개
📋 캐시 확인: 63700/72985 파일, 캐시된 파일: 63700개
📋 캐시 확인: 63800/72985 파일, 캐시된 파일: 63800개
📋 캐시 확인: 63900/72985 파일, 캐시된 파일: 63900개
📋 캐시 확인: 64000/72985 파일, 캐시된 파일: 64000개
📋 캐시 확인: 64100/72985 파일, 캐시된 파일: 64100개
📋 캐시 확인: 64200/72985 파일, 캐시된 파일: 64200개
📋 캐시 확인: 64300/72985 파일, 캐시된 파일: 64300개
📋 캐시 확인: 64400/72985 파일, 캐시된 파일: 64400개
📋 캐시 확인: 64500/72985 파일, 캐시된 파일: 64500개
📋 캐시 확인: 64600/72985 파일, 캐시된 파일: 64600개
📋 캐시 확인: 64700/72985 파일, 캐시된 파일: 64700개
📋 캐시 확인: 64800/72985 파일, 캐시된 파일: 64800개
📋 캐시 확인: 64900/72985 파일, 캐시된 파일: 64900개
📋 캐시 확인: 65000/72985 파일, 캐시된 파일: 65000개
📋 캐시 확인: 65100/72985 파일, 캐시된 파일: 65100개
📋 캐시 확인: 65200/72985 파일, 캐시된 파일: 65200개
📋 캐시 확인: 65300/72985 파일, 캐시된 파일: 65300개
📋 캐시 확인: 65400/72985 파일, 캐시된 파일: 65400개
📋 캐시 확인: 65500/72985 파일, 캐시된 파일: 65500개
📋 캐시 확인: 65600/72985 파일, 캐시된 파일: 65600개
📋 캐시 확인: 65700/72985 파일, 캐시된 파일: 65700개
📋 캐시 확인: 65800/72985 파일, 캐시된 파일: 65800개
📋 캐시 확인: 65900/72985 파일, 캐시된 파일: 65900개
📋 캐시 확인: 66000/72985 파일, 캐시된 파일: 66000개
📋 캐시 확인: 66100/72985 파일, 캐시된 파일: 66100개
📋 캐시 확인: 66200/72985 파일, 캐시된 파일: 66200개
📋 캐시 확인: 66300/72985 파일, 캐시된 파일: 66300개
📋 캐시 확인: 66400/72985 파일, 캐시된 파일: 66400개
📋 캐시 확인: 66500/72985 파일, 캐시된 파일: 66500개
📋 캐시 확인: 66600/72985 파일, 캐시된 파일: 66600개
📋 캐시 확인: 66700/72985 파일, 캐시된 파일: 66700개
📋 캐시 확인: 66800/72985 파일, 캐시된 파일: 66800개
📋 캐시 확인: 66900/72985 파일, 캐시된 파일: 66900개
📋 캐시 확인: 67000/72985 파일, 캐시된 파일: 67000개
📋 캐시 확인: 67100/72985 파일, 캐시된 파일: 67100개
📋 캐시 확인: 67200/72985 파일, 캐시된 파일: 67200개
📋 캐시 확인: 67300/72985 파일, 캐시된 파일: 67300개
📋 캐시 확인: 67400/72985 파일, 캐시된 파일: 67400개
📋 캐시 확인: 67500/72985 파일, 캐시된 파일: 67500개
📋 캐시 확인: 67600/72985 파일, 캐시된 파일: 67600개
📋 캐시 확인: 67700/72985 파일, 캐시된 파일: 67691개
📋 캐시 확인: 67800/72985 파일, 캐시된 파일: 67789개
📋 캐시 확인: 67900/72985 파일, 캐시된 파일: 67885개
📋 캐시 확인: 68000/72985 파일, 캐시된 파일: 67981개
📋 캐시 확인: 68100/72985 파일, 캐시된 파일: 68069개
📋 캐시 확인: 68200/72985 파일, 캐시된 파일: 68163개
📋 캐시 확인: 68300/72985 파일, 캐시된 파일: 68251개
📋 캐시 확인: 68400/72985 파일, 캐시된 파일: 68346개
📋 캐시 확인: 68500/72985 파일, 캐시된 파일: 68421개
📋 캐시 확인: 68600/72985 파일, 캐시된 파일: 68515개
📋 캐시 확인: 68700/72985 파일, 캐시된 파일: 68596개
📋 캐시 확인: 68800/72985 파일, 캐시된 파일: 68686개
📋 캐시 확인: 68900/72985 파일, 캐시된 파일: 68780개
📋 캐시 확인: 69000/72985 파일, 캐시된 파일: 68879개
📋 캐시 확인: 69100/72985 파일, 캐시된 파일: 68975개
📋 캐시 확인: 69200/72985 파일, 캐시된 파일: 69063개
📋 캐시 확인: 69300/72985 파일, 캐시된 파일: 69153개
📋 캐시 확인: 69400/72985 파일, 캐시된 파일: 69252개
📋 캐시 확인: 69500/72985 파일, 캐시된 파일: 69351개
📋 캐시 확인: 69600/72985 파일, 캐시된 파일: 69433개
📋 캐시 확인: 69700/72985 파일, 캐시된 파일: 69532개
📋 캐시 확인: 69800/72985 파일, 캐시된 파일: 69630개
📋 캐시 확인: 69900/72985 파일, 캐시된 파일: 69724개
📋 캐시 확인: 70000/72985 파일, 캐시된 파일: 69821개
📋 캐시 확인: 70100/72985 파일, 캐시된 파일: 69915개
📋 캐시 확인: 70200/72985 파일, 캐시된 파일: 70013개
📋 캐시 확인: 70300/72985 파일, 캐시된 파일: 70102개
📋 캐시 확인: 70400/72985 파일, 캐시된 파일: 70196개
📋 캐시 확인: 70500/72985 파일, 캐시된 파일: 70283개
📋 캐시 확인: 70600/72985 파일, 캐시된 파일: 70383개
📋 캐시 확인: 70700/72985 파일, 캐시된 파일: 70480개
📋 캐시 확인: 70800/72985 파일, 캐시된 파일: 70572개
📋 캐시 확인: 70900/72985 파일, 캐시된 파일: 70659개
📋 캐시 확인: 71000/72985 파일, 캐시된 파일: 70745개
📋 캐시 확인: 71100/72985 파일, 캐시된 파일: 70831개
📋 캐시 확인: 71200/72985 파일, 캐시된 파일: 70930개
📋 캐시 확인: 71300/72985 파일, 캐시된 파일: 71028개
📋 캐시 확인: 71400/72985 파일, 캐시된 파일: 71125개
📋 캐시 확인: 71500/72985 파일, 캐시된 파일: 71217개
📋 캐시 확인: 71600/72985 파일, 캐시된 파일: 71308개
📋 캐시 확인: 71700/72985 파일, 캐시된 파일: 71407개
📋 캐시 확인: 71800/72985 파일, 캐시된 파일: 71495개
📋 캐시 확인: 71900/72985 파일, 캐시된 파일: 71590개
📋 캐시 확인: 72000/72985 파일, 캐시된 파일: 71680개
📋 캐시 확인: 72100/72985 파일, 캐시된 파일: 71779개
📋 캐시 확인: 72200/72985 파일, 캐시된 파일: 71863개
📋 캐시 확인: 72300/72985 파일, 캐시된 파일: 71963개
📋 캐시 확인: 72400/72985 파일, 캐시된 파일: 72061개
📋 캐시 확인: 72500/72985 파일, 캐시된 파일: 72154개
📋 캐시 확인: 72600/72985 파일, 캐시된 파일: 72253개
📋 캐시 확인: 72700/72985 파일, 캐시된 파일: 72341개
📋 캐시 확인: 72800/72985 파일, 캐시된 파일: 72425개
📋 캐시 확인: 72900/72985 파일, 캐시된 파일: 72517개
📋 캐시 확인: 72985/72985 파일, 캐시된 파일: 72587개
✅ 캐시 확인 완료: 캐시된 파일 72587개, 새로 처리할 파일 398개
🔄 새로운 파일 분석 시작: 398개 파일
📊 진행 상황: 5/398 새 파일 처리 완료, 구매 0건 발견
📊 진행 상황: 10/398 새 파일 처리 완료, 구매 0건 발견
📊 진행 상황: 15/398 새 파일 처리 완료, 구매 0건 발견
📊 진행 상황: 20/398 새 파일 처리 완료, 구매 0건 발견
💰 구매 로그 발견: 1개
💰 구매 로그 발견: 1개
🔍 구매 로그 상세 정보:
🔍 구매 로그 상세 정보:
구매 #1: 시간=2025-07-16 12:09:32, 사용자=72552180-360a-11f0-aa69-21972f5f6197, 상품=select_bookmark_pack_9900
구매 #1: 시간=2025-07-16 12:18:05, 사용자=72552180-360a-11f0-aa69-21972f5f6197, 상품=select_bookmark_pack_9900
📊 진행 상황: 25/398 새 파일 처리 완료, 구매 0건 발견
📊 진행 상황: 30/398 새 파일 처리 완료, 구매 0건 발견
📊 진행 상황: 35/398 새 파일 처리 완료, 구매 0건 발견
📊 진행 상황: 40/398 새 파일 처리 완료, 구매 0건 발견
📊 진행 상황: 45/398 새 파일 처리 완료, 구매 0건 발견
📊 진행 상황: 50/398 새 파일 처리 완료, 구매 0건 발견
📊 진행 상황: 55/398 새 파일 처리 완료, 구매 0건 발견
📊 진행 상황: 60/398 새 파일 처리 완료, 구매 0건 발견
📊 진행 상황: 65/398 새 파일 처리 완료, 구매 0건 발견
📊 진행 상황: 70/398 새 파일 처리 완료, 구매 0건 발견
📊 진행 상황: 75/398 새 파일 처리 완료, 구매 0건 발견
📊 진행 상황: 80/398 새 파일 처리 완료, 구매 0건 발견
📊 진행 상황: 85/398 새 파일 처리 완료, 구매 0건 발견
📊 진행 상황: 90/398 새 파일 처리 완료, 구매 0건 발견
📊 진행 상황: 95/398 새 파일 처리 완료, 구매 0건 발견
📊 진행 상황: 100/398 새 파일 처리 완료, 구매 0건 발견
📊 진행 상황: 105/398 새 파일 처리 완료, 구매 0건 발견
📊 진행 상황: 110/398 새 파일 처리 완료, 구매 0건 발견
📊 진행 상황: 115/398 새 파일 처리 완료, 구매 0건 발견
📊 진행 상황: 120/398 새 파일 처리 완료, 구매 0건 발견
📊 진행 상황: 125/398 새 파일 처리 완료, 구매 0건 발견
📊 진행 상황: 130/398 새 파일 처리 완료, 구매 0건 발견
📊 진행 상황: 135/398 새 파일 처리 완료, 구매 0건 발견
📊 진행 상황: 140/398 새 파일 처리 완료, 구매 0건 발견
📊 진행 상황: 145/398 새 파일 처리 완료, 구매 0건 발견
📊 진행 상황: 150/398 새 파일 처리 완료, 구매 0건 발견
📊 진행 상황: 155/398 새 파일 처리 완료, 구매 0건 발견
📊 진행 상황: 160/398 새 파일 처리 완료, 구매 0건 발견
📊 진행 상황: 165/398 새 파일 처리 완료, 구매 0건 발견
📊 진행 상황: 170/398 새 파일 처리 완료, 구매 0건 발견
📊 진행 상황: 175/398 새 파일 처리 완료, 구매 0건 발견
📊 진행 상황: 180/398 새 파일 처리 완료, 구매 0건 발견
📊 진행 상황: 185/398 새 파일 처리 완료, 구매 0건 발견
📊 진행 상황: 190/398 새 파일 처리 완료, 구매 0건 발견
🔍 구매 로그 분석 중:
- 파일: 72552180-360a-11f0-aa69-21972f5f6197_12H_17M_57S.pb.zst
- 사용자 ID: 72552180-360a-11f0-aa69-21972f5f6197
- 구매 시간: 2025-07-16 12:09:32
- 파일 내 전체 로그 수: 42
🔗 다중 파일 통합 분석 시작 (구매 전 2분간)...
🔍 다중 파일 구매 전 분석 시작:
구매 사용자: 72552180-360a-11f0-aa69-21972f5f6197
구매 시간: 2025-07-16 12:09:32
사용자 관련 파일: 39개 (전체 72985개 중)
✅ 사용자 파일 다운로드 완료: 39개
📊 로그 수집 완료: 1137개 로그 (처리된 파일: 39개)
⏰ 시간 순서 정렬 완료: 1137개 로그
📅 전체 시간 범위: 2025-07-10 16:16:14 ~ 2025-07-16 12:29:29
🎯 분석 대상 시간: 2025-07-16 12:07:32 ~ 2025-07-16 12:09:32 (구매 전 2분)
📊 시간 분포:
- 구매 전 2분간: 31개
- 구매 시점: 4개
- 구매 후: 83개
- 전체 구매 전: 1050개
🕐 세션 기반 필터링 결과:
구매 사용자 ID: 72552180-360a-11f0-aa69-21972f5f6197
구매 시간: 2025-07-16 12:09:32
시작 시간 (구매 전 2분): 2025-07-16 12:07:32
구매 세션 ID: session_72552180-360a-11f0-aa69-21972f5f6197_20250716_114205
전체 로그 수: 1137
세션 내 로그 수: 277
구매 전 2분간 로그 수: 35
🔍 시간 필드 및 필터링 로직 디버깅:
구매 시간: 2025-07-16 12:09:32
시작 시간: 2025-07-16 12:07:32
세션 내 로그 시간 분석:
로그 1: 2025-07-16 11:42:05 (구매 전) - MAF_ANALYTICS_LOG_EVENT_TYPE_LOG_LOGIN
로그 2: 2025-07-16 11:42:05 (구매 전) - MAF_ANALYTICS_LOG_EVENT_TYPE_USER_CURRENCY_DATA
로그 3: 2025-07-16 11:42:05 (구매 전) - MAF_ANALYTICS_LOG_EVENT_TYPE_LOG_UI_OPEN
로그 4: 2025-07-16 11:42:25 (구매 전) - MAF_ANALYTICS_LOG_EVENT_TYPE_LOG_UI_OPEN
로그 5: 2025-07-16 11:42:26 (구매 전) - MAF_ANALYTICS_LOG_EVENT_TYPE_LOG_UI_OPEN
로그 6: 2025-07-16 11:42:26 (구매 전) - MAF_ANALYTICS_LOG_EVENT_TYPE_LOG_SERVER_FUNCTION
로그 7: 2025-07-16 11:42:26 (구매 전) - MAF_ANALYTICS_LOG_EVENT_TYPE_LOG_UI_OPEN
로그 8: 2025-07-16 11:42:28 (구매 전) - MAF_ANALYTICS_LOG_EVENT_TYPE_LOG_UI_OPEN
로그 9: 2025-07-16 11:42:28 (구매 전) - MAF_ANALYTICS_LOG_EVENT_TYPE_LOG_UI_OPEN
로그 10: 2025-07-16 11:42:33 (구매 전) - MAF_ANALYTICS_LOG_EVENT_TYPE_LOG_UI_OPEN
시간 분포 요약:
구매 전: 10개
구매 시점: 0개
구매 후: 0개
필터링 결과: 35개 (구매 시점 포함)
세션 내 로그 시간 분포:
총 204개의 서로 다른 시간
시간 1: 2025-07-16 11:52:36 (4개 로그)
시간 2: 2025-07-16 12:05:19 (4개 로그)
시간 3: 2025-07-16 12:09:32 (4개 로그)
시간 4: 2025-07-16 12:18:05 (4개 로그)
시간 5: 2025-07-16 11:42:05 (3개 로그)
세션 내 로그 시간 샘플 (처음 3개):
로그 1: 2025-07-16 11:42:05 (구매 전 2분 범위 내: False)
로그 2: 2025-07-16 11:42:05 (구매 전 2분 범위 내: False)
로그 3: 2025-07-16 11:42:05 (구매 전 2분 범위 내: False)
🔍 필터링된 로그 샘플 (처음 3개):
로그 1:
event_type: MAF_ANALYTICS_LOG_EVENT_TYPE_LOG_UI_OPEN
payload_ui_name: UIBookmarkIllustPopup
payload_name: None
payload_code: None
payload_a_param: None...
로그 2:
event_type: MAF_ANALYTICS_LOG_EVENT_TYPE_LOG_UI_OPEN
payload_ui_name: UICollectionBase
payload_name: None
payload_code: None
payload_a_param: None...
로그 3:
event_type: MAF_ANALYTICS_LOG_EVENT_TYPE_LOG_UI_OPEN
payload_ui_name: UIBookmarkEnhanceBase
payload_name: None
payload_code: None
payload_a_param: None...
🔄 이벤트 집계 시작: 30개 로그 처리
로그 1: UI 이벤트 -> UIBookmarkIllustPopup(MAF_ANALYTICS_LOG_EVENT_TYPE_LOG_UI_OPEN)
로그 2: UI 이벤트 -> UICollectionBase(MAF_ANALYTICS_LOG_EVENT_TYPE_LOG_UI_OPEN)
로그 3: UI 이벤트 -> UIBookmarkEnhanceBase(MAF_ANALYTICS_LOG_EVENT_TYPE_LOG_UI_OPEN)
로그 4: UI 이벤트 -> UICollectionBase(MAF_ANALYTICS_LOG_EVENT_TYPE_LOG_UI_OPEN)
로그 5: UI 이벤트 -> UIBookmarkEnhanceBase(MAF_ANALYTICS_LOG_EVENT_TYPE_LOG_UI_OPEN)
🎯 이벤트 집계 최종 결과:
- 필터링된 로그 수: 30
- 집계된 이벤트 종류: 5
- 상위 4개 이벤트: [('UICollectionBase(MAF_ANALYTICS_LOG_EVENT_TYPE_LOG_UI_OPEN)', 12), ('UIBookmarkEnhanceBase(MAF_ANALYTICS_LOG_EVENT_TYPE_LOG_UI_OPEN)', 11), ('UIBookmarkIllustPopup(MAF_ANALYTICS_LOG_EVENT_TYPE_LOG_UI_OPEN)', 1), ('Unknown(MAF_ANALYTICS_LOG_EVENT_TYPE_USER_CURRENCY_DATA)', 1)]
✅ 이벤트 집계 성공!
- UICollectionBase(MAF_ANALYTICS_LOG_EVENT_TYPE_LOG_UI_OPEN): 12회
- UIBookmarkEnhanceBase(MAF_ANALYTICS_LOG_EVENT_TYPE_LOG_UI_OPEN): 11회
- UIBookmarkIllustPopup(MAF_ANALYTICS_LOG_EVENT_TYPE_LOG_UI_OPEN): 1회
- Unknown(MAF_ANALYTICS_LOG_EVENT_TYPE_USER_CURRENCY_DATA): 1회
- 구매 전 로그 수: 0
❌ 구매 전 로그 없음!
🔍 구매 로그 분석 중:
- 파일: 72552180-360a-11f0-aa69-21972f5f6197_12H_19M_55S.pb.zst
- 사용자 ID: 72552180-360a-11f0-aa69-21972f5f6197
- 구매 시간: 2025-07-16 12:18:05
- 파일 내 전체 로그 수: 36
🔗 다중 파일 통합 분석 시작 (구매 전 2분간)...
🔍 다중 파일 구매 전 분석 시작:
구매 사용자: 72552180-360a-11f0-aa69-21972f5f6197
구매 시간: 2025-07-16 12:18:05
사용자 관련 파일: 39개 (전체 72985개 중)
✅ 사용자 파일 다운로드 완료: 39개
📊 로그 수집 완료: 1137개 로그 (처리된 파일: 39개)
⏰ 시간 순서 정렬 완료: 1137개 로그
📅 전체 시간 범위: 2025-07-10 16:16:14 ~ 2025-07-16 12:29:29
🎯 분석 대상 시간: 2025-07-16 12:16:05 ~ 2025-07-16 12:18:05 (구매 전 2분)
📊 시간 분포:
- 구매 전 2분간: 15개
- 구매 시점: 4개
- 구매 후: 41개
- 전체 구매 전: 1092개
🕐 세션 기반 필터링 결과:
구매 사용자 ID: 72552180-360a-11f0-aa69-21972f5f6197
구매 시간: 2025-07-16 12:18:05
시작 시간 (구매 전 2분): 2025-07-16 12:16:05
구매 세션 ID: session_72552180-360a-11f0-aa69-21972f5f6197_20250716_114205
전체 로그 수: 1137
세션 내 로그 수: 277
구매 전 2분간 로그 수: 19
🔍 시간 필드 및 필터링 로직 디버깅:
구매 시간: 2025-07-16 12:18:05
시작 시간: 2025-07-16 12:16:05
세션 내 로그 시간 분석:
로그 1: 2025-07-16 11:42:05 (구매 전) - MAF_ANALYTICS_LOG_EVENT_TYPE_LOG_LOGIN
로그 2: 2025-07-16 11:42:05 (구매 전) - MAF_ANALYTICS_LOG_EVENT_TYPE_USER_CURRENCY_DATA
로그 3: 2025-07-16 11:42:05 (구매 전) - MAF_ANALYTICS_LOG_EVENT_TYPE_LOG_UI_OPEN
로그 4: 2025-07-16 11:42:25 (구매 전) - MAF_ANALYTICS_LOG_EVENT_TYPE_LOG_UI_OPEN
로그 5: 2025-07-16 11:42:26 (구매 전) - MAF_ANALYTICS_LOG_EVENT_TYPE_LOG_UI_OPEN
로그 6: 2025-07-16 11:42:26 (구매 전) - MAF_ANALYTICS_LOG_EVENT_TYPE_LOG_SERVER_FUNCTION
로그 7: 2025-07-16 11:42:26 (구매 전) - MAF_ANALYTICS_LOG_EVENT_TYPE_LOG_UI_OPEN
로그 8: 2025-07-16 11:42:28 (구매 전) - MAF_ANALYTICS_LOG_EVENT_TYPE_LOG_UI_OPEN
로그 9: 2025-07-16 11:42:28 (구매 전) - MAF_ANALYTICS_LOG_EVENT_TYPE_LOG_UI_OPEN
로그 10: 2025-07-16 11:42:33 (구매 전) - MAF_ANALYTICS_LOG_EVENT_TYPE_LOG_UI_OPEN
시간 분포 요약:
구매 전: 10개
구매 시점: 0개
구매 후: 0개
필터링 결과: 19개 (구매 시점 포함)
세션 내 로그 시간 분포:
총 204개의 서로 다른 시간
시간 1: 2025-07-16 11:52:36 (4개 로그)
시간 2: 2025-07-16 12:05:19 (4개 로그)
시간 3: 2025-07-16 12:09:32 (4개 로그)
시간 4: 2025-07-16 12:18:05 (4개 로그)
시간 5: 2025-07-16 11:42:05 (3개 로그)
세션 내 로그 시간 샘플 (처음 3개):
로그 1: 2025-07-16 11:42:05 (구매 전 2분 범위 내: False)
로그 2: 2025-07-16 11:42:05 (구매 전 2분 범위 내: False)
로그 3: 2025-07-16 11:42:05 (구매 전 2분 범위 내: False)
🔍 필터링된 로그 샘플 (처음 3개):
로그 1:
event_type: MAF_ANALYTICS_LOG_EVENT_TYPE_LOG_UI_OPEN
payload_ui_name: UISummonRewardCutscenePopup
payload_name: None
payload_code: None
payload_a_param: None...
로그 2:
event_type: MAF_ANALYTICS_LOG_EVENT_TYPE_LOG_SERVER_FUNCTION
payload_ui_name: None
payload_name: None
payload_code: None
payload_a_param: {"code":"Gacha","count":10,"free":0,"platform":"aos","tableId":"2322","timeDate":"2025-07-16T12:16:5...
로그 3:
event_type: MAF_ANALYTICS_LOG_EVENT_TYPE_LOG_FUNNEL
payload_ui_name: None
payload_name: m_use_ticket
payload_code: None
payload_a_param: None...
🔄 이벤트 집계 시작: 14개 로그 처리
로그 1: UI 이벤트 -> UISummonRewardCutscenePopup(MAF_ANALYTICS_LOG_EVENT_TYPE_LOG_UI_OPEN)
로그 2: SERVER_FUNCTION -> Gacha(MAF_ANALYTICS_LOG_EVENT_TYPE_LOG_SERVER_FUNCTION)
로그 3: FUNNEL -> m_use_ticket(MAF_ANALYTICS_LOG_EVENT_TYPE_LOG_FUNNEL)
로그 4: UI 이벤트 -> UICollectionBase(MAF_ANALYTICS_LOG_EVENT_TYPE_LOG_UI_OPEN)
로그 5: UI 이벤트 -> UIJokerEnhancePopup(MAF_ANALYTICS_LOG_EVENT_TYPE_LOG_UI_OPEN)
🎯 이벤트 집계 최종 결과:
- 필터링된 로그 수: 14
- 집계된 이벤트 종류: 8
- 상위 4개 이벤트: [('UIJokerEnhancePopup(MAF_ANALYTICS_LOG_EVENT_TYPE_LOG_UI_OPEN)', 2), ('UISummonRewardCutscenePopup(MAF_ANALYTICS_LOG_EVENT_TYPE_LOG_UI_OPEN)', 1), ('Gacha(MAF_ANALYTICS_LOG_EVENT_TYPE_LOG_SERVER_FUNCTION)', 1), ('m_use_ticket(MAF_ANALYTICS_LOG_EVENT_TYPE_LOG_FUNNEL)', 1)]
✅ 이벤트 집계 성공!
- UIJokerEnhancePopup(MAF_ANALYTICS_LOG_EVENT_TYPE_LOG_UI_OPEN): 2회
- UISummonRewardCutscenePopup(MAF_ANALYTICS_LOG_EVENT_TYPE_LOG_UI_OPEN): 1회
- Gacha(MAF_ANALYTICS_LOG_EVENT_TYPE_LOG_SERVER_FUNCTION): 1회
- m_use_ticket(MAF_ANALYTICS_LOG_EVENT_TYPE_LOG_FUNNEL): 1회
- 구매 전 로그 수: 0
❌ 구매 전 로그 없음!
📊 진행 상황: 195/398 새 파일 처리 완료, 구매 1건 발견
📊 진행 상황: 200/398 새 파일 처리 완료, 구매 0건 발견
📊 진행 상황: 205/398 새 파일 처리 완료, 구매 0건 발견
📊 진행 상황: 210/398 새 파일 처리 완료, 구매 0건 발견
📊 진행 상황: 215/398 새 파일 처리 완료, 구매 0건 발견
📊 진행 상황: 220/398 새 파일 처리 완료, 구매 0건 발견
📊 진행 상황: 225/398 새 파일 처리 완료, 구매 0건 발견
📊 진행 상황: 230/398 새 파일 처리 완료, 구매 0건 발견
📊 진행 상황: 235/398 새 파일 처리 완료, 구매 0건 발견
📊 진행 상황: 240/398 새 파일 처리 완료, 구매 0건 발견
📊 진행 상황: 245/398 새 파일 처리 완료, 구매 0건 발견
📊 진행 상황: 250/398 새 파일 처리 완료, 구매 0건 발견
📊 진행 상황: 255/398 새 파일 처리 완료, 구매 0건 발견
📊 진행 상황: 260/398 새 파일 처리 완료, 구매 0건 발견
📊 진행 상황: 265/398 새 파일 처리 완료, 구매 0건 발견
📊 진행 상황: 270/398 새 파일 처리 완료, 구매 0건 발견
📊 진행 상황: 275/398 새 파일 처리 완료, 구매 0건 발견
📊 진행 상황: 280/398 새 파일 처리 완료, 구매 0건 발견
📊 진행 상황: 285/398 새 파일 처리 완료, 구매 0건 발견
📊 진행 상황: 290/398 새 파일 처리 완료, 구매 0건 발견
📊 진행 상황: 295/398 새 파일 처리 완료, 구매 0건 발견
📊 진행 상황: 300/398 새 파일 처리 완료, 구매 0건 발견
📊 진행 상황: 305/398 새 파일 처리 완료, 구매 0건 발견
📊 진행 상황: 310/398 새 파일 처리 완료, 구매 0건 발견
📊 진행 상황: 315/398 새 파일 처리 완료, 구매 0건 발견
📊 진행 상황: 320/398 새 파일 처리 완료, 구매 0건 발견
📊 진행 상황: 325/398 새 파일 처리 완료, 구매 0건 발견
📊 진행 상황: 330/398 새 파일 처리 완료, 구매 0건 발견
📊 진행 상황: 335/398 새 파일 처리 완료, 구매 0건 발견
📊 진행 상황: 340/398 새 파일 처리 완료, 구매 0건 발견
📊 진행 상황: 345/398 새 파일 처리 완료, 구매 0건 발견
📊 진행 상황: 350/398 새 파일 처리 완료, 구매 0건 발견
📊 진행 상황: 355/398 새 파일 처리 완료, 구매 0건 발견
📊 진행 상황: 360/398 새 파일 처리 완료, 구매 0건 발견
📊 진행 상황: 365/398 새 파일 처리 완료, 구매 0건 발견
📊 진행 상황: 370/398 새 파일 처리 완료, 구매 0건 발견
📊 진행 상황: 375/398 새 파일 처리 완료, 구매 0건 발견
📊 진행 상황: 380/398 새 파일 처리 완료, 구매 0건 발견
📊 진행 상황: 385/398 새 파일 처리 완료, 구매 0건 발견
📊 진행 상황: 390/398 새 파일 처리 완료, 구매 0건 발견
📊 진행 상황: 395/398 새 파일 처리 완료, 구매 0건 발견
🎉 분석 완료! 총 구매 483건, 처리된 파일 72985개 (캐시: 72587개, 새로 처리: 398개)
💾 구매 분석 결과 캐시 저장 완료!
