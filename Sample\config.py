"""
프로젝트 설정 및 상수 정의 모듈

이 모듈은 프로젝트 전반에서 사용되는 설정값, 상수, 매핑 데이터 등을 
중앙 집중식으로 관리합니다.
"""

import os

# AWS S3 설정
AWS_ACCESS_KEY_ID = os.getenv("AWS_ACCESS_KEY_ID", "********************")
AWS_SECRET_ACCESS_KEY = os.getenv("AWS_SECRET_ACCESS_KEY", "CpEHZYjyIv9Ugtls7RvwIeeJNb89fxBTvWtATJ+j")
AWS_BUCKET_NAME = "maf-ace"
AWS_REGION_NAME = "ap-northeast-2"
S3_PREFIX = "Log/"

# 로컬 파일 경로 설정
LOCAL_DOWNLOAD_DIR = "./downloads"
BOOKMARK_NAME_FILE = "bookmakr_name.txt"
JOKER_NAME_FILE = "joker_name.txt"
PLAYING_CARD_NAME_FILE = "playing_card_name.txt"
CARD_LABEL_NAME_FILE = "card_label_name.txt"
SCROLL_ITEM_NAME_FILE = "scroll_item_name.txt"
CONSUME_ITEM_NAME_FILE = "consume_item_name.txt"

# 컬럼 이름 매핑
COLUMN_RENAME_MAP = {
    "round_index": "라운드",
    "order": "행동 순서",
    "event_type_order": "로그 순서",
    "event_type": "이벤트 타입",
    "bookmarkDataIndex": "책갈피 IDX",
    "jokerDataIndex": "조커 IDX",
    "cardDataIndex": "카드 IDX",
    "itemIndex": "아이템 IDX",
    "consumeItemDataIndex": "소모아이템 IDX",
    "scrollItemDataIndex": "스크롤아이템 IDX",
    "monsterCurrentHP": "몬스터 체력",
    "monsterMaxHP": "몬스터 최대 체력",
    "multiple": "배수",
    "chip": "공격력",
    "evaluateResult": "평가 결과",
}

# 캐시 설정
CACHE_TTL_SHORT = 300  # 5분
CACHE_TTL_LONG = 3600  # 1시간

# UI 설정
DEFAULT_MAX_EVENTS = 10000
DEFAULT_MAP_HEIGHT = 800
MIN_MAP_HEIGHT = 400
MAX_MAP_HEIGHT = 1200
MAP_HEIGHT_STEP = 50

# 성능 설정
LARGE_DATA_THRESHOLD = 2000  # 대용량 데이터 기준
BUFFER_SIZE = 1024 * 1024  # 1MB 버퍼 크기

# 이벤트 타입 매핑
EVENT_TYPE_ICONS = {
    "bookmark": "📖",
    "joker": "🃏", 
    "monster": "👹",
    "card": "🎴",
    "ability": "⚡",
    "damage": "💥",
    "heal": "💚",
    "money": "💰",
    "round": "🔄",
    "game": "🎮"
}

# 필드 아이콘 매핑 규칙
FIELD_ICON_RULES = {
    "money": "💰",
    "dollar": "💰", 
    "coin": "💰",
    "chip": "🔥",
    "damage": "💥",
    "attack": "⚔️",
    "hp": "❤️",
    "health": "❤️",
    "level": "📊",
    "count": "🔢",
    "index": "🔍",
    "id": "🆔",
    "type": "🏷️",
    "name": "📝",
    "value": "💎",
    "score": "🏆",
    "point": "⭐",
    "time": "⏰",
    "round": "🔄",
    "turn": "🔄",
    "max": "🔝",
    "maximum": "🔝",
    "is_": "✅",
    "default": "🔹"
}

# 페이로드 타입 매핑
PAYLOAD_TYPE_MAPPING = {
    "INT": "intValue",
    "FLOAT": "floatValue", 
    "STRING": "stringValue",
    "BOOL": "boolValue"
}

# 마인드맵 설정
MINDMAP_SETTINGS = {
    "default_height": DEFAULT_MAP_HEIGHT,
    "collapse_level": 2,
    "max_items_display": None,  # 제한 없음
    "show_empty_data": True,
    "empty_data_suffix": "(데이터 없음)"
}

# 로그 파싱 설정
PARSING_SETTINGS = {
    "max_events_default": DEFAULT_MAX_EVENTS,
    "batch_size": 1000,
    "enable_caching": True,
    "cache_size": 32
}

# 파일 확장자 설정
FILE_EXTENSIONS = {
    "compressed": ".zst",
    "protobuf": ".pb",
    "text": ".txt"
}

# 에러 메시지
ERROR_MESSAGES = {
    "file_not_found": "파일을 찾을 수 없습니다: {path}",
    "parse_error": "파일 파싱 중 오류가 발생했습니다: {error}",
    "download_error": "파일 다운로드 중 오류가 발생했습니다: {error}",
    "s3_connection_error": "S3 연결 중 오류가 발생했습니다: {error}",
    "decompression_error": "파일 압축 해제 중 오류가 발생했습니다: {error}"
}

# 성공 메시지
SUCCESS_MESSAGES = {
    "file_downloaded": "파일이 성공적으로 다운로드되었습니다: {path}",
    "file_parsed": "파일이 성공적으로 파싱되었습니다. 총 {count}개 이벤트",
    "cache_hit": "캐시에서 데이터를 불러왔습니다",
    "processing_complete": "처리가 완료되었습니다"
}

# 디버그 설정
DEBUG_MODE = os.getenv("DEBUG", "False").lower() == "true"
VERBOSE_LOGGING = os.getenv("VERBOSE", "False").lower() == "true"

# 앱 메타데이터
APP_METADATA = {
    "title": "ACE Battle Log Analysis Tool",
    "version": "2.0.0",
    "description": "모바일 카드 게임 로그 분석 도구",
    "author": "ACE Team"
}
