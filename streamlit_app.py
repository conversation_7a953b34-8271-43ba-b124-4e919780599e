import streamlit as st
import os
import pandas as pd
from datetime import datetime
from s3_utils import list_s3_log_files, get_s3_prefix_for_environment
from config import LOCAL_DOWNLOAD_DIR, ENVIRONMENT_NAMES

#Streamlit_app
# 페이지 설정
st.set_page_config(
    page_title="User Action Log Analyzer",
    page_icon="📊",
    layout="wide",
    initial_sidebar_state="expanded"
)

# 프로젝트 목록 (추후 config 파일이나 DB에서 가져올 수 있음)
AVAILABLE_PROJECTS = {
    "maf-ace": {
        "name": "MAF ACE",
        "description": "메인 서비스 게임",
        "bucket": "maf-ace",
        "region": "ap-northeast-2"
    },
    "maf-project1": {
        "name": "MAF Project 1",
        "description": "개발 중인 프로젝트 1",
        "bucket": "maf-project1",
        "region": "ap-northeast-2"
    },
    "maf-project2": {
        "name": "MAF Project 2",
        "description": "개발 중인 프로젝트 2",
        "bucket": "maf-project2",
        "region": "ap-northeast-2"
    }
}

# 기능 목록 (리텐션 분석을 메인으로)
FEATURE_TABS = [
    "🚀 리텐션 분석",
    "👤 개별 유저 동선",
    "📖 책갈피 레벨&돌파 통계",
    "📋 로그 목록",
    "💎 재화 통계",
    "💰 유료 구매 시점",
    "🔧 설정"
]

# ============================================================================
# 사이드바: 프로젝트 선택
# ============================================================================

st.sidebar.title("🎮 프로젝트 선택")

# 프로젝트 선택
selected_project_key = st.sidebar.selectbox(
    "분석할 프로젝트를 선택하세요:",
    options=list(AVAILABLE_PROJECTS.keys()),
    format_func=lambda x: f"{AVAILABLE_PROJECTS[x]['name']} ({x})",
    index=0
)

# 선택된 프로젝트 정보 표시
if selected_project_key:
    project_info = AVAILABLE_PROJECTS[selected_project_key]
    st.sidebar.info(f"""
    **프로젝트:** {project_info['name']}
    **설명:** {project_info['description']}
    **S3 버킷:** {project_info['bucket']}
    **리전:** {project_info['region']}
    """)

# ============================================================================
# 사이드바: 환경 선택 (Dev/Live)
# ============================================================================

st.sidebar.markdown("---")
st.sidebar.title("🌍 환경 선택")

# 환경 선택 (Dev/Live)
selected_environment = st.sidebar.radio(
    "분석할 환경을 선택하세요:",
    options=list(ENVIRONMENT_NAMES.keys()),
    format_func=lambda x: ENVIRONMENT_NAMES[x],
    index=1  # 기본값: live (0=dev, 1=live)
)

# 선택된 환경 정보 표시
current_prefix = get_s3_prefix_for_environment(selected_environment)
st.sidebar.info(f"""
**환경:** {ENVIRONMENT_NAMES[selected_environment]}
**S3 경로:** `{current_prefix}`
""")

# ============================================================================
# 사이드바: 캐시 관리
# ============================================================================

st.sidebar.markdown("---")
st.sidebar.title("💾 캐시 관리")

# 캐시 디렉토리 정보
cache_dir = "cache"
if os.path.exists(cache_dir):
    cache_files = [f for f in os.listdir(cache_dir) if f.startswith("purchase_analysis_") and f.endswith(".pkl")]
    cache_count = len(cache_files)

    if cache_count > 0:
        # 캐시 파일 크기 계산
        total_size = 0
        for cache_file in cache_files:
            cache_path = os.path.join(cache_dir, cache_file)
            if os.path.exists(cache_path):
                total_size += os.path.getsize(cache_path)

        size_mb = total_size / (1024 * 1024)

        st.sidebar.info(f"""
        **캐시 파일:** {cache_count}개
        **총 크기:** {size_mb:.1f} MB
        """)

        # 캐시 삭제 버튼
        if st.sidebar.button("🗑️ 모든 캐시 삭제", type="secondary"):
            try:
                for cache_file in cache_files:
                    cache_path = os.path.join(cache_dir, cache_file)
                    if os.path.exists(cache_path):
                        os.remove(cache_path)
                st.sidebar.success("✅ 캐시가 삭제되었습니다!")
                st.rerun()
            except Exception as e:
                st.sidebar.error(f"❌ 캐시 삭제 실패: {e}")
    else:
        st.sidebar.info("캐시 파일이 없습니다.")
else:
    st.sidebar.info("캐시 디렉토리가 없습니다.")

# ============================================================================
# 메인 컨텐츠
# ============================================================================

st.title("📊 User Action Log Analyzer")
st.markdown(f"**현재 프로젝트:** {AVAILABLE_PROJECTS[selected_project_key]['name']} | **환경:** {ENVIRONMENT_NAMES[selected_environment]}")

# 탭 생성
selected_tab = st.tabs(FEATURE_TABS)

@st.cache_data
def get_s3_files_cached(bucket_name: str, environment: str):
    """S3 파일 목록을 환경별로 캐싱하여 가져옵니다."""
    try:
        # 선택된 환경에 따른 S3 prefix 사용
        prefix = get_s3_prefix_for_environment(environment)
        return list_s3_log_files(prefix=prefix)
    except Exception as e:
        st.error(f"S3 파일 목록을 가져오는 중 오류가 발생했습니다: {e}")
        return []

# 탭 1: 리텐션 분석
with selected_tab[0]:
    st.header("🚀 리텐션 분석")
    st.markdown("사용자 리텐션을 분석하여 **코호트별 유지율**을 확인할 수 있습니다.")

    # 날짜 선택 기능
    from datetime import date, timedelta
    col1, col2 = st.columns([2, 3])
    with col1:
        selected_date = st.date_input(
            "📅 기준 날짜 선택",
            value=date.today(),
            help="선택한 날짜를 기준으로 이전 30일간 등록한 사용자의 리텐션을 분석합니다."
        )
    with col2:
        start_date = selected_date - timedelta(days=30)
        st.info(f"📊 **분석 범위**: {start_date.strftime('%Y-%m-%d')} ~ {selected_date.strftime('%Y-%m-%d')} 등록 사용자")

    # 분석 시작 버튼
    if st.button("📊 리텐션 분석 시작", type="primary"):
        with st.spinner("리텐션 분석을 진행하고 있습니다..."):
            from retention_analyzer import analyze_retention_from_s3_files
            from config import LOCAL_DOWNLOAD_DIR
            
            # 현재 프로젝트와 환경의 S3 파일 목록 가져오기
            current_project = AVAILABLE_PROJECTS[selected_project_key]
            s3_files = get_s3_files_cached(current_project['bucket'], selected_environment)
            
            if not s3_files:
                st.error("S3에서 로그 파일을 찾을 수 없습니다.")
            else:
                # 리텐션 분석 실행 (선택된 날짜 전달)
                retention_result = analyze_retention_from_s3_files(s3_files, LOCAL_DOWNLOAD_DIR, end_date=selected_date)
                
                if retention_result and 'error' not in retention_result:
                    # 분석 결과 표시
                    st.success("✅ 리텐션 분석이 완료되었습니다!")
                    
                    # 요약 정보
                    summary = retention_result['summary']
                    file_processing = retention_result.get('file_processing', {})

                    col1, col2, col3, col4 = st.columns(4)
                    with col1:
                        st.metric("총 사용자 수", f"{summary['total_users']:,}")
                    with col2:
                        total_files = file_processing.get('total_files', 0)
                        st.metric("분석 파일 수", f"{total_files:,}")
                    with col3:
                        start_date = summary['date_range']['start']
                        end_date = summary['date_range']['end']
                        if start_date and end_date:
                            days = (end_date - start_date).days + 1
                        else:
                            days = 0
                        st.metric("분석 기간", f"{days}일")
                    with col4:
                        st.metric("총 디바이스 수", f"{summary['total_devices']:,}")
                    
                    # 기간 정보
                    if summary['date_range']['start'] and summary['date_range']['end']:
                        st.info(f"**분석 기간:** {summary['date_range']['start']} ~ {summary['date_range']['end']}")
                    else:
                        st.info("**분석 기간:** 데이터 없음")
                    
                    # 새로운 리텐션 기준 코호트 테이블 표시 (UserID vs DeviceID)
                    if ('new_cohort_table_user' in retention_result and not retention_result['new_cohort_table_user'].empty and
                        'new_cohort_table_device' in retention_result and not retention_result['new_cohort_table_device'].empty):

                        st.subheader("📈 코호트 리텐션 테이블 비교")
                        st.info("📊 선택된 날짜 범위의 모든 등록일이 표시됩니다. (사용자가 없는 날짜 포함)")
                        st.markdown("🎨 **색상 안내**: 진한 색일수록 높은 리텐션율을 나타냅니다. (UserID: 파란색, DeviceID: 초록색)")

                        # 좌우 배치를 위한 컬럼 생성
                        col_left, col_right = st.columns(2)

                        # UserID 기준 테이블 (좌측)
                        with col_left:
                            st.markdown("#### 👤 UserID 기준 리텐션")
                            user_table = retention_result['new_cohort_table_user']

                            # 리텐션 컬럼만 선택 (+0일, +1일, +2일, ...)
                            retention_columns = [col for col in user_table.columns if col.startswith('+') and col.endswith('일')]

                            if retention_columns:
                                # 표시용 테이블 생성
                                display_table = user_table.copy()

                                # 백분율 포맷 적용 함수
                                def format_percentage(val):
                                    if isinstance(val, (int, float)):
                                        if val > 0:
                                            return f"{val * 100:.1f}%"
                                        else:
                                            return "-"
                                    else:
                                        return str(val)

                                # 리텐션 컬럼에만 포맷 적용
                                format_dict = {}
                                for col in retention_columns:
                                    format_dict[col] = format_percentage

                                # 히트맵용 숫자 데이터 생성 (0-100 범위)
                                heatmap_data = display_table.copy()
                                for col in retention_columns:
                                    heatmap_data[col] = pd.to_numeric(heatmap_data[col], errors='coerce') * 100

                                # 스타일링된 테이블 표시 (부드러운 색상 팔레트 사용)
                                styled_table = display_table.style.format(format_dict).background_gradient(
                                    subset=retention_columns, cmap='Blues', axis=None, vmin=0, vmax=100,
                                    gmap=heatmap_data[retention_columns]
                                )
                                st.dataframe(styled_table, use_container_width=True, height=600)
                            else:
                                st.dataframe(user_table, use_container_width=True, height=600)

                        # DeviceID 기준 테이블 (우측)
                        with col_right:
                            st.markdown("#### 📱 DeviceID 기준 리텐션")
                            device_table = retention_result['new_cohort_table_device']

                            # 리텐션 컬럼만 선택 (+0일, +1일, +2일, ...)
                            retention_columns = [col for col in device_table.columns if col.startswith('+') and col.endswith('일')]

                            if retention_columns:
                                # 표시용 테이블 생성
                                display_table = device_table.copy()

                                # 백분율 포맷 적용 함수
                                def format_percentage(val):
                                    if isinstance(val, (int, float)):
                                        if val > 0:
                                            return f"{val * 100:.1f}%"
                                        else:
                                            return "-"
                                    else:
                                        return str(val)

                                # 리텐션 컬럼에만 포맷 적용
                                format_dict = {}
                                for col in retention_columns:
                                    format_dict[col] = format_percentage

                                # 히트맵용 숫자 데이터 생성 (0-100 범위)
                                heatmap_data = display_table.copy()
                                for col in retention_columns:
                                    heatmap_data[col] = pd.to_numeric(heatmap_data[col], errors='coerce') * 100

                                # 스타일링된 테이블 표시 (부드러운 색상 팔레트 사용)
                                styled_table = display_table.style.format(format_dict).background_gradient(
                                    subset=retention_columns, cmap='Greens', axis=None, vmin=0, vmax=100,
                                    gmap=heatmap_data[retention_columns]
                                )
                                st.dataframe(styled_table, use_container_width=True, height=600)
                            else:
                                st.dataframe(device_table, use_container_width=True, height=600)

                    else:
                        st.warning("코호트 테이블을 생성할 수 없습니다.")
                else:
                    error_msg = retention_result.get('error', '알 수 없는 오류가 발생했습니다.') if retention_result else '분석 결과가 없습니다.'
                    st.error(f"리텐션 분석 중 오류가 발생했습니다: {error_msg}")
    else:
        # 기본 안내 메시지
        st.info("👆 위의 버튼을 클릭하여 리텐션 분석을 시작하세요!")
        
        # 리텐션 분석 설명
        st.markdown("### 📖 리텐션 분석이란?")
        st.markdown("""
        **리텐션(Retention)**은 사용자가 서비스에 얼마나 지속적으로 참여하는지를 측정하는 지표입니다.
        
        - **Day 0**: 사용자 등록일 (100%)
        - **Day 1**: 등록 다음날 재방문율
        - **Day 7**: 등록 후 7일째 재방문율
        - **Day 30**: 등록 후 30일째 재방문율
        
        이 분석을 통해 게임의 **사용자 유지력**과 **장기적 성장 가능성**을 파악할 수 있습니다.
        """)

# 탭 2: 개별 유저 동선
with selected_tab[1]:
    from user_journey_tab import render_user_journey_tab
    render_user_journey_tab(selected_project_key, AVAILABLE_PROJECTS, get_s3_files_cached, selected_environment)

# 탭 3: 책갈피 레벨&돌파 통계
with selected_tab[2]:
    st.header("📖 책갈피 레벨&돌파 통계")

    # 환경별 S3 파일 가져오기
    try:
        s3_files = get_s3_files_cached(selected_project_key, selected_environment)
        if not s3_files:
            st.error("S3 파일을 가져올 수 없습니다.")
        else:
            # 개별 통계와 전체 통계 선택
            analysis_type = st.radio(
                "분석 유형을 선택하세요:",
                ["개별 사용자 통계", "전체 사용자 통계"],
                horizontal=True
            )

            if analysis_type == "개별 사용자 통계":
                st.subheader("👤 개별 사용자 책갈피 통계")

                # 사용자 ID 입력
                user_id = st.text_input("분석할 사용자 ID를 입력하세요:", placeholder="예: user123")

                if user_id and st.button("🔍 개별 분석 시작"):
                    with st.spinner("사용자 책갈피 데이터를 분석하고 있습니다..."):
                        from bookmark_analyzer import analyze_user_bookmark_data, create_individual_bookmark_summary

                        # 개별 사용자 분석
                        user_data = analyze_user_bookmark_data(user_id, s3_files, LOCAL_DOWNLOAD_DIR)

                        if user_data:
                            st.success(f"✅ 사용자 {user_id}의 책갈피 데이터 분석 완료!")

                            # 기본 정보 표시
                            col1, col2 = st.columns(2)
                            with col1:
                                st.metric("사용자 ID", user_data['user_id'])
                            with col2:
                                st.metric("데이터 시점", user_data['timestamp'].strftime("%Y-%m-%d %H:%M:%S") if user_data['timestamp'] else "알 수 없음")

                            # 책갈피 데이터 테이블
                            df = create_individual_bookmark_summary(user_data)
                            if not df.empty:
                                st.subheader("📊 책갈피 상세 데이터")
                                st.dataframe(df, use_container_width=True)

                                # 통계 요약
                                st.subheader("📈 통계 요약")
                                col1, col2, col3, col4 = st.columns(4)
                                with col1:
                                    st.metric("총 책갈피 수", len(df))
                                with col2:
                                    avg_lv = df['lv'].mean() if not df.empty else 0
                                    st.metric("평균 레벨", f"{avg_lv:.1f}")
                                with col3:
                                    avg_e_lv = df['e_lv'].mean() if not df.empty else 0
                                    st.metric("평균 진화 레벨", f"{avg_e_lv:.1f}")
                                with col4:
                                    avg_p_lv = df['p_lv'].mean() if not df.empty else 0
                                    st.metric("평균 돌파 레벨", f"{avg_p_lv:.1f}")
                            else:
                                st.warning("책갈피 데이터가 없습니다.")
                        else:
                            st.error(f"사용자 {user_id}의 책갈피 데이터를 찾을 수 없습니다.")

            else:  # 전체 사용자 통계
                st.subheader("🌍 전체 사용자 책갈피 통계")
                st.info("📊 모든 사용자의 책갈피 데이터를 분석합니다.")

                if st.button("📊 전체 분석 시작"):
                    with st.spinner("전체 사용자 책갈피 데이터를 분석하고 있습니다..."):
                        from bookmark_analyzer import analyze_all_users_bookmark_data, create_overall_bookmark_statistics

                        # 전체 사용자 분석 (모든 파일 처리)
                        all_data = analyze_all_users_bookmark_data(s3_files, LOCAL_DOWNLOAD_DIR)

                        if 'error' not in all_data:
                            st.success(f"✅ 전체 사용자 책갈피 데이터 분석 완료!")

                            # 기본 통계
                            st.metric("분석된 사용자 수", all_data['total_users'])

                            # e_lv 통계 테이블
                            stats_df = create_overall_bookmark_statistics(all_data)
                            if not stats_df.empty:
                                st.subheader("📊 책갈피별 진화 레벨(e_lv) 통계")
                                st.dataframe(stats_df, use_container_width=True)

                                # 다운로드 버튼
                                csv = stats_df.to_csv(index=False, encoding='utf-8-sig')
                                st.download_button(
                                    label="📥 통계 데이터 다운로드 (CSV)",
                                    data=csv,
                                    file_name=f"bookmark_statistics_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                                    mime="text/csv"
                                )
                            else:
                                st.warning("통계 데이터가 없습니다.")
                        else:
                            st.error(f"분석 실패: {all_data.get('error', '알 수 없는 오류')}")

    except Exception as e:
        st.error(f"오류가 발생했습니다: {e}")

# 탭 4: 로그 목록
with selected_tab[3]:
    st.header("📋 로그 목록")
    st.markdown("S3에 저장된 **모든 로그 파일 목록**을 확인할 수 있습니다.")

    # S3 파일 목록 가져오기 (환경별)
    current_project = AVAILABLE_PROJECTS[selected_project_key]
    s3_files = get_s3_files_cached(current_project['bucket'], selected_environment)

    if s3_files:
        st.success(f"✅ 총 {len(s3_files)}개의 로그 파일을 발견했습니다.")

        # 검색 및 필터링 옵션
        col1, col2 = st.columns([3, 1])
        with col1:
            search_term = st.text_input("🔍 파일명 검색", placeholder="파일명 또는 사용자 ID로 검색...")
        with col2:
            show_all = st.checkbox("모든 파일 표시", value=False, help="체크하면 모든 파일을 표시합니다 (느릴 수 있음)")

        # 파일 필터링
        filtered_files = s3_files
        if search_term:
            filtered_files = [f for f in s3_files if search_term.lower() in f.lower()]
            st.info(f"검색 결과: {len(filtered_files)}개 파일")

        # 표시할 파일 수 결정
        if show_all:
            display_files = filtered_files
        else:
            display_files = filtered_files[:500]  # 최대 500개까지 표시

        # 파일 목록을 DataFrame으로 변환
        file_data = []
        for file_path in display_files:
            file_name = os.path.basename(file_path)

            # 파일 경로에서 날짜 추출
            import re
            date_match = re.search(r'(\d{4}_\d{1,2}_\d{1,2})', file_path)
            date_str = date_match.group(1) if date_match else "알 수 없음"

            # 사용자 ID 추출 (파일명에서)
            user_id_match = re.search(r'([a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12})', file_name)
            user_id = user_id_match.group(1) if user_id_match else "시스템"

            # 시간 추출 (파일명에서)
            time_match = re.search(r'(\d+)H_(\d+)M_(\d+)S', file_name)
            time_str = f"{time_match.group(1)}:{time_match.group(2)}:{time_match.group(3)}" if time_match else "알 수 없음"

            file_data.append({
                "파일명": file_name,
                "날짜": date_str,
                "사용자 ID": user_id,
                "시간": time_str,
                "경로": file_path
            })

        df_files = pd.DataFrame(file_data)

        # 정렬 옵션
        sort_col1, sort_col2 = st.columns(2)
        with sort_col1:
            sort_by = st.selectbox("정렬 기준", ["날짜", "시간", "사용자 ID", "파일명"], index=0)
        with sort_col2:
            sort_order = st.selectbox("정렬 순서", ["내림차순", "오름차순"], index=0)

        # 데이터 정렬
        ascending = sort_order == "오름차순"
        df_files_sorted = df_files.sort_values(by=sort_by, ascending=ascending)

        # 파일 목록 표시
        st.subheader(f"📄 파일 목록 ({len(display_files)}개)")
        st.dataframe(
            df_files_sorted[["파일명", "날짜", "사용자 ID", "시간"]],
            use_container_width=True,
            height=600
        )

        # 통계 정보
        st.subheader("📊 통계 정보")
        col1, col2, col3, col4 = st.columns(4)
        with col1:
            st.metric("총 파일 수", len(filtered_files))
        with col2:
            unique_dates = df_files['날짜'].nunique()
            st.metric("활동 일수", unique_dates)
        with col3:
            unique_users = df_files[df_files['사용자 ID'] != '시스템']['사용자 ID'].nunique()
            st.metric("고유 사용자", unique_users)
        with col4:
            system_files = len(df_files[df_files['사용자 ID'] == '시스템'])
            st.metric("시스템 파일", system_files)

        # 표시 제한 안내
        if not show_all and len(filtered_files) > 500:
            st.warning(f"⚠️ 성능을 위해 처음 500개 파일만 표시됩니다. 전체 {len(filtered_files)}개 파일이 있습니다. '모든 파일 표시'를 체크하면 전체를 볼 수 있습니다.")
    else:
        st.error("S3에서 로그 파일을 찾을 수 없습니다.")

# 탭 5: 재화 통계
with selected_tab[4]:
    st.header("💎 재화 통계 분석")
    st.markdown("**MAF_ANALYTICS_LOG_EVENT_TYPE_USER_CURRENCY_DATA** 이벤트를 기반으로 유료/무료 다이아 통계를 분석합니다.")

    # 분석 정보
    st.info(f"""
    **분석 대상:**
    - 아이템 ID 101: 유료 다이아
    - 아이템 ID 102: 무료 다이아
    - 아이템 ID 10004: 일반 소환권
    - 아이템 ID 10005: 픽업 소환권
    - 각 아이템별 보유/획득/사용량 추적
    - 모든 로그 파일을 분석합니다
    """)

    # 분석 시작 버튼
    if st.button("💎 재화 통계 분석 시작", type="primary"):
        with st.spinner("재화 통계를 분석하고 있습니다..."):
            from currency_analyzer import analyze_currency_from_s3_files

            # 현재 프로젝트와 환경의 S3 파일 목록 가져오기
            current_project = AVAILABLE_PROJECTS[selected_project_key]
            s3_files = get_s3_files_cached(current_project['bucket'], selected_environment)

            if not s3_files:
                st.error("S3에서 로그 파일을 찾을 수 없습니다.")
            else:
                # 재화 통계 분석 실행 (모든 파일 분석)
                currency_result = analyze_currency_from_s3_files(s3_files, LOCAL_DOWNLOAD_DIR, None)

                if 'error' in currency_result:
                    st.error(f"❌ 분석 실패: {currency_result['error']}")
                    if 'processed_files' in currency_result:
                        st.info(f"처리된 파일: {currency_result['processed_files']}개")
                else:
                    # 분석 결과 표시
                    st.success("✅ 재화 통계 분석이 완료되었습니다!")

                    # 요약 정보
                    st.subheader("📊 분석 요약")
                    col1, col2, col3, col4 = st.columns(4)

                    with col1:
                        st.metric("처리된 파일", currency_result.get('processed_files', 0))
                    with col2:
                        st.metric("총 사용자", currency_result.get('total_users', 0))
                    with col3:
                        st.metric("결제 사용자", currency_result.get('purchase_users_count', 0))
                    with col4:
                        before_count = currency_result.get('total_records_before', 0)
                        after_count = currency_result.get('total_records_after', 0)
                        st.metric("분석 레코드", f"{after_count:,}", delta=f"전체: {before_count:,}")

                    # 데이터 필터링 정보
                    if 'total_records_before' in currency_result and 'total_records_after' in currency_result:
                        st.info(f"""
                        **데이터 필터링 결과:**
                        - 전체 재화 로그: {currency_result['total_records_before']:,}개
                        - 사용자별 최신 데이터: {currency_result['total_records_after']:,}개
                        - 각 사용자의 마지막 날짜의 마지막 재화 정보만 집계
                        """)

                    # 전체 재화 총합
                    if 'summary' in currency_result and currency_result['summary']:
                        st.subheader("💰 전체 재화 총합")
                        summary_data = []
                        for item_id, totals in currency_result['summary'].items():
                            item_name = ''
                            if item_id == 101:
                                item_name = '유로 다이아'
                            elif item_id == 102:
                                item_name = '무료 다이아'
                            elif item_id == 10004:
                                item_name = '일반 소환권'
                            elif item_id == 10005:
                                item_name = '픽업 소환권'                            
                            summary_data.append({
                                "아이템": f"{item_name} (ID: {item_id})",
                                "총 보유량": f"{totals['total_current']:,}",
                                "총 획득량": f"{totals['total_acquired']:,}",
                                "총 사용량": f"{totals['total_used']:,}"
                            })

                        summary_df = pd.DataFrame(summary_data)
                        st.dataframe(summary_df, use_container_width=True)

                    # 소환권 보유량 상위 5명 유저 정보
                    if 'user_summary' in currency_result and not currency_result['user_summary'].empty:
                        st.subheader("🏆 소환권 보유량 상위 5명")
                        st.markdown("일반 소환권과 픽업 소환권의 보유량을 기준으로 상위 5명의 유저 정보를 표시합니다.")

                        from currency_analyzer import get_top_currency_holders

                        # 일반 소환권과 픽업 소환권에 대해 상위 5명 표시
                        col1, col2 = st.columns(2)

                        with col1:
                            st.markdown("**🎫 일반 소환권 (ID: 10004) 상위 5명**")
                            top_normal_tickets = get_top_currency_holders(currency_result['user_summary'], 10004, 5)
                            if not top_normal_tickets.empty:
                                st.dataframe(top_normal_tickets, use_container_width=True, hide_index=True)
                            else:
                                st.info("일반 소환권 데이터가 없습니다.")

                        with col2:
                            st.markdown("**🎟️ 픽업 소환권 (ID: 10005) 상위 5명**")
                            top_pickup_tickets = get_top_currency_holders(currency_result['user_summary'], 10005, 5)
                            if not top_pickup_tickets.empty:
                                st.dataframe(top_pickup_tickets, use_container_width=True, hide_index=True)
                            else:
                                st.info("픽업 소환권 데이터가 없습니다.")

                    # 결제 사용자 목록
                    if 'purchase_users' in currency_result and not currency_result['purchase_users'].empty:
                        st.subheader("💳 결제 사용자 목록")
                        purchase_df = currency_result['purchase_users'].copy()

                        # 컬럼명 변경
                        purchase_display = purchase_df.rename(columns={
                            'user_id': '사용자 ID',
                            'item_101_current': '유료 다이아 보유',
                            'item_101_acquired': '유료 다이아 획득',
                            'item_101_used': '유료 다이아 사용',
                            'item_101_last_time': '유료 다이아 마지막 시간',
                            'item_102_current': '무료 다이아 보유',
                            'item_102_acquired': '무료 다이아 획득',
                            'item_102_used': '무료 다이아 사용',
                            'item_102_last_time': '무료 다이아 마지막 시간',
                            'item_10004_current': '일반 소환권 보유',
                            'item_10004_acquired': '일반 소환권 획득',
                            'item_10004_used': '일반 소환권 사용',
                            'item_10004_last_time': '일반 소환권 마지막 시간',
                            'item_10005_current': '픽업 소환권 보유',
                            'item_10005_acquired': '픽업 소환권 획득',
                            'item_10005_used': '픽업 소환권 사용',
                            'item_10005_last_time': '픽업 소환권 마지막 시간'
                        })

                        # 시간 컬럼 포맷팅
                        for col in ['유료 다이아 마지막 시간', '무료 다이아 마지막 시간', '일반 소환권 마지막 시간', '픽업 소환권 마지막 시간']:
                            if col in purchase_display.columns:
                                purchase_display[col] = pd.to_datetime(purchase_display[col]).dt.strftime('%Y-%m-%d %H:%M:%S')

                        st.dataframe(purchase_display, use_container_width=True, height=400)

                        # CSV 다운로드 버튼
                        csv = purchase_display.to_csv(index=False, encoding='utf-8-sig')
                        st.download_button(
                            label="📥 결제 사용자 목록 CSV 다운로드",
                            data=csv,
                            file_name=f"purchase_users_{selected_project_key}.csv",
                            mime="text/csv"
                        )

                    # 이상 데이터 감지
                    col1, col2 = st.columns(2)

                    with col1:
                        if currency_result.get('negative_count', 0) > 0:
                            st.subheader("⚠️ 음수 값 감지")
                            st.error(f"{currency_result['negative_count']}개의 음수 재화 값이 감지되었습니다.")
                            if not currency_result['negative_records'].empty:
                                negative_display = currency_result['negative_records'][['user_id', 'item_id', 'current_amount']].rename(columns={
                                    'user_id': '사용자 ID',
                                    'item_id': '아이템 ID',
                                    'current_amount': '보유량'
                                })
                                st.dataframe(negative_display, height=200)

                    with col2:
                        if currency_result.get('large_value_count', 0) > 0:
                            st.subheader("🚨 대용량 값 감지")
                            st.warning(f"{currency_result['large_value_count']}개의 대용량 재화 값(100만 이상)이 감지되었습니다.")
                            if not currency_result['large_value_records'].empty:
                                large_display = currency_result['large_value_records'][['user_id', 'item_id', 'current_amount']].rename(columns={
                                    'user_id': '사용자 ID',
                                    'item_id': '아이템 ID',
                                    'current_amount': '보유량'
                                })
                                st.dataframe(large_display, height=200)

    # 사용자별 재화 분석 섹션
    st.markdown("---")
    st.subheader("👤 사용자별 재화 분석")
    st.markdown("특정 사용자의 날짜별 재화 변화를 상세 분석합니다.")

    # 사용자 ID 입력
    col1, col2 = st.columns([2, 1])

    with col1:
        target_user_id = st.text_input(
            "분석할 사용자 ID 입력",
            placeholder="예: 031bb250-2be4-11f0-9f49-9580f8393a5e",
            help="분석하고 싶은 사용자의 ID를 입력하세요"
        )

    with col2:
        st.markdown("<br>", unsafe_allow_html=True)  # 버튼 위치 맞추기
        analyze_user_btn = st.button("🔍 사용자 분석", type="secondary")

    if analyze_user_btn and target_user_id.strip():
        with st.spinner(f"사용자 {target_user_id}의 재화 데이터를 분석하고 있습니다..."):
            from currency_analyzer import analyze_user_currency_history

            # 현재 프로젝트와 환경의 S3 파일 목록 가져오기
            current_project = AVAILABLE_PROJECTS[selected_project_key]
            s3_files = get_s3_files_cached(current_project['bucket'], selected_environment)

            if not s3_files:
                st.error("S3에서 로그 파일을 찾을 수 없습니다.")
            else:
                # 사용자별 재화 분석 실행
                user_result = analyze_user_currency_history(s3_files, LOCAL_DOWNLOAD_DIR, target_user_id.strip())

                if 'error' in user_result:
                    st.error(f"❌ 분석 실패: {user_result['error']}")
                    if 'processed_files' in user_result:
                        st.info(f"처리된 파일: {user_result['processed_files']}개")
                else:
                    # 분석 결과 표시
                    st.success(f"✅ 사용자 {target_user_id}의 재화 분석이 완료되었습니다!")

                    # 요약 정보
                    col1, col2, col3 = st.columns(3)
                    with col1:
                        st.metric("처리된 파일", user_result.get('processed_files', 0))
                    with col2:
                        st.metric("총 레코드", user_result.get('total_records', 0))
                    with col3:
                        daily_analysis = user_result.get('daily_analysis', {})
                        st.metric("분석 일수", daily_analysis.get('total_days', 0))

                    if 'daily_analysis' in user_result and 'daily_last_logs' in user_result['daily_analysis']:
                        daily_logs = user_result['daily_analysis']['daily_last_logs']
                        daily_changes = user_result['daily_analysis']['daily_changes']

                        # 1. 날짜별 마지막 재화 로그
                        st.subheader("📅 날짜별 마지막 재화 로그")

                        if daily_logs:
                            # 테이블 형태로 표시
                            log_data = []
                            for day_log in daily_logs:
                                date_str = day_log['date'].strftime('%Y-%m-%d')

                                # 각 아이템별 데이터 정리
                                row_data = {'날짜': date_str}

                                for log in day_log['logs']:
                                    item_name = log['item_name']
                                    row_data[f'{item_name} 보유'] = f"{log['current_amount']:,}"
                                    row_data[f'{item_name} 획득'] = f"{log['acquired_amount']:,}"
                                    row_data[f'{item_name} 사용'] = f"{log['used_amount']:,}"
                                    row_data[f'{item_name} 시간'] = log['time'].strftime('%H:%M:%S')

                                log_data.append(row_data)

                            log_df = pd.DataFrame(log_data)
                            st.dataframe(log_df, use_container_width=True, height=400)

                            # CSV 다운로드
                            csv = log_df.to_csv(index=False, encoding='utf-8-sig')
                            st.download_button(
                                label="📥 날짜별 로그 CSV 다운로드",
                                data=csv,
                                file_name=f"user_{target_user_id}_daily_logs.csv",
                                mime="text/csv"
                            )
                        else:
                            st.warning("날짜별 재화 로그를 찾을 수 없습니다.")

                        # 2. 날짜 간 재화 변화량 비교
                        st.subheader("📊 날짜 간 재화 변화량")

                        if daily_changes:
                            # 변화량 테이블
                            change_data = []
                            for change in daily_changes:
                                from_date = change['from_date'].strftime('%Y-%m-%d')
                                to_date = change['to_date'].strftime('%Y-%m-%d')

                                row_data = {'기간': f"{from_date} → {to_date}"}

                                for change_item in change['changes']:
                                    item_name = change_item['item_name']

                                    # 변화량 (+ 증가, - 감소)
                                    current_change = change_item['current_change']
                                    acquired_change = change_item['acquired_change']
                                    used_change = change_item['used_change']

                                    row_data[f'{item_name} 보유 변화'] = f"{current_change:+,}"
                                    row_data[f'{item_name} 획득 변화'] = f"{acquired_change:+,}"
                                    row_data[f'{item_name} 사용 변화'] = f"{used_change:+,}"

                                    # 이전 → 현재 값
                                    row_data[f'{item_name} 보유 상세'] = f"{change_item['prev_current']:,} → {change_item['curr_current']:,}"

                                change_data.append(row_data)

                            change_df = pd.DataFrame(change_data)
                            st.dataframe(change_df, use_container_width=True, height=400)

                            # CSV 다운로드
                            csv = change_df.to_csv(index=False, encoding='utf-8-sig')
                            st.download_button(
                                label="📥 변화량 분석 CSV 다운로드",
                                data=csv,
                                file_name=f"user_{target_user_id}_changes.csv",
                                mime="text/csv"
                            )

                            # 변화량 시각화
                            if len(daily_changes) > 1:
                                st.subheader("📈 재화 변화 시각화")

                                # 날짜별 보유량 변화 차트
                                chart_data = []
                                for day_log in daily_logs:
                                    date_str = day_log['date'].strftime('%Y-%m-%d')
                                    row = {'날짜': date_str}

                                    for log in day_log['logs']:
                                        item_name = log['item_name']
                                        row[item_name] = log['current_amount']

                                    chart_data.append(row)

                                chart_df = pd.DataFrame(chart_data)
                                if not chart_df.empty:
                                    chart_df = chart_df.set_index('날짜')
                                    st.line_chart(chart_df)
                        else:
                            st.info("비교할 날짜가 충분하지 않습니다. (최소 2일 이상의 데이터가 필요합니다)")

    elif analyze_user_btn and not target_user_id.strip():
        st.warning("⚠️ 분석할 사용자 ID를 입력해주세요.")

# 탭 6: 유료 구매 시점
with selected_tab[5]:
    st.header("💰 유료 구매 시점 분석")
    st.markdown("**IAP 구매 로그**를 기준으로 구매 전 **2분간의 사용자 동선**을 분석합니다.")

    # 분석 설정
    col1, col2 = st.columns([2, 1])
    with col1:
        st.info("� **분석 기준**: payload_a_param에 'IAP'가 포함된 로그를 구매로 판단")
    with col2:
        file_limit_option = st.selectbox(
            "파일 수 제한",
            options=["제한 없음 (전체)", "50개", "100개", "500개", "1000개", "사용자 정의"],
            index=0,  # 기본값: "제한 없음 (전체)" (0번 인덱스)
            help="분석할 파일 수를 선택하세요. 제한 없음 선택시 모든 파일을 분석합니다."
        )

        if file_limit_option == "제한 없음 (전체)":
            max_files = None
            st.info("🚀 **전체 파일 분석**: 모든 로그 파일을 분석합니다 (시간이 오래 걸릴 수 있음)")
        elif file_limit_option == "사용자 정의":
            max_files = st.number_input("파일 수 입력", min_value=1, max_value=100000, value=50)
        else:
            max_files = int(file_limit_option.replace("개", ""))

    # 분석 시작 버튼
    if st.button("🛒 구매 시점 분석 시작", type="primary"):
        # 현재 프로젝트와 환경의 S3 파일 목록 가져오기
        current_project = AVAILABLE_PROJECTS[selected_project_key]
        s3_files = get_s3_files_cached(current_project['bucket'], selected_environment)

        if not s3_files:
            st.error("S3에서 로그 파일을 찾을 수 없습니다.")
        else:
            # 분석할 파일 수 표시
            total_files = len(s3_files)
            if max_files is None:
                analyze_files = total_files
                st.info(f"📊 **분석 대상**: 총 {total_files:,}개 파일 **전체** 분석 (제한 없음)")
            else:
                analyze_files = min(max_files, total_files)
                st.info(f"📊 **분석 대상**: 총 {total_files:,}개 파일 중 {analyze_files:,}개 파일 분석")

            # 진행 상황 표시를 위한 플레이스홀더
            progress_placeholder = st.empty()
            status_placeholder = st.empty()

            # 분석 시작 전 상태 표시
            status_placeholder.info("🔄 분석을 시작합니다... (캐시 확인 중)")

            from purchase_analyzer import analyze_purchase_patterns_from_s3_files, get_cache_key, load_analysis_cache, LogCapture

            # 로그 캡처 시작
            with LogCapture() as log_capture:
                # 캐시 확인
                cache_key = get_cache_key(s3_files, max_files)
                cached_result = load_analysis_cache(cache_key)

                if cached_result:
                    status_placeholder.success("🚀 캐시된 분석 결과를 사용합니다!")
                    purchase_result = cached_result
                else:
                    # 구매 패턴 분석 실행
                    with st.spinner(f"구매 패턴을 분석하고 있습니다... (병렬 처리: 최대 8개 파일 동시 처리)"):
                        purchase_result = analyze_purchase_patterns_from_s3_files(s3_files, LOCAL_DOWNLOAD_DIR, max_files=max_files)

                    # 분석 완료 후 상태 업데이트
                    status_placeholder.success("✅ 분석이 완료되었습니다!")

                # 로그를 파일로 저장
                log_file_path = log_capture.save_to_file("purchase_analysis_streamlit")

            if purchase_result and 'error' not in purchase_result:
                    # 분석 결과 표시
                    st.success("✅ 구매 시점 분석이 완료되었습니다!")

                    # 요약 정보
                    summary = purchase_result['summary']
                    file_processing = purchase_result.get('file_processing', {})

                    col1, col2, col3, col4 = st.columns(4)
                    with col1:
                        st.metric("총 구매 건수", f"{summary['total_purchases']:,}")
                    with col2:
                        st.metric("구매 사용자 수", f"{summary['unique_users']:,}")
                    with col3:
                        st.metric("분석 파일 수", f"{summary['total_files_processed']:,}")
                    with col4:
                        processed_ratio = (summary['total_files_processed'] / file_processing['total_files'] * 100) if file_processing['total_files'] > 0 else 0
                        st.metric("처리 비율", f"{processed_ratio:.1f}%")

                    # iapProductID별 구매 패턴
                    if purchase_result['overall_patterns']:
                        st.subheader("📈 iapProductID별 구매 전 동선 패턴")

                        # iapProductID별 구매 패턴 요약 테이블 생성
                        from purchase_analyzer import create_purchase_pattern_summary
                        summary_df = create_purchase_pattern_summary(purchase_result)
                        if not summary_df.empty:
                            st.dataframe(summary_df, use_container_width=True, height=400)
                        else:
                            st.info("iapProductID별 구매 패턴 데이터가 없습니다.")

                    # 가장 많이 구매된 상품 (iapProductID)
                    if summary['most_common_iap_products']:
                        st.subheader("🛍️ 가장 많이 구매된 상품 (iapProductID)")

                        iap_product_data = []
                        for i, (iap_product_id, count) in enumerate(summary['most_common_iap_products'], 1):
                            iap_product_data.append({
                                "순위": i,
                                "상품 ID (iapProductID)": iap_product_id,
                                "구매 횟수": count,
                                "비율": f"{count/summary['total_purchases']*100:.1f}%" if summary['total_purchases'] > 0 else "0%"
                            })

                        iap_product_df = pd.DataFrame(iap_product_data)
                        st.dataframe(iap_product_df, use_container_width=True, height=200)



                    # 시간대별/요일별 구매 패턴 시각화
                    if 'hourly_patterns' in purchase_result and 'weekday_patterns' in purchase_result:
                        st.subheader("📊 구매 시간 패턴 분석")

                        col1, col2 = st.columns(2)

                        # 시간대별 구매 패턴
                        with col1:
                            st.markdown("**⏰ 시간대별 구매 패턴**")
                            hourly_data = purchase_result['hourly_patterns']
                            if hourly_data:
                                # 0-23시간을 모두 포함하도록 데이터 준비
                                hours = list(range(24))
                                counts = [hourly_data.get(hour, 0) for hour in hours]

                                hourly_df = pd.DataFrame({
                                    '시간': [f"{h:02d}시" for h in hours],
                                    '구매 건수': counts
                                })

                                st.bar_chart(hourly_df.set_index('시간'), height=300)
                            else:
                                st.info("시간대별 데이터가 없습니다.")

                        # 요일별 구매 패턴
                        with col2:
                            st.markdown("**📅 요일별 구매 패턴**")
                            weekday_data = purchase_result['weekday_patterns']
                            if weekday_data:
                                weekdays = ['월', '화', '수', '목', '금', '토', '일']
                                counts = [weekday_data.get(day, 0) for day in weekdays]

                                weekday_df = pd.DataFrame({
                                    '요일': weekdays,
                                    '구매 건수': counts
                                })

                                st.bar_chart(weekday_df.set_index('요일'), height=300)
                            else:
                                st.info("요일별 데이터가 없습니다.")





            else:
                # 오류 처리 및 디버깅 정보
                if 'error' in purchase_result:
                    st.error(f"❌ 분석 중 오류가 발생했습니다: {purchase_result['error']}")
                else:
                    st.warning("분석 결과가 없습니다.")



# 탭 7: 설정
with selected_tab[6]:
    st.header("🔧 설정")
    st.markdown("애플리케이션 설정을 관리합니다.")
    
    st.subheader("📁 로컬 저장소 설정")
    st.text_input("다운로드 디렉토리", value=LOCAL_DOWNLOAD_DIR, disabled=True)
    
    st.subheader("🔑 AWS 설정")
    st.info("AWS 자격 증명은 환경 변수 또는 AWS 자격 증명 파일에서 읽어옵니다.")
    
    # 캐시 관리
    st.subheader("🗂️ 캐시 관리")

    # 다운로드 캐시 정보 표시
    from s3_utils import get_cache_info, clear_cache_directory
    cache_info = get_cache_info(LOCAL_DOWNLOAD_DIR)

    if cache_info['exists']:
        col1, col2, col3 = st.columns(3)
        with col1:
            st.metric("캐시된 파일 수", f"{cache_info['total_files']:,}")
        with col2:
            st.metric("캐시 크기", f"{cache_info['total_size_mb']:,.1f} MB")
        with col3:
            st.metric("하위 디렉토리", len(cache_info['subdirs']))

        if cache_info['subdirs']:
            with st.expander("📂 캐시 디렉토리 구조"):
                for subdir in cache_info['subdirs']:
                    st.text(f"📁 {subdir}")
    else:
        st.info("아직 캐시된 파일이 없습니다.")

    # 캐시 관리 버튼들
    col1, col2 = st.columns(2)
    with col1:
        if st.button("🧹 Streamlit 캐시 지우기"):
            st.cache_data.clear()
            st.success("Streamlit 캐시가 지워졌습니다!")

    with col2:
        if st.button("🗑️ 다운로드 캐시 지우기", type="secondary"):
            if clear_cache_directory(LOCAL_DOWNLOAD_DIR):
                st.success("다운로드 캐시가 지워졌습니다!")
                st.rerun()  # 페이지 새로고침으로 캐시 정보 업데이트
            else:
                st.error("캐시 지우기에 실패했습니다.")
