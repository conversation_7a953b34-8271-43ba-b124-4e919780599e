# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: ActionLog_PROTO.proto
# Protobuf Python Version: 4.25.6
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


import ActionLogEnumsFull_pb2 as ActionLogEnumsFull__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x15\x41\x63tionLog_PROTO.proto\x12\x0fgoogle.protobuf\x1a\x18\x41\x63tionLogEnumsFull.proto\"Z\n\x17MafActionLogQueue_PROTO\x12\x0c\n\x04oder\x18\x01 \x01(\x05\x12\x31\n\x04logs\x18\x02 \x03(\x0b\x32#.google.protobuf.MafActionLog_PROTO\"\x88\x02\n\x12MafActionLog_PROTO\x12@\n\tEventType\x18\x01 \x01(\x0e\x32-.google.protobuf.MAF_ANALYTICS_LOG_EVENT_TYPE\x12\x0e\n\x06Source\x18\x02 \x01(\t\x12\x41\n\x07Payload\x18\x03 \x03(\x0b\x32\x30.google.protobuf.MafActionLog_PROTO.PayloadEntry\x1a]\n\x0cPayloadEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12<\n\x05value\x18\x02 \x01(\x0b\x32-.google.protobuf.MafActionLogPayloadDTO_PROTO:\x02\x38\x01\"\xfe\x01\n\x1cMafActionLogPayloadDTO_PROTO\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\x41\n\x0bpayloadType\x18\x02 \x01(\x0e\x32,.google.protobuf.MAF_ACTION_LOG_PAYLOAD_TYPE\x12\x43\n\x0bscalarValue\x18\x03 \x01(\x0b\x32..google.protobuf.MafActionLogScalarValue_PROTO\x12I\n\x0ereferenceValue\x18\x04 \x01(\x0b\x32\x31.google.protobuf.MafActionLogReferenceValue_PROTO\"m\n\x1dMafActionLogScalarValue_PROTO\x12\x10\n\x08intValue\x18\x01 \x01(\x05\x12\x12\n\nulongValue\x18\x02 \x01(\x04\x12\x11\n\tboolValue\x18\x03 \x01(\x08\x12\x13\n\x0bstringValue\x18\x04 \x01(\t\"3\n MafActionLogReferenceValue_PROTO\x12\x0f\n\x07intList\x18\x01 \x03(\x05\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'ActionLog_PROTO_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  DESCRIPTOR._options = None
  _globals['_MAFACTIONLOG_PROTO_PAYLOADENTRY']._options = None
  _globals['_MAFACTIONLOG_PROTO_PAYLOADENTRY']._serialized_options = b'8\001'
  _globals['_MAFACTIONLOGQUEUE_PROTO']._serialized_start=68
  _globals['_MAFACTIONLOGQUEUE_PROTO']._serialized_end=158
  _globals['_MAFACTIONLOG_PROTO']._serialized_start=161
  _globals['_MAFACTIONLOG_PROTO']._serialized_end=425
  _globals['_MAFACTIONLOG_PROTO_PAYLOADENTRY']._serialized_start=332
  _globals['_MAFACTIONLOG_PROTO_PAYLOADENTRY']._serialized_end=425
  _globals['_MAFACTIONLOGPAYLOADDTO_PROTO']._serialized_start=428
  _globals['_MAFACTIONLOGPAYLOADDTO_PROTO']._serialized_end=682
  _globals['_MAFACTIONLOGSCALARVALUE_PROTO']._serialized_start=684
  _globals['_MAFACTIONLOGSCALARVALUE_PROTO']._serialized_end=793
  _globals['_MAFACTIONLOGREFERENCEVALUE_PROTO']._serialized_start=795
  _globals['_MAFACTIONLOGREFERENCEVALUE_PROTO']._serialized_end=846
# @@protoc_insertion_point(module_scope)
